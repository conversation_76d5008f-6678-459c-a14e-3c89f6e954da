<?xml version="1.0" encoding="UTF-8"?>
<project name="local-kiwoom-indonesia-home" default="build_project" basedir=".">

    <!-- Define project properties -->
    <property name="src.dir" value="${basedir}/src/main/java"/>
    <property name="resource.dir" value="${basedir}/src/main/resources"/>
    <property name="web.dir" value="${basedir}/WebContent"/>
    <property name="lib.dir" value="${web.dir}/WEB-INF/lib"/>
    <property name="build.dir" value="${basedir}/build"/>
    <property name="classes.dir" value="${build.dir}/classes"/>

    <!-- Define classpath for compilation -->
    <path id="compile.classpath">
        <fileset dir="${lib.dir}">
            <include name="*.jar"/>
        </fileset>
    </path>
    
    <target name="information">
        <echo>* basedir   : ${basedir}</echo>
        <echo>* src.dir   : ${src.dir}</echo>
        <echo>* resource.dir   : ${resource.dir}</echo>
        <echo>* web.dir   : ${web.dir}</echo>
        <echo>* lib.dir   : ${lib.dir}</echo>
        <echo>* build.dir   : ${build.dir}</echo>
        <echo>* classes.dir : ${classes.dir}</echo>
        <echo>* copy classes.dir : ${build.dir}/WEB-INF/classes</echo>
    </target>
    
    <!-- Define target for cleaning up build directory -->
    <target name="clean">
        <delete dir="${build.dir}"/>
    </target>

    <!-- Define target for compiling Java source code -->
    <target name="compile">
        <mkdir dir="${classes.dir}"/>
        <javac srcdir="${src.dir}" 
               destdir="${classes.dir}" 
               debug="true" 
               encoding="UTF-8" 
               includeantruntime="false">
            <classpath refid="compile.classpath"/>
        </javac>
        <copy todir="${classes.dir}" overwrite="true">
            <fileset dir="${resource.dir}"/>
        </copy>
    </target>
    
    <!-- Copy classes and WEB-INF -->
    <target name="copy-files">
        <copy todir="${build.dir}" overwrite="true">
            <fileset dir="${web.dir}">
                <exclude name="WEB-INF/classes/**"/>
            </fileset>
        </copy>
        <mkdir dir="${build.dir}/WEB-INF/classes"/>
        <copy todir="${build.dir}/WEB-INF/classes" overwrite="true">
            <fileset dir="${classes.dir}"/>
        </copy>
        <delete dir="${classes.dir}"/>
    </target>
    
    <!-- Create necessary directories and files if they don't exist -->
    <target name="create-missing-files">
        <mkdir dir="${build.dir}/WEB-INF/classes"/>
        <mkdir dir="${build.dir}/WEB-INF/conf/properties"/>
        
        <!-- Check if application-local.properties exists in source, if so copy it -->
        <condition property="app.local.exists">
            <available file="${resource.dir}/application-local.properties"/>
        </condition>
        <antcall target="copy-app-local-if-exists"/>
        
        <!-- If not in source, check if it exists in WebContent -->
        <condition property="app.local.web.exists">
            <available file="${web.dir}/WEB-INF/classes/application-local.properties"/>
        </condition>
        <antcall target="copy-app-local-web-if-exists"/>
        
        <!-- If still not found, create a default one -->
        <condition property="app.local.missing">
            <not>
                <available file="${build.dir}/WEB-INF/classes/application-local.properties"/>
            </not>
        </condition>
        <antcall target="create-default-app-local"/>
        
        <!-- Similar checks for tcpc.properties.local -->
        <condition property="tcpc.local.exists">
            <available file="${web.dir}/WEB-INF/conf/properties/tcpc.properties.local"/>
        </condition>
        <antcall target="copy-tcpc-local-if-exists"/>
        
        <!-- If not found, create a default one -->
        <condition property="tcpc.local.missing">
            <not>
                <available file="${build.dir}/WEB-INF/conf/properties/tcpc.properties.local"/>
            </not>
        </condition>
        <antcall target="create-default-tcpc-local"/>
    </target>
    
    <target name="copy-app-local-if-exists" if="app.local.exists">
        <copy file="${resource.dir}/application-local.properties" 
              tofile="${build.dir}/WEB-INF/classes/application-local.properties" 
              overwrite="true"/>
    </target>
    
    <target name="copy-app-local-web-if-exists" if="app.local.web.exists">
        <copy file="${web.dir}/WEB-INF/classes/application-local.properties" 
              tofile="${build.dir}/WEB-INF/classes/application-local.properties" 
              overwrite="true"/>
    </target>
    
    <target name="create-default-app-local" if="app.local.missing">
        <echo file="${build.dir}/WEB-INF/classes/application-local.properties">#JMX
spring.jmx.enabled=true
###################################################################################################

# JNDI
jdbc.jndi=false
jdbc.jndi.name=kwibst1

# JDBC
jdbc.driver-class-name=net.sf.log4jdbc.DriverSpy
jdbc.url=*******************************************************
jdbc.username=kiwoom
jdbc.password=7/ofl3ZIYK7hhX9LDwFJY6E4dSSd+Emf
jdbc.initial-size=3
jdbc.max-total=50
jdbc.max-idle=50
jdbc.max-wait-millis=10000
jdbc.validation-query=SELECT 1 FROM DUAL

###################################################################################################

# FILE UPLOAD
file.upload.dir=${user.home}/WWW2/datafiles/upload/

# BANNER JSON PATH
file.upload.banner-json-save-dir=${file.upload.dir}banner/json/

# HOMEPAGE TOP BANNER JSON FILENAME
home.top.banner.json.filename=main_banner01.json

# HTS FRONTPAGE TOP BANNER JSON FILENAME
hts.top.banner.json.filename=frontpage_banner01.json

# HTS FRONTPAGE TOP BOTTOM JSON FILENAME
hts.bottom.banner.json.filename=frontpage_banner02.json

# POPUP JSON PATH
file.upload.popup-json-save-dir=${file.upload.dir}popup/json/

# HOMEPAGE POPUP JSON FILENAME
homepage.popup.json.filename=homepage_popup_info.json

# HTS POPUP JSON FILENAME
hts.popup.json.filename=hts_popup_info.json

# HTS POPUP PAGE URL
hts.popup.page.url=/hts/popup/getPopupMain

###################################################################################################

# MENU
menu.file.base=${basedir}/src/main/resources/
menu.file.name=menu.xml

###################################################################################################

# Secret-key
auth.secret-key=gweBvuXHfhpiwXmnt3eXpzCxEmBr5eFr

# AUTH
auth.login.success-url=/
auth.login.entry-url=/auth/loginView 

# Prometheus Exporter
prometheus.server.instance01.port=9094
prometheus.server.instance02.port=9094

# Email Send API URL
email.send.server.url=http://202.4.160.98:8092/api/sendemail</echo>
    </target>
    
    <target name="copy-tcpc-local-if-exists" if="tcpc.local.exists">
        <copy file="${web.dir}/WEB-INF/conf/properties/tcpc.properties.local" 
              tofile="${build.dir}/WEB-INF/conf/properties/tcpc.properties.local" 
              overwrite="true"/>
    </target>
    
    <target name="create-default-tcpc-local" if="tcpc.local.missing">
        <echo file="${build.dir}/WEB-INF/conf/properties/tcpc.properties.local">tcpc.xml_axis = AXIS_Config.xml
tcpc.xml_tux = TUX_Config.xml
tcpc.xml_axisfid = FID_Config.xml</echo>
    </target>
    
    <!-- Define active profile -->
    <target name="profile" depends="create-missing-files">
        <copy file="${build.dir}/WEB-INF/classes/application-local.properties" 
              tofile="${build.dir}/WEB-INF/classes/application.properties" 
              overwrite="true"/>
        <copy file="${build.dir}/WEB-INF/conf/properties/tcpc.properties.local" 
              tofile="${build.dir}/WEB-INF/conf/properties/tcpc.properties" 
              overwrite="true"/>
    </target>
    
    <target name="build_project" depends="information, clean, compile, copy-files, profile" />
</project>
