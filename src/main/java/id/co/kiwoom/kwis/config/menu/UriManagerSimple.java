package id.co.kiwoom.kwis.config.menu;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Simplified URI Manager to avoid compilation issues
 * This is a temporary solution until library dependencies are properly configured
 */
public class UriManagerSimple {

    private static UriManagerSimple instance;
    private Map<String,String> menus = new HashMap<>();

    private UriManagerSimple(){
        // Initialize with basic menu structure
        initializeBasicMenus();
    }

    /**
     * Initialize UriManager instance
     */
    public static UriManagerSimple initInstance() throws Exception{
        synchronized(UriManagerSimple.class){
            if(instance==null){
                instance = new UriManagerSimple();
            }
            return instance;
        }
    }

    /**
     * Get UriManager instance
     */
    public static UriManagerSimple getInstance() throws Exception{
        synchronized(UriManagerSimple.class){
            if(instance==null)
                throw new Exception("UriManager not initialized.");
            return instance;
        }
    }

    /**
     * Initialize basic menu structure
     */
    private void initializeBasicMenus() {
        menus.put("0", "var $menusObject = [];");
    }

    /**
     * Get current URI tree (simplified version)
     */
    public String getCurrentUriTree(HttpServletRequest request){
        return getCurrentUriTree(getRequestUri(request));
    }

    /**
     * Get current URI tree by URI string
     */
    public String getCurrentUriTree(String uri){
        // Return empty array for now
        return "[]";
    }

    /**
     * Get URL info (simplified version)
     */
    public UriVo getUrlInfo(HttpServletRequest request) throws Exception{
        String uri = getRequestUri(request);
        return getUrlInfo(uri);
    }

    /**
     * Get URL info by URI string
     */
    public UriVo getUrlInfo(String uri) throws Exception{
        // Create a basic UriVo for common paths
        if("/main".equals(uri) || "/".equals(uri)) {
            UriVo info = new UriVo();
            info.setLvl("0");
            info.setUcd("00000000");
            info.setUrl(uri);
            info.setName("Main");
            info.setUucd("00000000");
            info.setExps("N");
            info.setMulti("Y");
            return info;
        }
        return null;
    }

    /**
     * Get current URI (simplified version)
     */
    public String getCurrentUri(HttpServletRequest request){
        String uri = getRequestUri(request);
        return getCurrentUri(uri);
    }

    /**
     * Get current URI by URI string
     */
    public String getCurrentUri(String uri){
        // Return basic structure for main page
        if("/main".equals(uri) || "/".equals(uri)) {
            return "[{\"lvl\":\"0\",\"ucd\":\"00000000\",\"url\":\"" + uri + "\",\"name\":\"Main\",\"exps\":\"N\",\"multi\":\"Y\"}]";
        }
        return "[]";
    }

    /**
     * Get menus JavaScript object
     */
    public String getMenus(String key) {
        return menus.getOrDefault(key, "var $menusObject = [];");
    }

    /**
     * Extract request URI (simplified version)
     */
    private String getRequestUri(HttpServletRequest request) {
        if(request == null) return "/";
        
        String uri = request.getRequestURI();
        if(uri == null || uri.isEmpty()) return "/";
        
        // Remove context path if present
        String contextPath = request.getContextPath();
        if(contextPath != null && !contextPath.isEmpty() && uri.startsWith(contextPath)) {
            uri = uri.substring(contextPath.length());
        }
        
        // Remove query parameters
        if(uri.indexOf("?") >= 0) {
            uri = uri.substring(0, uri.indexOf("?"));
        }
        
        // Remove file extensions
        if(uri.indexOf(".") >= 0) {
            uri = uri.substring(0, uri.indexOf("."));
        }
        
        if(uri.isEmpty()) uri = "/";
        
        return uri;
    }
}
