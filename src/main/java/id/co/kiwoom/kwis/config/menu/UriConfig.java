package id.co.kiwoom.kwis.config.menu;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

/**
 * UriConfig - Creating a UriManager
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020-09-10 13:53:16
 *
 * @see UriManager
 */

@Configuration
public class UriConfig {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
	public UriConfig() {
		try{
			//initialize uri manager
			UriManager.initInstance();	//read form xml file
			logger.info("UriManager initialized successfully");
		}catch(IOException e){
			logger.error("IOException during UriManager initialization: " + e.getMessage());
		}catch(Exception e){
			logger.error("Exception during UriManager initialization: " + e.getMessage());
		}
	}
}
