package id.co.kiwoom.kwis.config.menu;

import java.io.IOException;

/**
 * UriConfig - Creating a UriManager
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020-09-10 13:53:16
 *
 * @see UriManager
 */

public class UriConfig {
	public UriConfig() {
		try{
			//initialize uri manager - using simplified version to avoid compilation issues
			UriManagerSimple.initInstance();
			System.out.println("UriManager initialized successfully");
		}catch(IOException e){
			System.err.println("IOException during UriManager initialization: " + e.getMessage());
		}catch(Exception e){
			System.err.println("Exception during UriManager initialization: " + e.getMessage());
		}
	}
}
