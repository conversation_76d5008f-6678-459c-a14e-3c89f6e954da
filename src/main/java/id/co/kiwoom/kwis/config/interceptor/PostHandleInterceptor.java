package id.co.kiwoom.kwis.config.interceptor;

import id.co.kiwoom.kwis.config.menu.UriManager;
import id.co.kiwoom.kwis.config.menu.UriVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class PostHandleInterceptor extends HandlerInterceptorAdapter {

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
		if(modelAndView==null)
			return;

		String viewName	= modelAndView.getViewName();
		if(viewName!=null && !"".equals(viewName)){
			if(!viewName.startsWith("redirect:")) {
				try {
					String currentMenus = UriManager.getInstance().getCurrentUriTree(request);
					if(StringUtils.isNotEmpty(currentMenus))
						modelAndView.addObject("currentMenus", currentMenus); // current menu tree information

					String currentMenu = UriManager.getInstance().getCurrentUri(request);
					if(StringUtils.isNotEmpty(currentMenu))
						modelAndView.addObject("currentMenu", currentMenu); // current menu information

					String isMulti = "";
					UriVo uriVo = UriManager.getInstance().getUrlInfo(request);
					if(uriVo != null) {
						isMulti = uriVo.getMulti();
					}

					modelAndView.addObject("isMulti", isMulti); // whether Multi Language support is available.
				} catch (Exception e) {
					// Log the error but don't fail the request
					System.err.println("Error in PostHandleInterceptor: " + e.getMessage());
					// Set default values
					modelAndView.addObject("currentMenus", "[]");
					modelAndView.addObject("currentMenu", "[]");
					modelAndView.addObject("isMulti", "N");
				}
				modelAndView.addObject("nowDate", Long.toString(System.currentTimeMillis()));	// variables for static file index processing
			}
		}
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {}
}
