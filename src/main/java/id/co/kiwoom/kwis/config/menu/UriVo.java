package id.co.kiwoom.kwis.config.menu;

import java.io.Serializable;

public class UriVo implements Serializable, Cloneable{
	private static final long serialVersionUID = 1L;

	private String lvl;
	private String ucd;
	private String url;
	private String name;
	private String uucd;
	private String exps;
	private String multi;

	// Getters
	public String getLvl() { return lvl; }
	public String getUcd() { return ucd; }
	public String getUrl() { return url; }
	public String getName() { return name; }
	public String getUucd() { return uucd; }
	public String getExps() { return exps; }
	public String getMulti() { return multi; }

	// Setters
	public void setLvl(String lvl) { this.lvl = lvl; }
	public void setUcd(String ucd) { this.ucd = ucd; }
	public void setUrl(String url) { this.url = url; }
	public void setName(String name) { this.name = name; }
	public void setUucd(String uucd) { this.uucd = uucd; }
	public void setExps(String exps) { this.exps = exps; }
	public void setMulti(String multi) { this.multi = multi; }
}
