package id.co.kiwoom.kwis.common.exception;

import id.co.kiwoom.kwis.common.vo.ErrorResponse;

import org.apache.ibatis.exceptions.PersistenceException;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import com.kiwoom.common.utils.StringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeoutException;

@ControllerAdvice
public class GlobalExceptionHandler {

	@ExceptionHandler(Exception.class)
	protected Object handleException(Throwable e, HttpServletRequest request, HttpServletResponse response) {

		ErrorResponse errorResponse = this.getErrorResponse(e, request, response);

		String contentType = StringUtil.null2str(request.getContentType(), "");
		if((contentType.contains("application/json"))) {	// for json
			return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
		} else {
			response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			request.setAttribute("errorMessage", errorResponse.getMessage());
			return "frame:" + request.getContextPath() + "/common/error/error";
		}

	}

	private ErrorResponse getErrorResponse(Throwable e, HttpServletRequest request, HttpServletResponse response) {

		final ErrorResponse errorResponse = new ErrorResponse();

		if (e instanceof NullPointerException) {
			log.error("handle NullPointerException", e);
			errorResponse.setMessage("This is a data processing error. Please contact your system administrator.");
			errorResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			errorResponse.setCode("NullPointerError");
		} else if (e instanceof ArrayIndexOutOfBoundsException) {
			log.error("handle ArrayIndexOutOfBoundsException", e);
			errorResponse.setMessage("This is a data processing error. Please contact your system administrator.");
			errorResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			errorResponse.setCode("ArrayIndexOutOfBoundsError");
		} else if (e instanceof PersistenceException) {
			log.error("handle PersistenceException", e);
			errorResponse.setMessage("This is a data connection related error. Please contact your system administrator.");
			errorResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			errorResponse.setCode("DBError");
		} else if (e instanceof DataAccessException) {
			log.error("handle DataAccessException", e);
			errorResponse.setMessage("This is a data access related error. Please contact your system administrator.");
			errorResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			errorResponse.setCode("DBError");
		} else if (e instanceof TimeoutException) {
			log.error("handle TimeoutException", e);
			errorResponse.setMessage(e.getMessage());
			errorResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			errorResponse.setCode("TimeoutError");
		} else if (e instanceof BusinessException) {
			log.error("handle BusinessException message:" + e.getMessage());
			if (((BusinessException) e).getCode() != null) {
				log.error("code:" + ((BusinessException) e).getCode());
			}
			errorResponse.setMessage(e.getMessage());
			errorResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			errorResponse.setCode("BizError");

		} else {
			log.error("handle Exception", e);
			errorResponse.setMessage("A problem occurred during precessing. Please contact your system administrator.");
			errorResponse.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
			errorResponse.setCode("Error");
		}

		return errorResponse;
	}

}
