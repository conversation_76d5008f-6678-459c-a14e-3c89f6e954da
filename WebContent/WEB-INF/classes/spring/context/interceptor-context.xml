<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:mvc="http://www.springframework.org/schema/mvc"
    xsi:schemaLocation="http://www.springframework.org/schema/beans https://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/mvc https://www.springframework.org/schema/mvc/spring-mvc.xsd" >
	
	<bean id="xssInterceptor" class="id.co.kiwoom.kwis.config.interceptor.XssInterceptor"/>
	<bean id="postHandleInterceptor" class="id.co.kiwoom.kwis.config.interceptor.PostHandleInterceptor"/>
	
    <!-- interceptor Priority is important. Must be applied in order. -->
    <mvc:interceptors>
    
    	<!-- if bypass the lucy fillter, the xss script finally blocks -->
    	<mvc:interceptor>
            <mvc:mapping path="/**" />
            <mvc:exclude-mapping path="/kid/**"/>
            <mvc:exclude-mapping path="/maint/**"/>
            <mvc:exclude-mapping path="/help/**"/>
            <mvc:exclude-mapping path="/resources/**" />
            <mvc:exclude-mapping path="/**/*.ico" />
            <mvc:exclude-mapping path="/common/error/**" />
            <ref bean="xssInterceptor" />
        </mvc:interceptor>
        
        <!-- post handler --> 
	    <mvc:interceptor>
			<mvc:mapping path="/**" />
			<mvc:exclude-mapping path="/kid/**"/>
			<mvc:exclude-mapping path="/maint/**"/>
			<mvc:exclude-mapping path="/help/**"/>
			<mvc:exclude-mapping path="/resources/**" />
	        <mvc:exclude-mapping path="/**/*.ico" />
	        <mvc:exclude-mapping path="/error/**" />
		    <ref bean="postHandleInterceptor" />
		</mvc:interceptor>
		
    </mvc:interceptors>
</beans>
