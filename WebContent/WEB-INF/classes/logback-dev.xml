<?xml version="1.0" encoding="UTF-8"?>
<!-- Check settings file for changes every 60 seconds and update on changes -->
<configuration scan="true" scanPeriod="60 seconds">
	
	<!-- log file path -->
    <property name="PROJECT_NAME" value="home" />
	
	<!-- log file path -->
    <property name="LOG_DIR" value="/log/logback/${PROJECT_NAME}" />
    
    <!-- log file name -->
    <property name="INFO_FILE_NAME" value="info_%d{yyyy-MM-dd}" />
	
	<!-- error log file name -->
    <property name="ERROR_FILE_NAME" value="error_%d{yyyy-MM-dd}" />
    
	<!-- log pattern -->
	<property name="LOG_PATTERN" value="%-5level %d{yy-MM-dd HH:mm:ss}[%thread] [%logger{0}:%line] - %msg%n"/>
	    
    <!-- CONSOLE log Appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    
    <!-- CONSOLE log Appender -->
    <appender name="CONSOLE2" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{HH:mm:ss.SSS} [%-5level] %logger.%method\(%line\) --- %msg%n</pattern>
		</encoder>
    </appender>
	
	<!-- INFO log Appender -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- setting log print pattern -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>

        <!-- rolling policy -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- Automatic compression of log files by date -->
            <fileNamePattern>${LOG_DIR}/info/${INFO_FILE_NAME}_%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- Maximum capacity per file -->
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- Log file maximum archive period -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>
		
	<!-- ERROR log Appender -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        
        <!-- rolling policy -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- Automatic compression of log files by date -->
            <fileNamePattern>${LOG_DIR}/error/${ERROR_FILE_NAME}_%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- Maximum capacity per file -->
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- Log file maximum archive period -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
    </appender>
	
	<!-- setting root logging level -->
	<root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>
    
    <!-- setting logging level for specific packages -->
    <logger name="java.sql" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="jdbc" level="OFF" />
	
	<logger name="jdbc.sqlonly" level="INFO" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="jdbc.sqltiming" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="jdbc.audit" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="jdbc.resultsettable" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="jdbc.connection" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="org.springframework" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="org.springframework.security" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="org.springframework.data" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="org.apache.tiles" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="net.springbranch" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="com.kiwoom" level="ERROR" additivity="false">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>
	
	<logger name="TUX" level="INFO" additivity="false">
		<appender-ref ref="CONSOLE2" />
		<appender-ref ref="ERROR_FILE"/>
	</logger>

</configuration>
