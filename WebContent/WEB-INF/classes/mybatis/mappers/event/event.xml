<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="id.co.kiwoom.kwis.event.mapper.EventMapper">
	<resultMap type="java.util.HashMap" id="EventPrizeOutVo">
		<result column="EVENT_ID" property="EVENT_ID" />
		<result column="PRIZE_SEQ_NO" property="PRIZE_SEQ_NO" />
		<result column="PRIZE_CD" property="PRIZE_CD" />
		<result column="USE_YN" property="USE_YN" />
		<result column="LOGIN_ID" property="LOGIN_ID" />
		<result column="BRODID" property="BRODID" />
		<result column="RGST_TIME" property="RGST_TIME" />
		<result column="CHNG_TIME" property="CHNG_TIME" />
		<result column="EVENT_SEQ" property="EVENT_SEQ" />
	</resultMap>
	
	<select id="selectMaxEventSeq" resultType="int">
		SELECT NVL(MAX(EVENT_SEQ), 1) 
			FROM EVENT_PRIZE
	</select>
	
	<!-- Query to select the data in the lowest order among the prizes not won.  -->
	<select id="selectNotWonPrize" resultMap="EventPrizeOutVo" parameterType="id.co.kiwoom.kwis.event.vo.EventPrizeInVo">
		SELECT EVENT_ID, 
			PRIZE_SEQ_NO, 
			PRIZE_CD
		FROM EVENT_PRIZE
			WHERE EVENT_ID = #{eventId}
				AND EVENT_SEQ = #{eventSeq}
				AND PRIZE_SEQ_NO IN 
				(
					SELECT 
						NVL(MIN(PRIZE_SEQ_NO), 0) 
					FROM EVENT_PRIZE
						WHERE EVENT_ID = #{eventId} AND EVENT_SEQ = #{eventSeq} AND USE_YN = 'N'
				)
				AND ROWNUM = 1
	</select>
	
	<!-- Query to UPDATE prize winning data. -->
	<update id="updateWonPrize" parameterType="id.co.kiwoom.kwis.event.vo.EventPrizeInVo">
		UPDATE 
			EVENT_PRIZE 
		SET 
			USE_YN = 'Y', 
			LOGIN_ID = #{loginId},
			CHNG_TIME = SYSDATE 
		WHERE EVENT_ID = #{eventId}
			AND EVENT_SEQ = #{eventSeq}
			AND PRIZE_SEQ_NO = #{prizeSeqNo} 
	</update>

</mapper>