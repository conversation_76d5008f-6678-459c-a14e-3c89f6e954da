<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-/mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="id.co.kiwoom.kwis.product.mapper.ProductMapper">

	<resultMap type="java.util.HashMap" id ="products" >
		<result column="SEQ_NO" property="SEQ_NO" />		
		<result column="PRD_GRP_CD" property="PRD_GRP_CD" />
		<result column="PRD_CD" property="PRD_CD"/>
		<result column="NM" property="NM" />
		<result column="EXPS_YN" property="EXPS_YN" />
		<result column="EXPS_RANK" property="EXPS_RANK" />	
		<result column="AUM" property="AUM" />
		<result column="RTN_RT_1" property="RTN_RT_1" />
		<result column="RTN_RT_3" property="RTN_RT_3" />
		<result column="RTN_RT_6" property="RTN_RT_6" />
		<result column="RTN_RT_12" property="RTN_RT_12" />
		<result column="RTN_RT_36" property="RTN_RT_36" />
		<result column="SCORE" property="SCORE" />
		<result column="LOGO_IMG_URL" property="LOGO_IMG_URL" />
		<result column="RGST_ID" property="RGST_ID" />
		<result column="RGST_TIME" property="RGST_TIME" />
		<result column="CHNG_ID" property="CHNG_ID" />
		<result column="CHNG_DT" property="CHNG_DT" />
	</resultMap>

	<select id="selectProducts" parameterType="id.co.kiwoom.kwis.product.vo.ProductSearchVo" resultMap="products">
		SELECT /*+ INDEX_DESC(PROD_LST  PROD_LST_PK) */
			SEQ_NO, PRD_GRP_CD, PRD_CD, NM, EXPS_YN, EXPS_RANK, AUM, RTN_RT_1, 
			RTN_RT_3,RTN_RT_6,RTN_RT_12,RTN_RT_36, SCORE, LOGO_IMG_URL,
			RGST_ID, RGST_TIME, CHNG_ID, CHNG_DT
		FROM PROD_LST
		<where>
			<if test="fromDate != null and fromDate != '' and toDate != null and toDate != ''">
				 AND RGST_TIME BETWEEN TO_DATE(#{fromDate} || '00:00:00', 'DD/MM/YYYY HH24:MI:SS') AND TO_DATE(#{toDate} || '23:59:59', 'DD/MM/YYYY HH24:MI:SS')
			</if>
			AND EXPS_YN = 'Y'
		</where>
		ORDER BY EXPS_RANK ASC
		OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY
	</select>

</mapper>