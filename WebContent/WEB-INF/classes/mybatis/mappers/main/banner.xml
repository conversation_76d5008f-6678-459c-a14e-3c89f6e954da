<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-/mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="id.co.kiwoom.kwis.banner.mapper.BannerMapper">

	<resultMap type="java.util.HashMap" id ="banners" >
		<result column="SEQ_NO" property="SEQ_NO" />
		<result column="RANK" property="RANK"/>
		<result column="IMG_URL" property="IMG_URL" />	
		<result column="MOBILE_IMG_URL" property="MOBILE_IMG_URL" />
		<result column="IMG_DESC" property="IMG_DESC" />
		<result column="PC_LINK_URL" property="PC_LINK_URL" />
		<result column="MOBILE_LINK_URL" property="MOBILE_LINK_URL" />
		<result column="LINK_METHOD" property="LINK_METHOD" />
	</resultMap>

	<select id="selectBanners" parameterType="id.co.kiwoom.kwis.banner.vo.BannerSearchVo" resultMap="banners">
		SELECT /*+ INDEX_DESC(BANNERLST  BANNERLST_PK) */
			SEQ_NO,
			RANK,
			IMG_URL,
			<if test="svcGb != 'F01' and svcGb != 'F02'">
				MOBILE_IMG_URL,
			</if>
			IMG_DESC,
			DECODE(PC_LINK_URL,NULL,'',PC_LINK_URL) as PC_LINK_URL,
			<if test="svcGb != 'F01' and svcGb != 'F02'">
				DECODE(MOBILE_LINK_URL,NULL,'',MOBILE_LINK_URL) as MOBILE_LINK_URL,
			</if>
			DECODE(LINK_METHOD,1,'_self','_blank') as LINK_METHOD
		FROM BANNERLST
		<where>
			AND SVC_GB = #{svcGb}
			AND EXPS_YN = 'Y'
		</where>
		ORDER BY RANK ASC
	</select>

</mapper>