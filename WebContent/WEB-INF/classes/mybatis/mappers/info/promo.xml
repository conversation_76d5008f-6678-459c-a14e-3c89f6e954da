<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-/mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="id.co.kiwoom.kwis.info.promo.mapper.InfoPromoMapper">

	<resultMap type="java.util.HashMap" id ="promos" >
		<result column="SEQ_NO" property="SEQ_NO" />
		<result column="RANK" property="RANK"/>
		<result column="IMG_URL" property="IMG_URL" />
		<result column="MOBILE_IMG_URL" property="MOBILE_IMG_URL" />
		<result column="IMG_DESC" property="IMG_DESC" />
		<result column="INFO_DESC" property="INFO_DESC" typeHandler="org.apache.ibatis.type.ClobTypeHandler"/>
		<result column="PC_LINK_URL" property="PC_LINK_URL" />
		<result column="TITL" property="TITL" />
		<result column="MOBILE_LINK_URL" property="MOBILE_LINK_URL" />
		<result column="LINK_METHOD" property="LINK_METHOD" />
		<result column="RGST_ID" property="RGST_ID" />
		<result column="RGST_TIME" property="RGST_TIME" />
	</resultMap>

	<select id="selInfoPromos" parameterType="id.co.kiwoom.kwis.info.promo.vo.InfoPromoSearchVo" resultMap="promos">
		SELECT /*+ INDEX_DESC(BANNERLST  BANNERLST_PK) */
		SEQ_NO,
		RANK,
		IMG_URL,
		MOBILE_IMG_URL,
		IMG_DESC,
		INFO_DESC as INFO_DESC,
		TITL,
		DECODE(PC_LINK_URL,NULL,'',PC_LINK_URL) as PC_LINK_URL,
		DECODE(MOBILE_LINK_URL,NULL,'',MOBILE_LINK_URL) as MOBILE_LINK_URL,
		DECODE(LINK_METHOD,1,'_self','_blank') as LINK_METHOD,
		'Admin' AS RGST_ID,
		TO_CHAR(RGST_TIME, 'DD Month YYYY', 'NLS_DATE_LANGUAGE=ENGLISH') as RGST_TIME
		FROM BANNERLST
		<where>
			AND SVC_GB = #{svcGb}
			AND EXPS_YN = 'Y'
		</where>
		ORDER BY RANK ASC
		OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY
	</select>
	<select id="selInfoPromosTotalCnt" resultType="Integer"
			parameterType="id.co.kiwoom.kwis.info.promo.vo.InfoPromoSearchVo">
		SELECT COUNT(*)
		FROM BANNERLST
		<where>
			AND SVC_GB = #{svcGb}
			AND EXPS_YN = 'Y'
		</where>
	</select>


	<select id="selInfoPromoDetail" resultMap="promos"
			parameterType="Integer">
		SELECT  /*+ INDEX_DESC(BANNERLST BANNERLST_PK) */
		'1' GUBUN,
		SEQ_NO,
		RANK,
		IMG_URL,
		MOBILE_IMG_URL,
		IMG_DESC,
		INFO_DESC,
		TITL,
		DECODE(PC_LINK_URL, NULL, '', PC_LINK_URL) AS PC_LINK_URL,
		DECODE(MOBILE_LINK_URL, NULL, '', MOBILE_LINK_URL) AS MOBILE_LINK_URL,
		DECODE(LINK_METHOD, 1, '_self', '_blank') AS LINK_METHOD,
		'Admin' AS RGST_ID,
		TO_CHAR(RGST_TIME, 'DD Month YYYY', 'NLS_DATE_LANGUAGE=ENGLISH') AS RGST_TIME
		FROM BANNERLST
		WHERE SEQ_NO = #{seqNo}
		  AND SVC_GB = 'E02'
		  AND EXPS_YN = 'Y'
		UNION ALL

		SELECT /*+ INDEX_DESC(BANNERLST BANNERLST_PK) */
		'2' GUBUN,
		SEQ_NO,
		RANK,
		IMG_URL,
		MOBILE_IMG_URL,
		IMG_DESC,
		INFO_DESC,
		TITL,
		DECODE(PC_LINK_URL, NULL, '', PC_LINK_URL) AS PC_LINK_URL,
		DECODE(MOBILE_LINK_URL, NULL, '', MOBILE_LINK_URL) AS MOBILE_LINK_URL,
		DECODE(LINK_METHOD, 1, '_self', '_blank') AS LINK_METHOD,
		'Admin' AS RGST_ID,
		TO_CHAR(RGST_TIME, 'DD Month YYYY', 'NLS_DATE_LANGUAGE=ENGLISH') AS RGST_TIME
		FROM BANNERLST
		WHERE SEQ_NO = (SELECT MAX(SEQ_NO) FROM BANNERLST WHERE SEQ_NO <![CDATA[<]]> #{seqNo} AND SVC_GB ='E02'  AND EXPS_YN = 'Y')
		AND SVC_GB = 'E02'
		AND EXPS_YN = 'Y'
		UNION ALL

		SELECT /*+ INDEX_ASC(BANNERLST BANNERLST_PK) */
		'3' GUBUN,
		SEQ_NO,
		RANK,
		IMG_URL,
		MOBILE_IMG_URL,
		IMG_DESC,
		INFO_DESC,
		TITL,
		DECODE(PC_LINK_URL, NULL, '', PC_LINK_URL) AS PC_LINK_URL,
		DECODE(MOBILE_LINK_URL, NULL, '', MOBILE_LINK_URL) AS MOBILE_LINK_URL,
		DECODE(LINK_METHOD, 1, '_self', '_blank') AS LINK_METHOD,
		'Admin' AS RGST_ID,
		TO_CHAR(RGST_TIME, 'DD Month YYYY', 'NLS_DATE_LANGUAGE=ENGLISH') AS RGST_TIME
		FROM BANNERLST
		WHERE
		    SEQ_NO = (SELECT MIN(SEQ_NO) FROM BANNERLST WHERE SEQ_NO <![CDATA[>]]> #{seqNo} AND SVC_GB ='E02'  AND EXPS_YN = 'Y')
		AND SVC_GB = 'E02'
		  AND EXPS_YN = 'Y'
	</select>
</mapper>
