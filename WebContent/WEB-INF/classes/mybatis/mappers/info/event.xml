<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-/mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="id.co.kiwoom.kwis.info.event.mapper.InfoEventMapper">

	<resultMap type="java.util.HashMap" id ="banners" >
		<result column="SEQ_NO" property="SEQ_NO" />
		<result column="RANK" property="RANK"/>
		<result column="IMG_URL" property="IMG_URL" />
		<result column="MOBILE_IMG_URL" property="MOBILE_IMG_URL" />
		<result column="IMG_DESC" property="IMG_DESC" />
		<result column="INFO_DESC" property="INFO_DESC" />
		<result column="PC_LINK_URL" property="PC_LINK_URL" />
		<result column="TITL" property="TITL" />
		<result column="MOBILE_LINK_URL" property="MOBILE_LINK_URL" />
		<result column="LINK_METHOD" property="LINK_METHOD" />
	</resultMap>

	<select id="selInfoEvents" parameterType="id.co.kiwoom.kwis.info.event.vo.InfoEventSearchVo" resultMap="banners">
		SELECT /*+ INDEX_DESC(BANNERLST  BANNERLST_PK) */
		SEQ_NO,
		RANK,
		IMG_URL,
		MOBILE_IMG_URL,
		IMG_DESC,
		DBMS_LOB.SUBSTR(INFO_DESC, 4000, 1) as INFO_DESC,
		TITL,
		DECODE(PC_LINK_URL,NULL,'',PC_LINK_URL) as PC_LINK_URL,
		DECODE(MOBILE_LINK_URL,NULL,'',MOBILE_LINK_URL) as MOBILE_LINK_URL,
		DECODE(LINK_METHOD,1,'_self','_blank') as LINK_METHOD
		FROM BANNERLST
		<where>
			AND SVC_GB = #{svcGb}
			AND EXPS_YN = 'Y'
		</where>
		ORDER BY RANK ASC
		OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY
	</select>
	<select id="selInfoEventsTotalCnt" resultType="Integer"
			parameterType="id.co.kiwoom.kwis.info.event.vo.InfoEventSearchVo">
		SELECT COUNT(*)
		FROM BANNERLST
		<where>
			AND SVC_GB = #{svcGb}
			AND EXPS_YN = 'Y'
		</where>
	</select>

</mapper>
