<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="id.co.kiwoom.kwis.research.iponews.mapper.IpoNewsMapper">


	<resultMap type="java.util.HashMap" id ="ipoNewsList" >
		<result column="SEQNO" property="SEQNO" />		
		<result column="TITL" property="TITL" />
		<result column="CONT" property="CONT" javaType="String" jdbcType="CLOB" />
		<result column="DOCWRGB" property="DOCWRGB" />
		<result column="READCNT" property="READCNT" />
		<result column="BRODID" property="BRODID" />	
		<result column="ALIA" property="ALIA" />
		<result column="SNMAKEDATE2" property="SNMAKEDATE2" />
		<result column="MAKEDATE" property="MAKEDATE" />
		<result column="MAKEDATE2" property="MAKEDATE2" />
		<result column="MAKEDATE3" property="MAKEDATE3" />
		<result column="MAKEDATE4" property="MAKEDATE4" />
		<result column="MAKEDATE5" property="MAKEDATE5" />
		<result column="DAYGB" property="DAYGB" />
	</resultMap>

	<select id="selectIpoNewsList" resultMap="ipoNewsList" parameterType="id.co.kiwoom.kwis.research.iponews.vo.IpoNewsSearchVo">
		SELECT /*+ INDEX_DESC(KBNTWB  KBNTWB_PK) */
			    SEQNO, TITL, CONT, DOCWRGB, READCNT, 
				BRODID, ALIA,
				TO_CHAR(MAKEDATE,'Month dd, yyyy', 'NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE,
				TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI') MAKEDATE2,
				TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI:SS') MAKEDATE3,
				TO_CHAR(MAKEDATE,'dd/mm') MAKEDATE4,
				TO_CHAR(MAKEDATE,'FMMonth DD, YYYY') MAKEDATE5,
				DECODE (TO_CHAR (MAKEDATE, 'YYYYMMDD'),TO_CHAR (SYSDATE, 'YYYYMMDD'), 1, 0 ) DAYGB,
				CASE WHEN MAKEDATE >= (SYSDATE - 1) THEN 1 ELSE 0 END AS IS_NEW
		FROM KBNTWB
		<where>
			<if test="searchTerms !='' and searchTerms != null ">
				<choose>
					<when test="searchCriteria == 'all'">
						AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT , UPPER(#{searchTerms}), 1, 1) > 0
						OR UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%')
					</when>
					<when test="searchCriteria == 'titl'">
						AND UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%'
					</when>
					<when test="searchCriteria == 'cont'">
						AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT , UPPER(#{searchTerms}), 1, 1) > 0
						)
					</when>
				</choose>
			</if>
			<if test="fromDate != null and fromDate != '' and toDate != null and toDate != ''">
				 AND MAKEDATE BETWEEN TO_DATE(#{fromDate} || '00:00:00', 'DD/MM/YYYY HH24:MI:SS') AND TO_DATE(#{toDate} || '23:59:59', 'DD/MM/YYYY HH24:MI:SS')
			</if>
		</where>
		ORDER BY SEQNO DESC
		OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY
	</select>
	
	<select id="selectIpoNewsListTotalCnt" resultType="Integer" parameterType="id.co.kiwoom.kwis.research.iponews.vo.IpoNewsSearchVo">
		SELECT /*+ INDEX_DESC(KBNTWB  KBNTWB_PK) */ COUNT(SEQNO) AS CNT
		FROM KBNTWB
		<where> 
			<if test="searchTerms !='' and searchTerms != null ">
				<choose>
					<when test="searchCriteria == 'all'">
						AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT , UPPER(#{searchTerms}), 1, 1) > 0
						OR UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%')
					</when>
					<when test="searchCriteria == 'titl'">
						AND UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%'
					</when>
					<when test="searchCriteria == 'cont'">
						AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT , UPPER(#{searchTerms}), 1, 1) > 0
						)
					</when>
				</choose>
			</if>
			<if test="fromDate != null and fromDate != '' and toDate != null and toDate != ''">
				 AND MAKEDATE BETWEEN TO_DATE(#{fromDate} || '00:00:00', 'DD/MM/YYYY HH24:MI:SS') AND TO_DATE(#{toDate} || '23:59:59', 'DD/MM/YYYY HH24:MI:SS')
			</if>
		</where>
	</select>
	
	<resultMap type="java.util.HashMap" id ="ipoNewsDetail" >
		<result column="GUBUN" property="GUBUN" />
		<result column="SEQNO" property="SEQNO" />		
		<result column="TITL" property="TITL" />
		<result column="CONT" property="CONT" javaType="String" jdbcType="CLOB" />
		<result column="DOCWRGB" property="DOCWRGB" />
		<result column="READCNT" property="READCNT" />
		<result column="BRODID" property="BRODID" />	
		<result column="ALIA" property="ALIA" />
		<result column="MAKEDATE" property="MAKEDATE" />
		<result column="MAKEDATE2" property="MAKEDATE2" />
		<result column="MAKEDATE3" property="MAKEDATE3" />
		<result column="MAKEDATE4" property="MAKEDATE4" />
		<result column="MAKEDATE_POPUP" property="MAKEDATE_POPUP" />
	</resultMap>
	
	<select id="selectIpoNewsDetail" resultMap="ipoNewsDetail" parameterType="id.co.kiwoom.kwis.research.iponews.vo.IpoNewsSearchVo">
		SELECT /*+ INDEX_DESC(KBNTWB  KBNTWB_PK) */ 
			'1' GUBUN, SEQNO, TITL, CONT, DOCWRGB, 
			READCNT, BRODID, ALIA,
			TO_CHAR(MAKEDATE, 'yyyymmdd') SNMAKEDATE2,  
			TO_CHAR(MAKEDATE,'Month dd, yyyy', 'NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE,
			TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI') MAKEDATE2,
			TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI:SS') MAKEDATE3,
			TO_CHAR(MAKEDATE,'dd/mm') MAKEDATE4,
			TO_CHAR(MAKEDATE, 'Month dd, yyyy','NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE_POPUP,
			CASE WHEN MAKEDATE >= (SYSDATE - 1) THEN 1 ELSE 0 END AS IS_NEW
		FROM KBNTWB
		WHERE SEQNO = #{seqNo}
		
		UNION ALL
		
		SELECT /*+ INDEX_DESC(KBNTWB  KBNTWB_PK) */ 
			'2' GUBUN, SEQNO, TITL, CONT, DOCWRGB, 
			READCNT, BRODID, ALIA, 
			TO_CHAR(MAKEDATE,'dd/mm/yyyy') MAKEDATE,
			TO_CHAR(MAKEDATE, 'yyyymmdd') SNMAKEDATE2, 
			TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI') MAKEDATE2,
			TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI:SS') MAKEDATE3,
			TO_CHAR(MAKEDATE,'dd/mm') MAKEDATE4,
		TO_CHAR(MAKEDATE, 'Month dd, yyyy','NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE_POPUP,
			CASE WHEN MAKEDATE >= (SYSDATE - 1) THEN 1 ELSE 0 END AS IS_NEW
		FROM KBNTWB
		<![CDATA[  WHERE SEQNO < #{seqNo} AND ROWNUM = 1 ]]>
		
		UNION ALL
		
		SELECT /*+ INDEX_ASC(KBNTWB  KBNTWB_PK) */ 
			'3' GUBUN, SEQNO, TITL, CONT, DOCWRGB, 
			READCNT, BRODID, ALIA, 
			TO_CHAR(MAKEDATE, 'yyyymmdd') SNMAKEDATE2, 
			TO_CHAR(MAKEDATE,'dd/mm/yyyy') MAKEDATE,
			TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI') MAKEDATE2,
			TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI:SS') MAKEDATE3,
			TO_CHAR(MAKEDATE,'dd/mm') MAKEDATE4,
			TO_CHAR(MAKEDATE, 'Month dd, yyyy','NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE_POPUP,
			CASE WHEN MAKEDATE >= (SYSDATE - 1) THEN 1 ELSE 0 END AS IS_NEW
		FROM KBNTWB
		<![CDATA[  WHERE SEQNO > #{seqNo} AND ROWNUM = 1 ]]>
	</select>
	
	<update id="updateIpoNewsReadCnt" parameterType="id.co.kiwoom.kwis.research.iponews.vo.IpoNewsSearchVo">
		UPDATE KBNTWB SET
		    READCNT  = READCNT + 1
		 WHERE SEQNO = #{seqNo}
	</update>
	
</mapper>