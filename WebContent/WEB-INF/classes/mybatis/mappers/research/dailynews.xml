<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="id.co.kiwoom.kwis.research.dailynews.mapper.DailyNewsMapper">
	<resultMap type="java.util.HashMap" id="DailyNewsOutVo">
		<result column="GUBUN" property="GUBUN" />
		<result column="SEQNO" property="SEQNO" />
		<result column="TITL" property="TITL" />
		<result column="CONT" property="CONT" jdbcType="CLOB" javaType="java.lang.String"/>
		<result column="DOCWRGB" property="DOCWRGB" />
		<result column="READCNT" property="READCNT" />
		<result column="BRODID" property="BRODID" />
		<result column="ALIA" property="ALIA" />
		<result column="MAKEDATE" property="MAKEDATE" />
		<result column="MAKEDATE2" property="MAKEDATE2" />
		<result column="MAKEDATE3" property="MAKEDATE3" />
		<result column="MAKEDATE4" property="MAKEDATE4" />
		<result column="MAKEDATE5" property="MAKEDATE5" />
		<result column="MAKEDATE_POPUP" property="MAKEDATE_POPUP" />
		<result column="STOCK_CODE_1" property="STOCK_CODE_1" />
		<result column="STOCK_DESC_1" property="STOCK_DESC_1" />
		<result column="CONT_1" property="CONT_1" jdbcType="CLOB" javaType="java.lang.String"/>
		<result column="STOCK_CODE_2" property="STOCK_CODE_2" />
		<result column="STOCK_DESC_2" property="STOCK_DESC_2" />
		<result column="CONT_2" property="CONT_2" jdbcType="CLOB" javaType="java.lang.String"/>
		<result column="STOCK_CODE_3" property="STOCK_CODE_3" />
		<result column="STOCK_DESC_3" property="STOCK_DESC_3" />
		<result column="CONT_3" property="CONT_3" jdbcType="CLOB" javaType="java.lang.String"/>
		<result column="STOCK_CODE_4" property="STOCK_CODE_4" />
		<result column="STOCK_DESC_4" property="STOCK_DESC_4" />
		<result column="CONT_4" property="CONT_4" jdbcType="CLOB" javaType="java.lang.String"/>
		<result column="STOCK_CODE_5" property="STOCK_CODE_5" />
		<result column="STOCK_DESC_5" property="STOCK_DESC_5" />
		<result column="CONT_5" property="CONT_5" jdbcType="CLOB" javaType="java.lang.String"/>
	</resultMap>

	<select id="selDailyNewsList" resultMap="DailyNewsOutVo" parameterType="id.co.kiwoom.kwis.research.dailynews.vo.DailyNewsSearchVo">
		SELECT /*+ INDEX_DESC(KBTRD KBTRD_PK) */
		SEQNO, TITL, CONT, DOCWRGB, READCNT,
		BRODID, ALIA,
		TO_CHAR(MAKEDATE, 'Month dd, yyyy','NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE,
		TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI') MAKEDATE2,
		TO_CHAR(MAKEDATE,'dd/mm/yyyy HH24:MI:SS') MAKEDATE3,
		TO_CHAR(MAKEDATE,'dd/mm') MAKEDATE4,
		TO_CHAR(MAKEDATE,'FMMonth DD, YYYY') MAKEDATE5,
		CASE
        	WHEN MAKEDATE >= (SYSDATE - 1) THEN 1
        	ELSE 0
    	END AS IS_NEW
		FROM KBTRD
		<where>
			<if test="searchTerms !='' and searchTerms != null ">
				<choose>
					<when test="searchCriteria == 'all'">
						AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , UPPER(#{searchTerms}), 1, 1) > 0
						OR UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_1) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_2) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_3) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_4) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_5) LIKE '%' || UPPER(#{searchTerms}) || '%'
						)
					</when>
					<when test="searchCriteria == 'titl'">
						AND UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%'
					</when>
					<when test="searchCriteria == 'cont'">
						AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , UPPER(#{searchTerms}), 1, 1) > 0
						OR UPPER(STOCK_CODE_1) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_2) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_3) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_4) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_5) LIKE '%' || UPPER(#{searchTerms}) || '%'
						)
					</when>
				</choose>
			</if>
			<if test="fromDate != null and fromDate != '' and toDate != null and toDate != ''">
				 AND MAKEDATE BETWEEN TO_DATE(#{fromDate} || '00:00:00', 'DD/MM/YYYY HH24:MI:SS') AND TO_DATE(#{toDate} || '23:59:59', 'DD/MM/YYYY HH24:MI:SS')
			</if>
		</where>
		ORDER BY SEQNO DESC
		OFFSET #{offset} ROWS FETCH NEXT #{limit} ROWS ONLY
	</select>
	
	<select id="selDailyNewsTotalCnt" resultType="Integer"
		parameterType="id.co.kiwoom.kwis.research.dailynews.vo.DailyNewsSearchVo">
		SELECT COUNT(*)
		FROM KBTRD
		<where>
			<if test="searchTerms !='' and searchTerms != null ">
				<choose>
					<when test="searchCriteria == 'all'">
						AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , UPPER(#{searchTerms}), 1, 1) > 0
						OR UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_1) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_2) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_3) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_4) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_5) LIKE '%' || UPPER(#{searchTerms}) || '%'
						)
					</when>
					<when test="searchCriteria == 'titl'">
						AND UPPER(TITL) LIKE '%' || UPPER(#{searchTerms}) || '%'
					</when>
					<when test="searchCriteria == 'cont'">
					AND (DBMS_LOB.INSTR(CONT , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , #{searchTerms}, 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_1 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_2 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_3 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_4 , UPPER(#{searchTerms}), 1, 1) > 0
						OR DBMS_LOB.INSTR(CONT_5 , UPPER(#{searchTerms}), 1, 1) > 0
						OR UPPER(STOCK_CODE_1) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_2) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_3) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_4) LIKE '%' || UPPER(#{searchTerms}) || '%'
						OR UPPER(STOCK_CODE_5) LIKE '%' || UPPER(#{searchTerms}) || '%'
						)
					</when>
				</choose>
			</if>
			<if test="fromDate != null and fromDate != '' and toDate != null and toDate != ''">
				 AND MAKEDATE BETWEEN TO_DATE(#{fromDate} || '00:00:00', 'DD/MM/YYYY HH24:MI:SS') AND TO_DATE(#{toDate} || '23:59:59', 'DD/MM/YYYY HH24:MI:SS')
			</if>
		</where>
	</select>
	<select id="selDailyNewsDetail" resultMap="DailyNewsOutVo"
		parameterType="Integer">
		SELECT /*+ INDEX_DESC(KBTRD  KBTRD_PK) */ 
			'1' GUBUN, SEQNO, TITL, CONT, DOCWRGB, 
			READCNT, BRODID, ALIA, 
			STOCK_CODE_1,
	        STOCK_DESC_1,
	        CONT_1,
	        B.LOGO_IMG_FILE_NM as STOCK_IMG_1,
	        STOCK_CODE_2,
	        STOCK_DESC_2,
	        CONT_2,
	        C.LOGO_IMG_FILE_NM as STOCK_IMG_2,
	        STOCK_CODE_3,
	        STOCK_DESC_3,
	        CONT_3,
	        D.LOGO_IMG_FILE_NM as STOCK_IMG_3,
	        STOCK_CODE_4,
	        STOCK_DESC_4,
	        CONT_4,
	        E.LOGO_IMG_FILE_NM as STOCK_IMG_4,
	        STOCK_CODE_5,
	        STOCK_DESC_5,
        	CONT_5,
        	F.LOGO_IMG_FILE_NM as STOCK_IMG_5,
			TO_CHAR(MAKEDATE, 'Month dd, yyyy','NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE,
			CASE
        		WHEN MAKEDATE >= (SYSDATE - 1) THEN 1
        		ELSE 0
    		END AS IS_NEW
		FROM KBTRD A
		    LEFT JOIN STOCK_IMG B ON A.STOCK_CODE_1 = B.STOCK_CODE
            LEFT JOIN STOCK_IMG C ON A.STOCK_CODE_2 = C.STOCK_CODE
            LEFT JOIN STOCK_IMG D ON A.STOCK_CODE_3 = D.STOCK_CODE
            LEFT JOIN STOCK_IMG E ON A.STOCK_CODE_4 = E.STOCK_CODE
            LEFT JOIN STOCK_IMG F ON A.STOCK_CODE_5 = E.STOCK_CODE
		WHERE SEQNO = #{id}
		
		UNION ALL
		
		SELECT /*+ INDEX_DESC(KBTRD  KBTRD_PK) */ 
			'2' GUBUN, SEQNO, TITL, CONT, DOCWRGB, 
			READCNT, BRODID, ALIA, 
			STOCK_CODE_1,
	        STOCK_DESC_1,
	        CONT_1,
	        B.LOGO_IMG_FILE_NM as STOCK_IMG_1,
	        STOCK_CODE_2,
	        STOCK_DESC_2,
	        CONT_2,
	        C.LOGO_IMG_FILE_NM as STOCK_IMG_2,
	        STOCK_CODE_3,
	        STOCK_DESC_3,
	        CONT_3,
	        D.LOGO_IMG_FILE_NM as STOCK_IMG_3,
	        STOCK_CODE_4,
	        STOCK_DESC_4,
	        CONT_4,
	        E.LOGO_IMG_FILE_NM as STOCK_IMG_4,
	        STOCK_CODE_5,
	        STOCK_DESC_5,
        	CONT_5,
        	F.LOGO_IMG_FILE_NM as STOCK_IMG_5,
			TO_CHAR(MAKEDATE, 'Month dd, yyyy','NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE,
			CASE
        		WHEN MAKEDATE >= (SYSDATE - 1) THEN 1
        		ELSE 0
    		END AS IS_NEW
		FROM KBTRD A
		    LEFT JOIN STOCK_IMG B ON A.STOCK_CODE_1 = B.STOCK_CODE
            LEFT JOIN STOCK_IMG C ON A.STOCK_CODE_2 = C.STOCK_CODE
            LEFT JOIN STOCK_IMG D ON A.STOCK_CODE_3 = D.STOCK_CODE
            LEFT JOIN STOCK_IMG E ON A.STOCK_CODE_4 = E.STOCK_CODE
            LEFT JOIN STOCK_IMG F ON A.STOCK_CODE_5 = E.STOCK_CODE
		<![CDATA[ WHERE SEQNO = (SELECT MAX(SEQNO) FROM KBTRD WHERE SEQNO < #{id}) ]]>   
		UNION ALL
		
		SELECT /*+ INDEX_ASC(KBTRD  KBTRD_PK) */ 
			'3' GUBUN, SEQNO, TITL, CONT, DOCWRGB, 
			READCNT, BRODID, ALIA, 
			STOCK_CODE_1,
	        STOCK_DESC_1,
	        CONT_1,
	        B.LOGO_IMG_FILE_NM as STOCK_IMG_1,
	        STOCK_CODE_2,
	        STOCK_DESC_2,
	        CONT_2,
	        C.LOGO_IMG_FILE_NM as STOCK_IMG_2,
	        STOCK_CODE_3,
	        STOCK_DESC_3,
	        CONT_3,
	        D.LOGO_IMG_FILE_NM as STOCK_IMG_3,
	        STOCK_CODE_4,
	        STOCK_DESC_4,
	        CONT_4,
	        E.LOGO_IMG_FILE_NM as STOCK_IMG_4,
	        STOCK_CODE_5,
	        STOCK_DESC_5,
        	CONT_5,
        	F.LOGO_IMG_FILE_NM as STOCK_IMG_5,
			TO_CHAR(MAKEDATE, 'Month dd, yyyy','NLS_DATE_LANGUAGE = AMERICAN') MAKEDATE,
			CASE
        		WHEN MAKEDATE >= (SYSDATE - 1) THEN 1
        		ELSE 0
    		END AS IS_NEW
		FROM KBTRD A
		    LEFT JOIN STOCK_IMG B ON A.STOCK_CODE_1 = B.STOCK_CODE
            LEFT JOIN STOCK_IMG C ON A.STOCK_CODE_2 = C.STOCK_CODE
            LEFT JOIN STOCK_IMG D ON A.STOCK_CODE_3 = D.STOCK_CODE
            LEFT JOIN STOCK_IMG E ON A.STOCK_CODE_4 = E.STOCK_CODE
            LEFT JOIN STOCK_IMG F ON A.STOCK_CODE_5 = E.STOCK_CODE
		<![CDATA[  WHERE SEQNO = (SELECT MIN(SEQNO) FROM KBTRD WHERE SEQNO > #{id}) ]]> 
		
	</select>

	<update id="updateDailyNewsReadCnt">
		UPDATE KBTRD SET
		    READCNT  = READCNT + 1
		 WHERE SEQNO = #{seqNo}
	</update>
</mapper>