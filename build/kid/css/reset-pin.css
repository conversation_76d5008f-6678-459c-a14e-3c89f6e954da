/* Backdrop */
.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
  z-index: 10;
  display: none; /* Initially hidden */
}

/* Alert box */
.alert-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 30%;
  z-index: 20;
  text-align: center;
  display: none; /* Initially hidden */
}

/* Alert header */
.alert-header {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 3rem;
  font-weight: bold;
  background-color: #ddd;
  padding-top: 15px;
  padding-bottom: 15px;
  color : rgba(8, 29, 88, 1);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

/* Close icon */
.close-icon {
  cursor: pointer;
  font-size: 3.5rem;
  color: #ddd;
  position : absolute;
  right: 0;
  top: -3rem;
  font-weight: 100;
}

/* Alert content */
.alert-content {
  padding: 20px;
  font-size: 2rem;
  color: rgba(97, 98, 100, 1);

}
.disabled-btn {
  background-color: #ccc; /* Gray out the button */
  cursor: not-allowed; /* Change the cursor to indicate it is not clickable */
  opacity: 0.6; /* Lower opacity to make it look disabled */
}
/* Media Query for Mobile Devices */
@media (max-width: 768px) {
  .alert-box {
    width: 80%; /* Increase width on mobile screens */
    padding: 0px 0 10px 0;
  }

  .alert-header {
    font-size: 2.5rem; /* Adjust font size for mobile */
  }

  .alert-content {
    font-size: 1.8rem; /* Smaller font size for mobile */
    padding: 10px;
  }

  .close-icon {
    font-size: 3rem; /* Slightly smaller close icon */
    top: -2.5rem; /* Adjust positioning */
  }
}

/* Media Query for Very Small Screens */
@media (max-width: 480px) {
  .alert-box {
    width: 90%; /* Maximize width for very small screens */
  }

  .alert-header {
    font-size: 2rem; /* Further adjust font size */
  }

  .alert-content {
    font-size: 1.6rem; /* Further adjust content font size */
  }
}
