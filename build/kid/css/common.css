@charset "UTF-8";
/* LANG */
.lang-en,
.lang-id {
    display: none !important;
}
.lang-en.active,
.lang-id.active {
   display: block !important;
}

/*input*/
.inp-check {
    position: relative;
}
.inp-check label {
    display: inline-flex;
    gap: .6rem;
    align-items: center;
    position: relative;
    font-size: var(--fs-1-4);
    line-height: var(--line-1-8);
}

.inp-check input[type="checkbox"]:checked + label::after {
   display: block;
    transition: background 0.25s ease-in-out;
}
.inp-check input[type="checkbox"]:checked + label {
    color: var(--blue-mid);
}
.inp-check input[type="checkbox"]:focus-visible + label::before{
    outline-offset: max(.2rem, 0.1rem);
    outline: max(.2rem, .1rem) solid var(--blue-mid);
}

.inp-check input[type="checkbox"]:hover  + label::before {
    box-shadow: 0 0 0 max(.2rem, 0.2em) var(--blue-mid);
    cursor: pointer;
    transition: box-shadow 0.3s ease-in-out;
    color: var(--gray-lev10);
}
.inp-check label::before {
    content: '';
    display: block;
    width: 1.8rem; height: 1.8rem;
    border: .1rem solid var(--gray-lev10);
    border-radius: .4rem;
}
.inp-check label::after {
    content: '\2713';
    display: none;
    font-size: 2.2rem;
    font-weight: 900;
    color: var(--blue-mid);
    position: absolute;
    left: .2rem; top: .9rem;
}
/* Disable scrolling on the entire HTML */
html {
    overflow: hidden;
    height: 100%;
}

/* Enable scrolling inside the body */
body {
    position: relative;
    width: 100vw !important;
    height: 100vh !important;
    overflow-y: auto; /* Allows vertical scrolling */
    overflow-x: hidden; /* Prevents horizontal scrolling */
    margin: 0;
    padding-top: 80px !important;
    scrollbar-width: thin; /* Makes scrollbar thinner (for Firefox) */
    scrollbar-color:#9c9c9c; /* Hide scrollbar initially */
}

/* Fixed sticky header */
#header {
    width: 100vw;
    z-index: 9999;
    position: fixed;
    top: 0;
    left: 0;
    background: var(--white);
    border-bottom: 0.1rem solid var(--gray-lev6);
    box-shadow: 8px 8px 16px 4px rgba(202, 204, 207, 0.2);
}
#header-wrap {
    width: 120rem; height: 7.9rem;
    display: flex;
    margin: 0 auto;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
}
#header-wrap h1 .logo-pc {
    width: 12.8rem;
    display: block;
}
#header-wrap h1 .logo-pc img {
    width: 100%;
}
#header-wrap h1 .history-back, #header-wrap h1 .move-home {
    display: none;
}
#header-wrap .global-menu-pc {
    display: flex;
    align-self: flex-end;
}
#dev-mode {
    background-color: yellow;
    padding: .5rem 5rem;
    border: .1rem solid red;
    color: var(--red-lev2);
    position: fixed;
    left: 0; top: 0;
    z-index: 10000;
    display: none;
}
#dev-mode.on {
    display: block;
}
.mo-menu-bg {
    display: none;
}
#header-wrap .global-menu-pc li.menu-item{
    margin-right: 3.2rem;
    height: 4.7rem;
    position: relative;
}
#header-wrap .global-menu-pc li.menu-item:last-child {
    margin-right: 0;
}
#header-wrap .global-menu-pc a.menu-name {
    display: block; position: relative;
    width: 100%; height: 100%;
    font-size: 1.6rem;
    letter-spacing: 0.02rem;
    color: var(--gray-lev3);
    font-weight: 700;
}
#header-wrap .global-menu-pc.menu-item.on a.menu-name,
#header-wrap .global-menu-pc.menu-item.active a.menu-name {
    color: var(--blue-lev1);
    font-weight: 600;
}
#header-wrap .global-menu-pc .menu-item a.menu-name:hover,
#header-wrap .global-menu-pc .menu-item.on a.menu-name,
#header-wrap .global-menu-pc a.menu-name.on,
#header-wrap .global-menu-pc .menu-item.active a.menu-name,
#header-wrap .global-menu-pc a.menu-name.active,
#header-wrap .global-menu-pc li.menu-item li.on a,
#header-wrap .global-menu-pc li.menu-item li.active a,
#header-wrap .global-menu-pc li.menu-item li a:hover{
    color: var(--blue-lev1);
}


#header-wrap .global-menu-pc .menu-item.on a.menu-name::after,
#header-wrap .global-menu-pc .menu-item a.menu-name:hover::after,
#header-wrap .global-menu-pc .menu-item.active a.menu-name::after{
    content: ''; height: .2rem;
    position: absolute;
    left: 0; bottom: 0;
    background: var(--blue-lev1);
    animation-duration: .25s;
    animation-name: widthfull;
    animation-fill-mode: forwards;
}

#header-wrap .global-menu-pc li.menu-item ul {
    display: none;
}
#header-wrap .global-menu-pc li.menu-item:nth-child(5) ul{
    right:0;
}

#header-wrap .global-menu-pc li.menu-item.active ul,
#header-wrap .global-menu-pc li.menu-item.on ul{
    display: flex; gap: 4rem;
    z-index: 11; align-items: flex-end;
    justify-content: flex-start;
    max-width: 950px; height: 6.4rem;
    position: absolute;
}

#header-wrap .global-menu-pc li.menu-item.active ul li a,
#header-wrap .global-menu-pc li.menu-item.on ul li a {
    width: 100%; height: 6.4rem;
    display: flex; white-space: nowrap;
    position: relative; align-items: center;
    font-weight: 700;
}

#header-wrap .global-menu-pc li.menu-item.active ul li.active a::after,
#header-wrap .global-menu-pc li.menu-item.on ul li.on a::after,
#header-wrap .global-menu-pc li.menu-item.on ul li a:hover::after,
#header-wrap .global-menu-pc li.menu-item ul li a:hover::after {
    content: ''; height: 2px;
    position: absolute;
    left: 0; bottom: 0;
    background: var(--blue-lev1);
    animation-duration: .25s;
    animation-name: widthfull;
    animation-fill-mode: forwards;
}

body.fixed {
    position: fixed;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
}
.global-menu-bg {
    display: block;
    width: 100%; height: 0rem;
    overflow: hidden;
    background: var(--white);
    position: absolute;
    left: 0;  top: 8rem;
    opacity: 0.85;
    transition:  height 0.05s;
}

.global-menu-bg.active,
.global-menu-bg.on {
    height: 6.3rem;
    box-shadow:8px 8px 16px 4px rgba(202, 204, 207, 0.2);
    transition:  all 0.25s;
}


.global-menu-bg::after {
    content:'';
    display: block ;
    width: 100%; height: 3rem;
    background: var(--white);
    filter: blur(10px);
    opacity: 0.85;
    -webkit-filter: blur(10px);
    backdrop-filter: blur(15px);

}

#sub-menu {
    width: 30.3rem; height: 5.6rem;
    border: .1rem solid var(--gray-lev6);
    padding: 0 .8rem;
    border-radius: 10rem;
    display: grid; gap: .8rem;
    grid-template-columns:  13.4rem 0.1rem 1fr;
    align-items: center;
    justify-content: space-between;
    margin-left: 2rem;
}
#sub-menu em {
    width: .1rem; height: 2.2rem;
    background: var(--gray-lev6);
    display: inline-block;
}

#sub-menu a.btn-openaccount {
    max-width: 13.4rem; height: 4rem;
    border-radius: 2.4rem;
    background: var(--blue-lighter);
    box-shadow: 0px 4px 8px 0px rgba(192, 192, 192, 0.40);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--blue);
    font-weight: 900;
}

#sub-menu a.btn-openaccount img {
    width: 100%; background-color: transparent;
    -webkit-filter: drop-shadow(0px 4px 8px rgba(192, 192, 192, 0.4));
    filter: drop-shadow(0px 4px 8px rgba(192, 192, 192, 0.4));
}
#sub-menu .btn-forgot {
    color: var(--gray-lev10)
}
#sub-menu a.btn-forgot:hover {
    color: var(--pink);
}
#sub-menu .lang-select {
    width: 4.6rem; height: 2.4rem;
    position: relative; z-index: 11;
}
.contents-wrap {
    width: 120rem;
    margin: 0 auto;
    position: relative;
}
.contents-gray {
    padding: 3rem 0 11.7rem;
    background-color: var(--gray-lev7);
}
.contents-blue {
    background-color: var(--blue);
    position: relative;
}
.contents-blue .content-tit {
    color: var(--white);
    margin: 0 0 7.5rem;
}
.only-text-wrap {
    margin: 8.5rem 0;
}

.only-text-wrap p {
    font-size: var(--fs-1-6);
    letter-spacing: 0.02rem;
    color: var(--gray-lev10);
    margin-bottom: 4rem;
}

body.gray {
    background: var(--gray-lev6);
}

/* S:BUTTON */
.btn {
    display: inline-block;
    -webkit-filter: drop-shadow(0px 4px 8px rgba(192, 192, 192, 0.4));
    filter: drop-shadow(0px 4px 8px rgba(192, 192, 192, 0.4));
    font-size: var(--fs-1-8);
    padding: 1rem 0;
    width: 100%;
    border-radius: 2.4rem;
}
.btn.al-c {
    text-align: center;
}
.btn.btn-confirm {
    width: auto;
    font-weight: 200;
    display: inline-block;
    padding: 1.4rem 4rem;
    font-size: var(--fs-1-6);
}

.btn.btn-gray-01{
    background: var( --gray-lev7);
    color: var(--blue-lev1);
    font-weight: 600;
}
.btn.btn-blue-lighter{
    background: var(--blue-lighter);
    color: var(--blue);
    font-weight: 600;
}
.btn.btn-blue-mid {
    background: var(--blue-mid);
    color: var(--white);
    font-weight: 600;
}
.btn.btn-blue {
    background: var(--blue);
    color: var(--white);
    font-weight: 600;
    text-align: center;
}
.btn.btn-white {
    background: var(--white);
    color: var(--blue-mid);
    font-weight: 600;
    text-align: center;

}

.btn.btn-pink-high {
    background: var(--pink-high);
    color: var(--white);
    font-weight: 600;
    text-align: center;
}
.btn.btn-pink {
    background: var(--pink);
    color: var(--white);
    font-weight: 600;
    text-align: center;
}
.btn.btn-green {
    background: linear-gradient(90deg, #20B038 3.01%, #60D66A 95.18%);
    color: var(--white);
    font-weight: 600;
    text-align: center;
}


.btn.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-flow: row;
}

.btn-prev {
    display: flex;
    align-items: center;
    color: var(--gray-lev10) !important;
}
.btn-prev span {
    margin-left: 2.4rem;
}
.btn-next span{
    margin-right: 2.4rem;
}
.btn-prev::before {
    content: ' ';

    margin-right: .4rem;
    width: 1.65rem; height: 1.35rem;
    background-image: url(../images/btn/btn_arrow_prev.svg);
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: contain;
}
.btn-next {
    display: flex;
    align-items: center;
    color: var(--gray-lev10) !important;
}
.btn-next::after {
    content: ' ';
    margin-left: 0.4rem;
    width: 1.65rem; height: 1.35rem;
    background-image: url(../images/btn/btn_arrow_next.svg);
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: contain;
}

.btn-large {
    width: auto;
    height: 4.8rem;
    padding: 1.4rem 4rem;
}
/*BTN ICONS*/
.btn.btn-icon.icon-hero::before{
    content: ' ';
    margin-right: 0.623rem;
    width: 5.6rem; height: 1.5rem;
    background-image: url(../images/icon/icon_hero.svg);
    background-repeat: no-repeat;
    background-position: 100% 100%;
    background-size: contain;
}


/*BTN ALIGN*/
.btn-wrap {
    display: inline-flex;
    gap: 2rem;
}

/* E:BUTTON */

/*SELECT*/
.select-item {
    display:inline-block;
    position:relative;
    vertical-align: middle;
}
.select-item .target {
    position: relative;
    line-height: var(--line-2-2);
    padding-right: 1.5rem;
    color:var(--gray-lev3);
    font-size: var(--fs-1-4);
}
.select-item .target::after {
    width: 0px; height: 0px;
    content:'';
    position: absolute;
    right: 0; top: 50%;
    margin-top: -.3rem;
    border-bottom: .6rem solid var(--gray-lev4);
    border-right: 0.55rem solid transparent;
    border-left: 0.55rem solid transparent;
    transform: rotate(180deg);
}
.select-item .target:hover,
.select-item.on .target:hover,
.select-item.on .target{
    color: var(--blue-lev1);
}

.select-item.on .target:hover::after {
    transform: rotate(180deg);
    transition: transform 0.25s ease-in-out;
}
.select-item .target:hover::after,
.select-item.on .target::after {
    border-bottom: .6rem solid var(--blue-lev1);
    transform: rotate(0deg);
}


.select-item ul {
    width: 95%;
    overflow: hidden;
    max-height: 0;
}

.select-item.on ul {
    max-height: 70rem;
    min-width: 10rem;
    position: absolute;
    left: 0; top: 3rem;
    z-index: 100;
    gap: 0.4rem;
    display: flex;
    flex-direction: column;
    padding: 0.8rem 1.6rem;
    border-radius: 0.8rem;
    background: var(--white);
    transition: max-height 0.25s ease-in-out;
    -webkit-filter: drop-shadow(2px 2px 8px rgba(232, 232, 232, 1));
    filter: drop-shadow(2px 2px 8px rgba(232, 232, 232, 1));
}
.select-item.on ul li{
    background: var(--white);
}

.select-item.on ul li.on,
.select-item.on ul li:hover{
    background: var(--gray-lev7);
}


.select-item.on ul li button {
    width: 100%;
    text-align: left;
    font-weight: 600;
    display: block;
    padding: .4rem .8rem;
    border-radius: 0.4rem;
    font-size: var(--fs-1-4);
}
.select-item.on ul li.on button, .select-item.on ul li:hover button{
    color: var(--blue-lev1);
}


/* QUICK LINK FIXED */
#quick-link {
    width: 5.6rem;
    position: fixed;
    position: -webkit-fixed;
    right: 2rem; bottom: 4rem;
    background: var(--gray-lev7);
    z-index: 30;
    border-radius: 10rem;
    display: flex;
    padding: 0 0 1.6rem;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    box-shadow: 2px 4px 8px rgba(218, 218, 218, 0.46);
}
#quick-link .icon-whatsapp {
    width: 100%; height: 5.2rem;
    background-image: url(../images/btn/btn_whatsapp.svg);
    background-repeat: no-repeat;
    background-position: center top;
    cursor: pointer;
}
#quick-link .icon-whatsapp a {
    width: 100%; height: 100%;
    display: block;
}
#quick-link ul {
    width:100%;
    max-height: 0rem;
    position: relative;
    display: flex;
    gap: 1.6rem;
    flex-direction: column;
    align-items: center;
    padding:1.6rem .8rem;
    background: var(--blue-lighter);
    border-radius: 10rem 10rem 0 0;
}
#quick-link ul.up {
    border-radius: 10rem 10rem 10rem 10rem;
    max-height: 50rem;
    transition: max-height 1s ease;
}

#quick-link ul.up li {
    display: block;
    transition: display 1s ease;
}

#quick-link ul li {
    width: 4rem; height: 4rem;
    display: none;
}
#quick-link ul li a {
    display: block;
    width: 100%; height: 100%;
}

#quick-link .icon-pc {
    background-image: url(../images/btn/btn_window.svg);
    background-repeat: no-repeat;
}
#quick-link .icon-adroid {
    background-image: url(../images/btn/btn_googleplay.svg);
    background-repeat: no-repeat;
}
#quick-link .icon-apple {
    background-image: url(../images/btn/btn_applestore.svg);
    background-repeat: no-repeat;
}

#quick-link ul li.icon-pc:hover  {
    background-image: url(../images/btn/btn_window_horver.svg);
    background-repeat: no-repeat;
}
#quick-link ul li.icon-adroid:hover {
    background-image: url(../images/btn/btn_googleplay_horver.svg);
    background-repeat: no-repeat;
}
#quick-link ul li.icon-apple:hover  {
    background-image: url(../images/btn/btn_applestore_horver.svg);
    background-repeat: no-repeat;
}

#footer {
    background: var(--gray-lev7);
}
.footer-top {
    width: 120rem;
    margin: 0 auto;
    padding: 4.8rem 0 3.476rem;
    display: grid;
    grid-template-columns: 1fr 1fr 23.2rem;
    grid-column-gap:10rem;
}

.footer-top address{
    font-weight: 400;
    font-size: var(--fs-1-4);
    line-height: var(--line-2);
}

.footer-top address h5 {
    font-weight: 700;
    color: var(--gray-lev2);
    font-size: var(--fs-1-6);
    margin-bottom: 0.8rem;
}

.footer-top dl{
    display: grid;
    justify-content: space-between;
    align-items: center;
    column-gap: 2.4rem;
    row-gap: 0.9rem;
    grid-auto-columns: 1fr 1fr 1fr 1fr;
}
.footer-top dl dt {
    font-weight: 700;
    color: var(--gray-lev2);
}
.footer-top dl dd {
    justify-self: center;
}
.footer-top dl dd a {
    width: 4rem; height: 4rem;

    display: block;
    align-self: center;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: contain;
}
.footer-top dl dt:nth-child(1) { grid-column: 1 / span 4; }
.footer-top dl dd:nth-child(2) { grid-column: 1; }
.footer-top dl dd:nth-child(3) { grid-column: 2; }
.footer-top dl dd:nth-child(4) { grid-column: 3; }
.footer-top dl dd:nth-child(5) { grid-column: 4; }
.footer-top dl dd.icon1 a {
    background-image: url(../images/icon/icon_facebook.svg);
}
.footer-top dl dd.icon2 a {
    width: 3.4rem; height: 3.4rem;
    background-image: url(../images/icon/icon_twitter.svg);
}
.footer-top dl dd.icon3 a {
    background-image: url(../images/icon/icon_linkidin.svg);
}
.footer-top dl dd.icon4 a {
    background-image: url(../images/icon/icon_instagram.svg);
}



.footer-bottom {
    width: 100%;
    background: var(--blue);
}
.footer-bottom-wrap {
    width: 120rem;
    height: 6.8rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.footer-bottom-wrap article {
    color: var(--white);
    font-weight: 400;
    font-size: var(--fs-1-2);
}
.footer-bottom-wrap ul {
    display: flex;
    flex-direction: row;
}

.footer-bottom-wrap ul li:last-child::after{
    content: '';
    margin: 0;
}
.footer-bottom-wrap ul li::after {
    content: '|';
    margin:0 2.4rem;
    color: var(--blue-mid)
}
.footer-bottom-wrap ul li > a {
    color: var(--white);
    font-size: var(--fs-1-2);
}

/* S:MARKET REPORT */
.grand-banner {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center 30%;
    display: flex;
    justify-content: center;
    align-items: center;
    height:36rem;
}

.grand-banner.type01 {
    background-image: url(../images/common/banner_01.png);
}

.grand-banner.type02 {
    background-image: url(../images/common/banner_02.png);
}

.grand-banner.type03 {
    background-image: url(../images/common/banner_03.png);
}
.grand-banner.type04 {
    background-image: url(../images/common/banner_04.png);
}

.grand-banner.type05 {
    background-image: url(../images/common/banner_05.png);
}
.grand-banner.type06 {
    background-image: url(../images/common/banner_06.png);
}
.grand-banner.type07 {
    background-image: url(../images/common/banner_07.png);
}
.grand-banner.type08 {
    background-image: url(../images/common/banner_08.png);
}
.grand-banner.type09 {
    background-image: url(../images/common/banner_03.png);
}
.grand-banner.info-event {
    background-image: url(../images/common/banner_info_event.png);
}

.grand-banner h2 {
    color: var(--white);
    font-weight: 700;
    font-size: var(--fs-4-8);
    text-align: center;
}

.report-list {
    margin: 4.8rem 0 5.4rem;
    padding: 3.2rem 0 0;
}
.report-list ul{
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-column-gap: 2.4rem;
    grid-row-gap: 4rem;
    grid-auto-flow:row;
}
.info-event-list-empty {
    margin: 0rem 0 5.4rem;
    margin-top: 15px;
    padding: 0 0 0;
}
.info-event-list {
    margin: 0rem 0 5.4rem;
    margin-top: -30px;
    padding: 0 0 0;
}
.info-event-list ul{
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 2.4rem;
    grid-row-gap: 4rem;
    grid-auto-flow:row;
}


.report-list li {
    border-radius: 1.6rem;
    padding: 1.6rem 2.4rem;
    background: var(--white);
    display: grid;
    grid-row-gap: 1.6rem;
    -webkit-filter: drop-shadow(2px 2px 8px var(--gray-lev5));
    filter: drop-shadow(2px 2px 8px var(--gray-lev5));
}

.info-event-list li {
    border-radius: 1.6rem;
    padding: 1.6rem 2.4rem;
    background: var(--white);
    display: inline;
    grid-row-gap: 1.6rem;
    -webkit-filter: drop-shadow(2px 2px 8px var(--gray-lev5));
    filter: drop-shadow(2px 2px 8px var(--gray-lev5));
}

.report-list li:hover {
    -webkit-filter: drop-shadow(2px 2px 8px var(--shadow));
    filter: drop-shadow(2px 2px 8px var(--shadow));
    background: var(--blue-lighter);
    transition: all .45s ease-in;
    cursor:pointer;
}
.report-list li .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.report-list li .top a {
    color: var(--gray-lev8);
}
.report-list li .top em {
    display: block;
    background: var(--pink);
    padding: .4rem .8rem;
    border-radius: .4rem;
    font-size: var(--fs-1);
    color: var(--white);
}
.report-list li .mid {
    padding: .4rem 0;
}
.info-event-list li .mid {
    padding: 1.4rem 0;
    text-align: center;
}
.report-list li .mid a {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-event-list li .mid a {
    display: inline;
    justify-content: space-between;
    align-items: center;
}

.report-list li .mid em {
    content: ">";
    width:.6rem; height: 1.2rem;
    display: block;
    color: var(--blue);
}
.report-list li .mid p {
    width: 20rem;
    word-break: keep-all;
    font-weight: 700;
    line-height: var(--line-2-2);
    font-size: var(--fs-1-6);
    color: var(--blue);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
}
.report-list li .mid a {
    position: relative;
}


.report-list li .mid a::after{
    content:'';
    position: absolute;
    right: 0; top: 50%;
    margin-top: -.3rem;
    width: .6rem; height: .6rem;
    border: solid var(--blue);
    border-width: .1rem .1rem 0 0;
    transform: rotate(45deg);
}
.info-event-list li .mid a::after{
    content:'';
    position: absolute;
    right: 0; top: 50%;
    margin-top: -.3rem;
    width: 0; height: 0;
    border: solid var(--blue);
    border-width: 0;
    transform: rotate(45deg);
}
.info-event-list li .mid {
    padding: 2.4rem 0;
    text-align: center;
}
.info-event-list li .mid p {
    font-size: 2.4rem;
}
.info-event-image {
    border: none;
}
.report-list li .bottom {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.report-list li .bottom a {
    color: var(--gray-lev8);
}
.report-list li .bottom a i {
    font-style: normal;
}
.report-list li .bottom a.download {
    color: var(--gray-lev3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-decoration: underline;
}
.report-list li .bottom a.download::before {
    content: '';
    display: block;
    width: 2.4rem; height: 2.4rem;
    margin-right: .4rem;
    background-image: url(../images/icon/icon_PDFDownload.svg);
    background-position: center center;
    background-repeat: no-repeat;

}
.report-list li:hover > .bottom a.download {
    color: var(--pink);
    transition: all .65s ease-in;
}
.report-list li:hover > .mid a::after {
    border: solid var(--pink);
    border-width: .1rem .1rem 0 0;
    transition: all .65s ease-in;
}

.btn-viewmore {
    text-align: center;
    margin-bottom: 6.2rem;
}
.btn-viewmore button {
    height: 2rem;
    position: relative;
    color: var(--blue);
    font-size: var(--fs-1-6);
    font-weight: 600;
    padding: 0 1.4rem 0 0;
}

.btn-viewmore button::before {
    content:'';
    position: absolute;
    right: 0; top: .25rem;
    width: .5rem; height: .5rem;
    border: solid var(--blue);
    border-width: .1rem .1rem 0 0;
    transform: rotate(132deg);
}
.btn-viewmore button::after {
    content:'';
    position: absolute;
    right: 0; bottom: .5rem;
    width: .5rem; height: .5rem;
    border: solid var(--blue);
    border-width: .1rem .1rem 0 0;
    transform: rotate(132deg);
}

.btn-viewmore button:hover {
    color: var(--pink);
}
.btn-viewmore button:hover::before,
.btn-viewmore button:hover::after {
    border: solid var(--pink);
    border-width: .1rem .1rem 0 0;
}

.search-bar {
    width: 94rem; height: 6.4rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    left: 50%; top: -8rem;
    margin-left: -47rem;
    z-index:10;
}

.search-bar .search-date {
    width: 34.9%; height: 100%;
    padding: .8rem; background: var(--white);
    border-radius: 10rem;
    display: flex; gap:.4rem;
    flex-direction: row;
    -webkit-filter: drop-shadow(2px 2px 8px var(--gray-lev4));
    filter: drop-shadow(2px 2px 8px var(--gray-lev4));
}

.search-bar .search-date .search-data-half {
    width: 50%; height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0 1.2rem;
    gap: .4rem;
    justify-content: space-between;
}

.search-bar .search-date .search-data-half .txt {
    color: var(--gray-lev9);
}
.search-bar .search-date .search-data-half label {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

}
.search-bar .search-date .search-data-half label input {
    width: 100%;
}
.search-bar .search-date .search-data-half label::after {
    content: '';
    display: block;
    width: 2.4rem; height: 2.4rem;
    background-image: url(../images/icon/icon-calendar.svg);
    background-position: center bottom;
    background-repeat: no-repeat;
}

.search-bar .search-info {
    width: 61.1%; height: 100%;
    padding: .8rem 1.6rem;
    background: var(--white);
    border-radius: 10rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    -webkit-filter: drop-shadow(2px 2px 8px var(--gray-lev4));
    filter: drop-shadow(2px 2px 8px var(--gray-lev4));
}

.search-select {
    width: 16rem;
    padding-right: 1.2rem;
    border-right: .1rem solid var(--gray-lev6);
}
.search-select button.target {
    width: 100%;
    text-align: left;
}
.search-select .target[type=button]::after {
    width: .6rem ; height: .6rem ;
    right: .6rem ; margin-top: -.4rem ;
    top: 50%;
    border-bottom: 0;
    border-right: .1rem solid var(--blue);
    border-left: 0;
    border-top: .1rem solid var(--blue);
    transform: rotate(135deg);
}

.search-select.on .target[type=button]::after,
.search-select.on .target:hover::after {
    transform: rotate(317deg);
    margin-top: -.3rem ;
}

.search-box {
    margin-left: .8rem;
    width: 30rem;
}

.search-inp {
    width: 100%;
    display: inline-flex;
    align-items: center;
}
.search-inp::before {
    content: '';
    display: block;
    width: 2.4rem; height: 2.4rem;
    background-image: url(../images/icon/icon-search_01.svg);
    background-position: left center;
}
.search-inp label {
    width: 100%;
    margin-left: 1.2rem;
}
.search-inp label input { width: 100%;}

.title-report {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: end;
    margin: 10.2rem 0 4rem;
}
.title-report .left h1 {
    width: 100rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 600;
    font-size: var(--fs-2-4);
    color: var(--black);
    margin-bottom: .8rem;
}
.title-report .left span {
    color: var(--gray-lev8)
}
.title-report .right a {
    text-decoration: underline;
}
.title-report .right a:hover {
    color: var(--pink);
}
.title-report .right a::before {
    content: ''; display: inline-block;
    width: 1.4rem; height: 1.4rem;
    margin-right: .9rem;
    background-image: url(../images/icon/icon_download.svg);
    background-repeat: no-repeat;
    background-position: center center;
}

.viewer-pdf {
    width: 120rem;
    margin: 0 auto 4rem;
}
/* E:MARKET REPORT */

/* Daily News View */
.round-view {
    width: 99.6rem;
    margin: 10.2rem auto 4rem;
    background: var(--white);
    border-radius: 1.6rem;
    overflow: hidden;
}
.title-news {
    width: 100%;
    height: 8.4rem;
    padding: 0 7.8rem;
    background: var(--gray-lev7);
    display: flex;
    justify-content: space-between;
    align-items: center;

}

.title-news h1 {

    white-space:wrap;
    line-height: var(--line-3-2);
    font-size: var(--fs-2-4);
    color: var(--black);
    font-weight: 600;
}
.title-news .date {
    display: flex;
    align-items: center;

}
.title-news .date span,
.title-news .date em {
    font-size: var(--fs-1-6)
}
.title-news .date span {
    display: inline-flex;
    align-items: center;
}
.title-news .date span::before {
    content: '';
    display: block;
    width: 2.4rem; height: 2.4rem;
    background-image: url(../images/icon/icon-calendar.svg);
}
.title-news .date em {
    margin-left: 2.4rem;
}

.round-content {
    padding: 2.4rem 7.8rem;
}
.round-content dt .header-area {
    display: grid;
    grid-template-columns: 12.7rem 1fr;
    grid-template-rows: 4rem 4rem ;
    gap: 1.6rem;

}
.round-content dt .header-area p.logo {
    grid-column: 1; grid-row: 1 / span 2;
}
.round-content dt .header-area p.sub-tit {
    grid-column: 2; grid-row: 2;align-self: flex-start;
}
.round-content dt .header-area h2 {
    grid-column: 2; grid-row: 1;align-self: flex-start;
}
.round-content dt .header-area .logo {
    max-width: 12.7rem;
}

.round-content dt .header-area h2 {
    font-weight: 600;
    font-size: var(--fs-2-4);
    color: var(--gray-lev8);
}
.round-content dt .header-area p.sub-tit {
    font-size: var(--fs-1-8);
    color: var(--gray-lev8);
}
.round-content dd {
    padding-bottom: 4.2rem;
    margin-bottom: 4.2rem;
    border-bottom: .1rem solid var(--gray-lev6);
}
.round-content dd p {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
    line-height: var(--line-2-2);
    margin-bottom: 2.2rem;
}
.round-content dd .source {
    font-size: var(--fs-1-8);
    color: var(--gray-lev8);
}
.round-content dd .source a {
    color: var(--blue-lev4);
}
.round-content dd img {
    width: 100%;
}

.max-lines {
	display: inline-block; /* Set display to inline-block */
	width:15rem; /* Set the desired width */
	white-space: nowrap; /* Prevent line breaks */
	overflow: hidden; /* Hide overflowing text */
	text-overflow: ellipsis; /* Show ellipsis (...) */
}

.max-lines-detail {
    display: inline-block;
    width:15rem; /* Set the desired width */
    white-space: nowrap;
    overflow: hidden;
    text-align: right;
    text-overflow: ellipsis;
}

.list-navi {
    display: flex;
    align-items: flex-start;
    flex-direction: row-reverse;
    justify-content: space-between;
    padding-bottom: 1rem;
}

.list-navi a:nth-child(2) {
    text-align: right;
}
.list-navi a:nth-child(1):hover,
.list-navi a:nth-child(2):hover {
    color: var(--pink) !important;
}
.list-view-btn {
    text-align: center;
    margin-bottom: 5.4rem;
}
.list-view-btn .btn {
    width: 14.3rem;
}
.content-tit {
    text-align: center;
    margin: 8rem 0 6.4rem;
    font-size: var(--fs-4-8);
    color: var(--blue);
    font-weight: 700;
    line-height: var(--line-4-6);
}
.content-tit.hts-tit {
    text-align: center;
    margin: 8rem 0 6.4rem;
    font-size: var(--fs-4-8);
    color: var(--white);
    font-weight: 700;
    line-height: var(--line-4-6);
}
.content-tit.hts-tit h4 {
    font-size: var(--fs-2-4);
    line-height: var(--line-3-2);
    font-weight: 400;
    margin-top: 2.4rem;
}
.content-tit.hero-title-mts {
    margin-top: 2.5rem;
    line-height: var(--line-3-2);
}
.content-tit.hero-title-mts span {
    font-size: var(--fs-2-4);

}
.content-tit p {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
    margin-top: 1.8rem;
}
.card-item-list {
    width: 79.2rem;
    margin: 0 auto 19.3rem;
    display: grid;
    grid-template-rows: 1fr 1fr 1fr;
    gap:5rem;

}
.card-item-list .card-item .top {
    width:100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 1.6rem;
    margin-bottom: 2.4rem;
}

.card-item-list .card-item .top .icon1 {
    background-image: url(../images/icon/icon_openaccount_01.svg);
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
}
.card-item-list .card-item .top .icon2 {
    background-image: url(../images/icon/icon_openaccount_02.svg);
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
}
.card-item-list .card-item .top .icon3 {
    background-image: url(../images/icon/icon_openaccount_03.svg);
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
}
.card-item-list .card-item .top span {
    display: block;
    width: 4.8rem; height: 4.8rem;
}

.card-item-list .card-item .top p {

    font-weight: 600;
    font-size: var(--fs-2-4);
    color: var(--blue);
    line-height: var(--line-3-2);
}
.card-item-list .card-item .bottom {
    width: 100%;
    padding: 2.4rem 4rem;
    background-color: var(--gray-lev13);
    border-radius: .8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.card-item-list .card-item .bottom .text-wrap {
    width: 33.6rem;
}
.card-item-list .card-item .bottom .openAccount-icon1 {
    width: 33.6rem; height: 16rem;
    background-image: url(../images/icon/icon_openaccount_10.svg);
    background-repeat: no-repeat;
    background-position: center center;
}
.card-item-list .card-item .bottom .openAccount-icon2 {
    width: 33.6rem; height: 16rem;
    background-image: url(../images/icon/icon_openaccount_11.svg);
    background-repeat: no-repeat;
    background-position: center center;
}


.card-item-list .card-item .bottom .icon-group {
    width: 33.6rem; display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    justify-items: center;
    gap: .9rem;
    align-items: center;
}
.card-item-list .card-item .icon-group .icon1 {
    grid-column: 1/ span 1;

}
.card-item-list .card-item .icon-group .icon3 {
    grid-column: 1/ span 1;

}
.arrow-list-blue li{
    padding-left: 2.4rem;
    background-image: url(../images/icon/icon_arrow_blue.svg);
    background-repeat: no-repeat;
    background-position: .6rem .9rem;
    font-size: var(--fs-1-6); line-height: var(--line-2-2);
}
.arrow-list-blue li em {
    color: var(--pink-high);
}

.arrow-list-pink li{
    padding-left: 2.4rem;
    background-image: url(../images/icon/icon_arrow_pink.svg);
    background-repeat: no-repeat;
    background-position: .6rem .9rem;
    line-height: var(--line-2-8);
    font-size: var(--fs-1-6);
}

.arrow-list-pink li em {
    color: var(--pink-high);
}
.arrow-list-pink2 li {
    line-height: var(--line-2-8);
    font-size: var(--fs-1-6);
    display: flex;
    align-items: center;
    justify-content: center;
}
.arrow-list-pink2 li::before{
    content: '';
    width: 1.6rem; height: 1.6rem;
    background-image: url(../images/icon/icon_arrow_pink.svg);
    background-repeat: no-repeat;
    background-position: center center;
}


.arrow-list-pink-round li {
    display: flex;
    align-items: flex-start;
}
.arrow-list-pink-round li::before {
    content: '';
    width: 1.2rem; height: 1.2rem;
    display: block;
    margin: 1rem 1rem 0 0;
    background: var(--white);
    border-radius: 200%;
    background-image: url(../images/icon/icon_arrow_pink.svg);
    background-repeat: no-repeat;
    background-position: center center;
}
.arrow-list-pink-round li{
    color: var(--white);
    line-height: var(--line-2-8);
    font-size: var(--fs-1-6);
    margin-bottom: 2rem;
}

.arrow-list-gray-round li {
    display: grid;
    grid-auto-flow: column;
    grid-template-columns: 2rem 1fr;
    align-items: flex-start;
}
.arrow-list-gray-round li::before {
    content: '';
    width: 1.2rem; height: 1.2rem;
    display: inline-block;
    margin: 1rem 1rem 0 0;
    background: var(--gray-lev6);
    border-radius: 100%;
    background-image: url(../images/icon/icon_arrow_gray.svg);
    background-repeat: no-repeat;
    background-position: center center;
}
.arrow-list-gray-round li{
    color: var(--black-lev2);
    line-height: var(--line-2-8);
    font-size: var(--fs-1-6);
    margin-bottom: .8rem;
}
.arrow-list-gray-round li i {
    display: contents;
    font-style: normal;
}




.caption-list {
    margin-top: 2.4rem;
}

.caption-list li {
    font-size: var(--fs-1-6);
    font-style: italic;
    line-height: var(--line-2-2);
    display: flex;
}
.caption-list li::before {
    content: '*';
    width: 2rem;
    color:var(--pink-high);
    display: block;
}
.caption-list li:nth-child(2)::before {
    content: '**';
}

.opening-step {
    width: 102rem;
    margin: 0 auto;

}
.opening-step ul {
    display: flex;
    flex-direction: row;
    justify-items: center;
    align-items: center;
}
.opening-step li {
    max-width: 20.9rem;
}
.opening-step li::before {
    content:'';
    width: 100%; height: 9.2rem;
    display: block;
    background-repeat: no-repeat;
    background-position: center center;
}
.opening-step li:nth-child(1)::before {
    background-image: url(../images/icon/icon_openaccount_04.svg);
}
.opening-step li .top {
    text-align: center;
    margin-bottom: .8rem;
}
.opening-step li .top i {
    display: inline-block;
    padding: 0 .6rem .3rem;
    border-bottom: .2rem solid var(--blue-lighter);
    font-style: normal; font-size: var(--fs-1-2);
    font-weight: 600; color: var(--blue);
}
.opening-step li .bottom {
    color:var(--gray-lev10);
}

.opening-step li .bottom strong {
    display: block;
    font-weight: 700;
    color: var(--black);
    font-size: var(--fs-1-8);
    text-align: center;
    margin-bottom: .8rem;
}


.ui-datepicker-inline {
    position: absolute;
    left: 0; top: 3rem;
    font-weight: 700;
    color: var(--blue);
}
.grand-banner.type03 h2{
    display: none;
}

.profile {
    width: 99.6rem;
    margin: -14.4rem auto 13rem;
    background: var(--white);
    border-radius: 2.4rem;
    position: relative;
    box-shadow: 6px -6px 16px 4px rgba(212, 213, 215, 0.4);
    padding: 4.7rem 0 2rem;
}

.profile h2 {
    font-weight: 700;
    font-size: var(--fs-3-8);
    color: var(--blue);
    text-align: center;
    margin: 0rem 0 3.2rem 0;
}

.profile h4 {
    width: 28.5rem; height: 3.5rem;
    background-image: url(../images/common/bg_overview.svg);
    font-weight: 700;
    font-size: var(--fs-1-6);
    color: var(--white);
    text-align: center;
    line-height: 3.5rem;
    margin: 0 auto;
}

.profile-box .profile-box-wrap{
    margin: 0 10.2rem 5.8rem;
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    border: solid var(--gray-lev6);
    border-width: .1rem .1rem .1rem .1rem;
    border-radius: 1.6rem;
}
.profile-box .col1 {
    grid-row: 1;
    border: solid var(--gray-lev6);
    border-width: 0 .1rem .1rem 0;
}

.profile-box .col2 {
    grid-row: 1;
    border: solid var(--gray-lev6);
    border-width: 0 0rem .1rem 0;
}
.profile-box .col3 {
    grid-row: 1;
    border: solid var(--gray-lev6);
    border-width: 0 0 .1rem 0;
}

.profile-box .col4 {
    grid-column: 2/1;
    grid-row: 2;
    border: solid var(--gray-lev6);
    border-width: 0 .1rem .1rem 0;

}

.profile-box .col5 {
    border: solid var(--gray-lev6);
    border-width: 0 0 .1rem 0;
    grid-column: 2/span 2;
    grid-row: 2;
}
.profile-box .col6 {
    grid-column: 1/span 3;
    border: solid var(--gray-lev6);
    border-width: 0 0 .1rem 0;
    grid-row: 3;
}
.profile-box .arrow-list-pink li {
    font-size: var(--fs-1-8);
    background-position: 0.6rem 1rem;
}
.profile-box .col7 {
    grid-column: 1/span 3;
    grid-row: 4;
    border: solid var(--gray-lev6);
    border-width: 0 0 .1rem 0;
}
.profile-box .col7 .table {
    border-radius: .8rem;
    overflow:hidden;
    margin: .8rem 0 0;
}
.profile-box .col7 .mod-pc {
    display: block;
}
.profile-box .col7 .mod-mo {
    display: none;
}

.profile-box .col7 p {
    text-align: right;
    margin-top: 1.2rem;
    font-size: var(--fs-1-4);
    color: var(--gray-lev10);
    line-height: var(--line-1-4);
}
.profile-box .col7 .mod-pc tr th {
    height: 5.6rem;
    font-size: var(--fs-1-8);
    line-height: var(--line-2-4);
    color: var(--white);
    border: solid var(--white);
    border-width: 0 0 0 .1rem;
}
.profile-box .col7 .mod-pc tr th:nth-child(even) {
    background: var(--blue-mid);
}
.profile-box .col7 .mod-pc tr th:nth-child(odd) {
    background: var(--blue);
}
.profile-box .col7 .mod-pc tr th:first-child {
    background: var(--gray-lev6);
    border: 0;
}
.profile-box .col7 .mod-pc tbody tr:nth-child(even) {
    background: var(--gray-lev6);
}
.profile-box .col7 .mod-pc tbody tr:nth-child(odd) {
    background: var(--gray-lev7);
}
.profile-box .col7 .mod-pc tbody tr td:first-child {
    border: solid var(--white);
    border-width: 0 0 .1rem 0rem;
}
.profile-box .col7 .mod-pc tbody tr:last-child td {
    border: solid var(--white);
    border-width: 0 0 0rem .1rem;
}
.profile-box .col7 .mod-pc tbody td {
    height: 5.6rem;
    font-size: var(--fs-1-8);
    line-height: var(--line-2-4);
    font-weight: 400;
    text-align: center;
    color: var(--black-lev5);
    border: solid var(--white);
    border-width: 0 0 .1rem .1rem;
}
.profile-box .col8 {
    grid-column: 1/span 3;
    grid-row: 5;
}


.profile-box .column {
    padding: 1.6rem;
    font-size: var(--fs-1-6);
    color: var(--gray-lev9);
}
.profile-box .column .tit-txt {
    display: block;
    margin-bottom: .8rem ;
    font-size: var(--fs-1-6);
    line-height: var(--line-2-2);
}

.profile-box .column .con-txt {
    line-height: var(--line-2-8);
    color: var(--black);
    font-weight: 600;
    font-size: var(--fs-1-8);
}

.pdf-viewer-height{
    height: 145rem;
}
.management-list {
    width: 100%;
    overflow: visible !important;
}

.management-list li {
    width: 34.8rem;
    cursor: pointer;
}
.management-list li .wrap {
    border-radius: 1.6rem;
    background: var(--white);
    padding: 1.6rem 1.6rem 2.8rem;
    margin: 0 10px 10px 10px;
}
.management-list li img {
    width:100%;
    filter:grayscale(100%);
}

.management-list li:hover img {
    filter:grayscale(0%);
}
.management-list li:hover .wrap {
    box-shadow: 2px 2px 8px  var(--gray-lev5);
}
.management-list li .top {
    margin-bottom: 1.6rem;
}
.management-list li .bottom h5 {
    font-size: var(--fs-2-8);
    line-height: var(--line-4);
    color: var(--black-lev1);
    margin-bottom: .4rem;
    font-weight: 600;
}
.management-list li .bottom span {
    color: var(--black-lev2);
    display: block;
}
.management-list li .bottom p {
    color: var(--gray-lev8);
    font-size: var(--fs-1-6);
    margin-top: 1.6rem;
}

.about-future {
    width: 100%; height:44rem;
    position: relative;
    display: grid;
    margin: 8.4rem auto 5.8rem;
    grid-template-columns: 1fr 1fr;
}

.about-future .left {
    width:100%;
    position: relative;
    background: var(--blue-lighter);
}
.about-future::after {
    content: '';
    width: 39.5rem; height: 25.2rem;
    background-image: url(../images/icon/icon_future.svg);
    position: absolute;
    right: 50%; top: 26.6rem;
    margin-right: -10rem;
}


.about-future .left span {
    position: absolute;
    font-weight: 600;
    left:12rem; top: 7rem;
    color: var(--blue-light);
    font-size: var(--fs-2-8);
}
.about-future .left div {
    position: absolute;
    left: 12rem; top: 11rem;
}
.about-future .left div p {
    font-weight: 700;
    font-size: var(--fs-3-8);
    line-height: var( --line-4-6);
}
.about-future .left div p:nth-child(1),
.about-future .left div p:nth-child(4){
    color: var(--blue);
}
.about-future .left div p:nth-child(2),
.about-future .left div p:nth-child(3) {
    color: var(--pink);
}

.about-future .left::after {
    content: '';
    width: 7.5rem; height: 6.6rem;
    background-image: url(../images/icon/icon_vision.svg);
    position: absolute;
    left: 41.3rem; top: 10rem;
}
.about-future .right {
    width: 100%;
    position: relative;
    background: var(--blue-mid);
}
.about-future .right span {
    position: absolute;
    left:17.9rem; top: 7rem;
    color: var(--blue-lighter);
    font-size: var(--fs-2-4);
}
.about-future .right ul {
    position: absolute;
    left: 17.9rem; top: 12.6rem;
}
.core-value {
    margin-bottom: 12.2rem;
}

.core-value h3 {
    color: var(--blue-light);
    font-size: var(--fs-2-4);
}
.core-value ul {
    display: grid;
    grid-template-columns:1fr 1fr 1fr 1fr;
    margin-top: 6.6rem;
}
.core-value li {
    width: 28.2rem;
    padding: 1.6rem;
}
.core-value li::before {
    content: '';
    display: block;
    height: 6rem; margin-bottom: 2.5rem;
    background-repeat: no-repeat;
    background-position: left center;
}
.core-value li:nth-child(1):before {
    background-image: url(../images/icon/icon_target.svg);

}
.core-value li:nth-child(2):before {
    background-image: url(../images/icon/icon_CoreValue_02.svg);
}
.core-value li:nth-child(3):before {
    background-image: url(../images/icon/icon_CoreValue_03.svg);
}
.core-value li:nth-child(4):before {
    background-image: url(../images/icon/icon_CoreValue_04.svg);
}

.core-value li h5 {
    color: var(--black);
    font-size: var(--fs-2-4);
    line-height: var(--line-3-2);
    margin-bottom: 1.5rem;
    font-weight: 600;
}
.core-value li p {
    font-size: var(--fs-1-6);
    color: var(--gray-lev8);
    line-height: var(--fs-1-8);
}
.history-year {
    width: 120rem;
    position: relative;
    margin-bottom: 6.4rem;
}
.history-year::after {
    content: '';
    position: absolute;
    left: 0; bottom: .8rem;
    display: block;
    width: 100%; height: .1rem;
    background: var(--gray-lev6);
}
.history-year li {
    display: grid;
    grid-template-rows: 1fr 1.6rem;
    gap:2.2rem;
    align-items: center;
    justify-items: center;
    cursor: pointer;
}
.history-year li.swiper-slide-thumb-active div:nth-child(1){
    color: var(--pink);
    font-size: var(--fs-3-2);
    font-weight: 700;
}
.history-year li.swiper-slide-thumb-active div:nth-child(2){
    width: 1.6rem; height: 1.6rem;
    background: var(--white);
    border-radius: 100%;
    border: .2rem solid var(--pink);
}
.history-year li.swiper-slide-thumb-active div:nth-child(2)::after{
    width: .8rem; height: .8rem;
    background: var(--pink);
    position: absolute;
    content: ''; display: block;
    left: 0.20rem; top: 0.2rem;
    border-radius: 100%;
}

.history-year li div:nth-child(1){
    font-size: var(--fs-2-4);
    color: var(--gray-lev9);
    line-height:  var(--line-3-2);
    text-align: center;
}
.history-year li div:nth-child(2){
    width: 1.2rem; height: 1.2rem;
    background: var(--gray-lev6);
    border-radius: 100%;
    position: relative;
}

.history-year-contents li.swiper-slide {
    padding: 0 10.2rem 0 6.8rem;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 40.8rem;
    justify-self: center;
    align-items: center;
}
.history-year-contents li.swiper-slide .year-content {
    display: flex;
    align-items: center;
    justify-content: center;
}


.history-year-contents .year-content strong {
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--black-lev3);
}
.history-year-contents .year-content p {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
}
.history-year-contents li.swiper-slide ul{
    width: 48.6rem;
    background: var(--white);
    box-shadow: 2px 2px 8px var(--gray-lev5);
    border-radius: 1.6rem;
    padding: 1.6rem;
    display: inline-block;
}
.history-year-contents li.swiper-slide li {
    margin: 1rem 0 1rem;
}

.history-nav {
    width: 59rem;
    position: absolute;
    left: 8.4rem; top: 19.9rem;
    z-index: 15;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.comprehensiveFeature-nav span,
.history-nav span {
    width: 4rem; height: 4rem;
    display: block; cursor:pointer;
    background-repeat: no-repeat;

}
.comprehensiveFeature-nav .prev.swiper-button-disabled,
.comprehensiveFeature-nav .prev{
    background-image: url(../images/btn/btn_left_active_01.svg);
}
.history-nav .history-prev {
    background-image: url(../images/btn/btn_left_active.svg);
}
.comprehensiveFeature-nav .next.swiper-button-disabled,
.comprehensiveFeature-nav .next{
    background-image: url(../images/btn/btn_right_active_01.svg);
}
.history-nav .history-next {
    background-image: url(../images/btn/btn_right_active.svg);
}

.history-nav .history-prev.swiper-button-disabled {
    background-image: url(../images/btn/btn_left_deactive.svg);
}

.history-nav .history-next.swiper-button-disabled {
    background-image: url(../images/btn/btn_right_deactive.svg);
}


.core-business-content li.swiper-slide {
    display: flex; gap:12.8rem;
    padding: 2rem 0;
    justify-content: space-between;
}
.core-business-content .content {
    align-self: center;
}
.core-business-content .content .tit {
    font-size: var(--fs-1-8);
    color: var(--blue-mid);
    font-weight: 600;
    margin: 1.6rem 0 1rem;
}
.core-business-tab {
    height: 19rem;
    border-bottom: .1rem solid var(--gray-lev6);
}
.core-business-tab li {
    padding: 3.4rem 3.4rem 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}
.core-business-tab li .icon {
    width: 6rem; height: 6rem;
    margin-bottom: 1.6rem;

}
.core-business-tab li .icon1 {
    background-image: url(../images/icon/icon_coreBusiness_01.svg);
}
.core-business-tab li .icon2 {
    background-image: url(../images/icon/icon_coreBusiness_02.svg);
}
.core-business-tab li .icon3 {
    background-image: url(../images/icon/icon_coreBusiness_03.svg);
}
.core-business-tab li .icon4 {
    background-image: url(../images/icon/icon_coreBusiness_04.svg);
}
.core-business-tab li .icon5 {
    background-image: url(../images/icon/icon_coreBusiness_05.svg);
}
.core-business-tab li .top-tit {
    width: 100%; height: 6.4rem;
    text-align: center;
    align-self: self-start;
    line-height: var(--line-3-2);
    font-size: var(--fs-1-8);
    color: var(--black-lev1);
    font-weight: 600;
    margin-bottom: 1.6rem;
    position: relative;
}

.core-business-tab li.swiper-slide-thumb-active .top-tit::after {
    content: '';
    display: block;
    left: 0; bottom:-1.6rem;
    position: absolute;
    height: .4rem;
    background: var(--blue);
    animation-duration: .25s;
    animation-name: widthfull;
    animation-fill-mode: forwards;
}

.why-us {
    margin-bottom: 9.6rem;
}
.why-us ul {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 4.9rem;
}

.why-us ul li {
    width: 36.8rem;
    padding: 4rem;
    border-radius: 1.6rem;
}
.why-us ul li .wrap {
    background: var(--white);
    padding: 1.6rem;
    width: 100%; height: 100%;
    border-radius:1.6rem;
    box-shadow: 2px 4px 8px rgba(218, 218, 218, 0.46);
    display: flex;
    flex-direction: column;
}
.why-us ul li .wrap h6 {
    font-size: var(--fs-2);
    color: var(--blue-mid);
    line-height:var(--line-2-4);
    margin:2.5rem 0 1.6rem;
}
.why-us ul li .wrap div {
    font-size: var(--fs-1-6);

}
.why-us ul li:nth-child(1){
    background: var(--pink-lighter);
}
.why-us ul li:nth-child(2){
    background: var(--blue-lighter);
}
.why-us ul li:nth-child(3){
    background: var(--gray-lev6);
}

.why-us ul li .wrap::before {
    content: '';
    width: 6rem; height: 6rem;

    background-repeat: no-repeat;
}
.why-us ul li:nth-child(1) .wrap::before {
    background-image: url(../images/icon/icon_whyUs_01.svg);
}
.why-us ul li:nth-child(2) .wrap::before {
    background-image: url(../images/icon/icon_whyUs_02.svg);
}
.why-us ul li:nth-child(3) .wrap::before {
    background-image: url(../images/icon/icon_whyUs_03.svg);
}
/* CORPORATE GOVERNANCE */
.corporate-board {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

}

.corporate-board .left {
    width: 54rem; height: 51.4rem;
    position: relative;
    padding: 15rem 5.6rem 5.9rem;
    background: var(--white);
    border-radius: 2.4rem;
    box-shadow: 2px 2px 8px #D1D2D4;
}

.corporate-board .top h3 {
    padding: 1rem 4rem;
    color: var(--white);
    border-radius: 10rem;
    font-size: var(--fs-2-4);
    font-weight: 600;
    background: var(--blue);
    margin-bottom: 1.6rem;
    display: inline-block;
}
.corporate-board .top p {
    font-size: var(--fs-2);
    color: var(--black-lev3);
    line-height: var(--line-2-4);

}
.corporate-board .top {
    width: 38.4rem;
    position: absolute;
    left: 50%; top: -2rem;
    text-align: center;
    margin: 0 0 0 -19.2rem;
}
.corporate-board .bottom li {
    display: grid;
    grid-template-columns: 4.8rem 1fr;
    gap: 2.4rem;
    padding-bottom: 2.6rem;
    margin-bottom: 2.6rem;
    border-bottom: .1rem solid var(--gray-lev6);
    align-items: center;
}
.corporate-board .bottom li:last-child {
    border-bottom: 0 !important;
}
.corporate-board .bottom li span {
    width: 4.8rem; height: 4.8rem;
    background: var(--blue-lighter);
    border-radius: 100%;
    display:flex;
    justify-content: center;
    align-items: center;
    color: var(--blue);
    font-size: var(--fs-2-4);
    font-weight: 600;
}
.corporate-board .bottom li p {
    font-size: var(--fs-1-6);
    letter-spacing: 0.02rem;
    line-height: var(--line-2-2);
}
.corporate-board .right {
    width: 54rem; height: 51.4rem;
    position: relative;
    padding: 11rem 5.6rem 3.8rem;
    background: var(--white);
    border-radius: 2.4rem;
    box-shadow: 2px 2px 8px #D1D2D4;
}
.corporate-board .right .bottom li {
    display: grid;
    grid-template-columns: 4.8rem 1fr;
    gap: 2.4rem;
    padding-bottom: 1rem;
    margin-bottom: .8rem;
    border-bottom: .1rem solid var(--gray-lev6);
}
.corporate-board .right .top h3 {
    background: var(--pink-high);
}
.corporate-board .right .bottom span {
    color: var(--pink-high);
    background: var(--pink-lighter);
}
.codeOfEthics {
    background: var(--blue-lighter);
    padding: 6.8rem 0 12.5rem;
    margin: 16.5rem 0 0;
}
.codeOfEthics .top {
    margin: 0 auto;
    text-align: center;
}
.codeOfEthics .top h4 {
    font-size: var(--fs-4-8);
    font-weight: 700;
    line-height: var( --line-4-6);
    color: var(--blue);
    margin-bottom: 2.4rem;
}
.codeOfEthics .top p {
    margin: 0 auto;
    max-width: 55%;
    font-size: var(--fs-1-8);
    color: var(--black-lev3);
    font-weight: 400;
    line-height: var(--line-2-8);
}
.codeOfEthics .mid {
    margin: 5.7rem 0 0;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    display: flex;
}
.codeOfEthics .mid .txt {
    max-width: 52%;
    text-align: center;
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--blue);
}
.codeOfEthics .mid ol {
    display: flex;
    flex-direction: row;
    gap: 4rem;
    margin-top: 3.5rem;
}
.codeOfEthics .mid ol li {
    width: 20.8rem; height: 19rem;
    background: var(--white);
    border-radius: 1.6rem;
    padding: 1.6rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.codeOfEthics .mid ol li .icon {
    margin: 0 0 1.8rem;
}
.codeOfEthics .mid ol li .num {
    text-align: center;
}
.codeOfEthics .mid ol li .num span {
    display: inline-block;
    font-size: var(--fs-2-4);
    line-height: 2.4rem;
    color: var(--blue);
    padding: 0 0rem .3rem;
    margin-bottom: .8rem;
    border-bottom: .2rem solid var(--blue-light);
}
.codeOfEthics .mid ol li .num p {
    font-size: var(--fs-2-4);
    font-weight: 600;
    line-height: var(--line-3-2);
    color: var(--black-lev1);
}
.codeOfEthics .bottom {
    max-width: 60%;
    text-align: center;
    margin: 7.5rem auto 0;
    font-size: var(--fs-1-8);
    font-weight: 600;
    line-height: var(--line-2-8);
    color: var(--black-lev3);
}

.riskManagementPolicy {
    background: url(../images/common/bg_RiskManagementPolicy.png);
    background-size: cover;
    background-repeat: no-repeat;
}
.riskManagementPolicy .wrap {
    width: 99.5rem;
    margin: 0 auto;
    padding: 7.4rem 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.riskManagementPolicy h3 {
    width: 30.5rem;
}
.riskManagementPolicy h3 .sub-box {
    padding: 1.6rem;
    margin-top: 4rem;
    border-radius: 1.6rem;
    background: var(--blue);
    color: var(--white);
    line-height: var(--line-2-2);

}
.riskManagementPolicy h3 span {
    font-size: var(--fs-3-8);
    line-height: var(--line-4-6);
    font-weight: 700;
    color: var(--white);
    display: block;
}
.riskManagementPolicy .list-area {
    width: 61.6rem;

}
.riskManagementPolicy .list-area ol {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4.8rem;
}
.riskManagementPolicy .list-area p {
    color: var(--white);
    font-size: var(--fs-1-6);
    margin-top: .8rem;
}
.riskManagementPolicy .list-area span {
    display: inline-block;
    font-size: var(--fs-2-4);
    line-height: var(--line-2-6);
    color: var(--white);
    padding-bottom: .4rem;
    border-bottom: .2rem solid var(--blue-light);
}

.gray-bg-pc {
    width: 100%;
    background-color: var(--gray-lev7);
}

.sub-txt-01 {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
    margin: 0 auto;
    width: 70%;
}

.content-box-01  {
    display: flex;
    justify-content: space-between;
    align-items: start;
    flex-direction: row;
    margin-bottom: 2.4rem;
}
.content-box-01 .contenta-area {
    max-width: 50%;
}
.content-box-01 .contenta-area h4 {
    font-size: var(--fs-2-4);
    color: var(--blue-mid);
    font-weight: 600;
    margin-bottom: 2.4rem;
}
.content-box-01 .contenta-area p {
    font-size: var(--fs-1-6);
    line-height: var(--line-2-2);
    color: var(--gray-lev10);
    margin-bottom: 2.2rem;
}
.content-box-01 .img-area {
   width: 48.6rem;
   display: block;
}
.content-box-01 .img-area img {
    width: 100%;
}



.complianceAndInternalAuditPolicy {
    margin: 6.4rem 0 11.6rem;

}

.complianceAndInternalAuditPolicy ol {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2.4rem;
}
.complianceAndInternalAuditPolicy li {
    display: flex;
    width: 38.4rem;
    padding: 1.6rem;
    flex-direction: column;
    align-items: center;
    gap: .8rem;
    flex-shrink: 0;
    align-self: stretch;
    flex-shrink: 0;
    align-self: stretch;
    box-shadow: .2rem .2rem .8rem 0 #D1D2D4;
    border-radius: 1.6rem;
}
.complianceAndInternalAuditPolicy li:nth-child(5) div{
    height: 8.8rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.complianceAndInternalAuditPolicy li:nth-child(6) div{
    height: 10rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.complianceAndInternalAuditPolicy li:first-child {
    box-shadow: none; border-radius: 0;
    justify-content: center; align-items: flex-start;
    color: var(--blue-mid);
    font-size: var(--fs-2-4);
    line-height: var(--line-3-2);
    font-weight: 600;
}
.complianceAndInternalAuditPolicy li p {
    display: inline;
}
.complianceAndInternalAuditPolicy li span {
    display: inline-block;
    font-size: var(--fs-2-4);
    line-height: var(--line-2-6);
    color: var(--blue);
    font-weight: 600;
    padding-bottom: .4rem;
    border-bottom: .2rem solid var(--blue-light);
}
.complianceAndInternalAuditPolicy  li div {
    width: calc(100% - 5rem);
    font-size: var(--fs-1-6);
    color: var(--black-lev3);
    text-align: center;
}
.intenalAuditFunction .left .mod-mo {
    display: none;
}
.intenalAuditFunction .right {
    display: block;
}

.intenalAuditFunction {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12.4rem;
}
.intenalAuditFunction h4 {
    font-size: var(--fs-2-4);
    margin-bottom: 2.4rem;
    font-weight: 600;
    line-height: var(--line-3-2);
    color: var(--blue-mid);
}
.intenalAuditFunction .txt {
    font-size: var(--fs-1-6);
    line-height: var(--line-2-2);
}

.organizationStructure {
    margin-bottom: 7.8rem;
}
.organizationStructure .pc{
    display: flex;
    justify-content: center;
    margin-bottom: 7.8rem;
}
.organizationStructure .mo {
    display: none;
}

/* CONTACT US */
.contactUs .left {
    padding: 2.4rem 8rem 4rem;
    background: var(--white);
    box-shadow: 2px 2px 8px #D1D2D4;
    display: block;
    border-radius: 2.4rem;
    margin-top: -7.2rem;

}
.contactUs .left .map-contents {
    margin-top: 4rem;
    display: flex;
    gap:10;
    justify-content: space-between;
}
.contactUs .left h3.tit {
    width: 22.5rem;
    padding: 1rem 4rem;
    background: var(--blue-lighter);
    border-radius: 10rem;
    color: var(--blue);
    text-align: center;
    margin: 0 auto;
    font-size: var(--fs-2-4);
}
.contactUs .left .info-01 {

    max-width: 50.8rem;
}
.contactUs .left h4 {
    margin-bottom: 1.6rem;
}
.contactUs  .sub-txt {
    /* font-weight: 600; */
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
    letter-spacing: 0.02rem;
    padding: 0 0 1.6rem;
    border-bottom: .1rem solid var(--gray-lev6);
}
.contactUs ul.list {
    margin-top: 1.6rem;
}
.contactUs  ul.list li:first-child {
    margin-top: 0;
}
.contactUs ul.list li {
    margin-top: 1.8rem;
    display: flex;
    align-items: center;
}
.contactUs ul.list li::before{
    content: '';
    width: 4rem;
    height: 2.2rem;
    display: block;
}
.contactUs .investment ul.list li::before{
    content: '';
    width: auto;
    height: 2.2rem;
    display: block;
    padding-left: 2rem
}
.contactUs ul.list li > * {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
}

.contactUs .icon1::before {
    background-image: url(../images/icon/icon_contactUs_01.svg);
    background-repeat: no-repeat;
    background-position: left center;
}

.contactUs .icon2::before {
    background-image: url(../images/icon/icon_contactUs_02.svg);
    background-repeat: no-repeat;
    background-position: left center;
}
.contactUs .icon3::before {
    background-image: url(../images/icon/icon_contactUs_03.svg);
    background-repeat: no-repeat;
    background-position: left center;
}
.contactUs .icon4::before {
    background-image: url(../images/icon/icon_contactUs_04.svg);
    background-repeat: no-repeat;
    background-position: left center;
}

.contactUs .icon5::before {
    background-image: url(../images/icon/icon_location.svg);
    background-repeat: no-repeat;
    background-position: left center;
    margin-top: 0.5rem;
}
.contactUs .padding-ul {
    padding-left: 38px;
}
.contactUs .customerService {
    padding: 2.4rem 8rem 4rem;
    background: var(--white);
    box-shadow: 2px 2px 8px #D1D2D4;
    border-radius: 2.4rem;
    margin-top: 4rem;
    margin-bottom: 8rem;;
}

.contactUs .customerService h3.tit {
    width: 28.5rem;
    padding: 1rem 4rem;
    background: var(--blue);
    border-radius: 10rem;
    color: var(--white);
    text-align: center;
    margin: 0 auto;
    font-size: var(--fs-2-4);
}

.contactUs .customerService h4 {
    margin-bottom: 1.6rem;
}
.contactUs .customerService .service-con {
    width: 79.1rem;
    margin: 2.4rem auto 0;
    display: grid;
    grid-template-columns: 1fr 26.6rem;

    align-items: center;
}
.contactUs .customerService .service-con .left-con ul {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.6rem;
}
.contactUs .customerService .service-con .left-con li {
    margin-top:0;
}
.contactUs .customerService .service-con .right-con {
    width: 26.6rem
}
.contactUs .customerService .service-con .right-con img {
    width: 100%;
}

.contactUs .koreanDesk {
    padding: 2.4rem 8rem 4rem;
    background: var(--white);
    box-shadow: 2px 2px 8px #D1D2D4;
    border-radius: 2.4rem;
    margin-top: 4rem;
    margin-bottom: 8rem;;
}

.contactUs .koreanDesk h3.tit {
    width: 28.5rem;
    padding: 1rem 4rem;
    background: rgba(171, 1, 95, 1);
    border-radius: 10rem;
    color: var(--white);
    text-align: center;
    margin: 0 auto;
    font-size: var(--fs-2-4);
}
.contactUs .koreanDesk .desk-con {
    width: 79.1rem;
    margin: 2.4rem auto 0;
    display: grid;
    grid-template-columns: 26.6rem 1fr ;
    align-items: center;

}
.contactUs .koreanDesk .desk-con .right-con ul {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.6rem;
}
.contactUs .koreanDesk .desk-con .right-con li {
    margin-top:0;
    margin-left: 10rem;
}
.contactUs .koreanDesk .desk-con .left-con {
    width: 26.6rem
}
.contactUs .koreanDesk .desk-con .left-con img {
    width: 100%;
}
.contactUs .label {
    color:rgba(157, 157, 157, 1);
}
.contactUs .content {
    color:rgba(97, 98, 100, 1);
    text-align: left;
    margin-left: 5px;
}
.contactUs .address {
    display: inline-flex;
    width: 100%
}

.contactUs ul.list {

}

.contactUs ul.list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px; /* Optional for spacing */
}

.contactUs ul.list .address {
    display: flex;
}

.contactUs ul.list .label {
    width: 80px; /* Set a fixed width to align labels */
    font-weight: bold;
    text-align: left;
}
.contactUs ul.list .content {
    text-align: left;
    width: 100%
}
.contactUs .right {
    padding: 2.4rem 8rem 4rem;
    background: var(--white);
    box-shadow: 2px 2px 8px #D1D2D4;
    border-radius: 2.4rem;
    margin-top: 4rem;
    margin-bottom: 8rem;;
    display: grid;
    grid-row-gap: 4rem;
    grid-column-gap: 8rem;
    grid-template-columns: 1fr 1fr;
}

.investment-custom {
    display: flex;align-items:center; width: 120%
}
.margin-left-20 {
    margin-left: 30px;
}
.contactUs .right h3.tit {
    padding: 1rem 4rem;
    background: var( --pink-lighter);
    border-radius: 10rem;
    color: var(--pink);
    text-align: center;
    margin: 0 auto;
    font-size: var(--fs-2-4);
    grid-column: 1/span 2 ;
}

.representative-title {
    display: flex;
    align-items: center;
}

.contactUs .representative h3.tit {

    padding: 1rem 4rem;
    background: rgba(103, 109, 153, 1);
    border-radius: 10rem;
    color: white;
    text-align: center;
    margin: 0 auto;
    font-size: var(--fs-2-4);
    grid-column: 1/span 2 ;
}

.contactUs .investment h3.tit {

    padding: 1rem 4rem;
    background: rgba(211, 112, 166, 1);
    border-radius: 10rem;
    color: white;
    text-align: center;
    margin: 0 auto;
    font-size: var(--fs-2-4);
    grid-column: 1/span 2 ;
}

.contactUs .right h4 {
    font-size: var(--fs-2);
    font-weight: 700;
    line-height: var(--line0-2-4);
    color: var(--black-lev3);
}


.contactUs .investment h4 {
    font-size: var(--fs-2);
    font-weight: 700;
    line-height: var(--line0-2-4);
    color: var(--black-lev3);
    margin-bottom: 0 !important;
    margin-left: 15px  !important;
}


.contactUs .con-left {
    width: 48rem
}

/* FAQ */
.faq-list {
    display: grid;
    grid-template-columns: 36.8rem 1fr;
    margin: 3rem 0;
}

.faq-list .title {
    font-size: var(--fs-2-4);
    color: var(--black-lev1);
    font-weight: 600;
    height: 4rem;
    gap:.8rem;
    display: flex;
    align-items: flex-start;
    line-height: var(--line-3-2);
}
.faq-list .title::before {
    content: '';
    width: 5.2rem; height: 4rem;
    display: block;
}
.faq-list .icon1::before {
    background-image: url(../images/icon/icon_faq_01.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 3.2rem 3.2rem;
}
.faq-list .icon2::before {
    background-image: url(../images/icon/icon_faq_02.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 3.2rem 3.2rem;
}
.faq-list .icon3::before {
    background-image: url(../images/icon/icon_faq_03.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 3.2rem 3.2rem;
}
.faq-list .icon4::before {
    background-image: url(../images/icon/icon_faq_04.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 3.2rem 3.2rem;
}
.faq-list .icon5::before {
    background-image: url(../images/icon/icon_faq_05.svg);
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 3.2rem 3.2rem;
}
.faq-list dl {
    border: .1rem solid var(--gray-lev6);
    padding: 1.6rem;
    border-radius: .8rem;
}
.faq-list dt {
    padding: 2.4rem;
    font-size: var(--fs-2);
    line-height: var(--line-2-2);
    font-weight: 700;
    color: var(--black-lev3);
    display: grid;
    grid-template-columns: 2rem 1fr 1.5rem;
    align-items: flex-start;
    gap: .8rem;
    cursor: pointer;
}
.faq-list dt span {
    color: var(--pink);
    margin:0 .8rem 0 0rem;
    cursor: pointer;
}
.faq-list dt button {
   width: 1.2rem; height: 1.2rem;
   position: relative;
   align-self: center;
}
.faq-list dt button::before {
    content: ''; display: block;
    width: 100%; height: .2rem;
    background: var(--gray-lev9);
}
.faq-list dt button::after {
    content: ''; display: block;
    width: 100%; height: .2rem;
    background: var(--gray-lev9);
    transform: rotate(90deg);
    position: absolute;
    left: 50%; top: .5rem;
    margin-left: -.6rem;
}
.faq-list dt.on button::after {
    display: none;
}

.faq-list dd {
    display: none;
}


.faq-list dd.active {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
    background: var(--gray-lev7);
    padding: 1.6rem 2.4rem 1.6rem 4rem;
    letter-spacing: 0.02rem;
    display: flex;
}
.faq-list dd a {
    color: var(--blue);
    text-decoration: underline;
    font-weight: 600;
}
.faq-list dd span {
    font-size: var(--fs-2);
    line-height: var(--line-2-2);
    font-weight: 700;
    color: var(--blue);
    margin-right: .8rem;
}


/* MAIN */
.OpenAccount-main {
    max-width: 120rem;
    margin: 0 auto;
    position: relative;
}

.openAccountStep {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr;
    align-items: center;
    gap: 5.6rem;
    position: relative;
}
.OpenAccount-main span.arrow-01{
    width: 10rem; height: 2.7rem;
    display: block; position: absolute;
    left: 22rem; top: 1.8rem;
    background-image: url(../images/common/arrow_round_down.svg);
    background-repeat: no-repeat;
}
.OpenAccount-main span.arrow-02{
    width: 10rem; height: 2.7rem;
    display: block; position: absolute;
    left: 53rem; top: 4.8rem;
    background-image: url(../images/common/arrow_round_up.svg);
    background-repeat: no-repeat;
}

.OpenAccount-main span.arrow-03{
    width: 10rem; height: 2.7rem;
    display: block; position: absolute;
    left: 86rem; top: 1.8rem;
    background-image: url(../images/common/arrow_round_down.svg);
    background-repeat: no-repeat;
}

.openAccountStep li {
    height: 100%;
}
.openAccountStep .top {
    height: 9.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}
.openAccountStep .bottom span {
    font-weight: 600;
    color: var(--blue);
    font-size: var(--fs-1-8);
    display: inline-block;
    padding: 0 .5rem .3rem;
    border-bottom: .2rem solid var(--blue-lighter);
    margin-bottom: .8rem;
}

.openAccountStep .bottom h4 {
    font-weight: 700;
    font-size: var(--fs-1-8);
    color: var(--black);
}
.openAccountStep .top,
.openAccountStep .bottom {
    text-align: center;
}

.list-type-01 li {
    margin-top: .8rem;
    margin-left: 2rem;
    list-style:disc !important;
}

.list-type-02{
    margin-bottom: 5rem;
}
.list-type-02 li {
    font-size: var(--fs-1-2);
    color: var(--gray-lev10);
}
.list-type-02 li::before {
    display: inline-block;
    content: '*';
    width: 1rem;
    color: var(--gray-lev10);
}


.list-type-03 li {

    color: var(--gray-lev10);
}
.list-type-03 li::before {
    display: inline-block;
    content: '-';
    width: 1rem;
    color: var(--gray-lev10);
}
.pRIVACYPOLICY-top {
    width: 99.6rem;
    padding: 2.4rem 8rem;
    background: var(--white);
    border-radius: 2.4rem;
    box-shadow: 2px 2px 8px #D1D2D4;
    margin: -6.7rem auto 10rem;
    display: grid;
    grid-template-columns: 20rem 1fr;
    gap: 4rem;
    align-items: center;
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
    font-weight: 700;
    letter-spacing: 0.02rem;

}
.pRIVACYPOLICY-top::before {
    content:'';
    width: 20rem; height: 20rem;
    background-image: url(../images/icon/icon_privacyPolicy_01.svg);

}
.pRIVACYPOLICY-top a {
    color: var(--blue) !important;
}


.two-col-content {
    display: grid;
    grid-template-columns: 28.2rem 1fr;
    gap: 4rem;
    margin-bottom: 8rem;
}
.two-col-content dt {
    font-weight: 700;
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--gray-lev8);
}
.two-col-content dd {
    color: var(--gray-lev10);
    letter-spacing: 0.02rem;
}

a.mail-type {
    text-decoration: underline !important;
    color: var(--gray-lev10)!important;
}


.openAccount-bottom {
    max-width: 60rem;
    margin: 6rem auto 11rem;
}
.openAccount-bottom .btn-blue {
    width: 23.3rem; height: 4.8rem;
    line-height: var(--line-4-6);
    padding: 0; font-size: var(--fs-2);
    margin: 0 auto; display: grid;
    align-items: center;
}
.openAccount-bottom .openAccount-bottom-info {
    margin: 6.4rem 0 11.6rem;
}
.openAccount-bottom .openAccount-bottom-info p {
    font-size: var(--fs-1-6);
    color: var(--gray-lev10);
}

.information-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 1.6rem;
}


.information-card li.wrap{
    padding: 2.4rem;
    background-color: var(--gray-lev12);
    border-radius: 2.4rem;
}

.information-card li.wrap:nth-child(1) {
    background-image: url(../images/common/bg_main_01.svg);
    background-repeat: no-repeat;
    background-position: 22.2rem 1.2rem;
}
.information-card li.wrap:nth-child(2) {
    background-image: url(../images/common/bg_main_02.svg);
    background-repeat: no-repeat;
    background-position: 22.2rem 1.2rem;
}
.information-card li.wrap h5 {
    font-size: var(--fs-1-6);
    color: var(--black-lev1);
    font-weight: 600;
    margin-bottom: 1.4rem;
}
.information-card li.wrap li {
    margin-bottom: .4rem;
    display: flex;
    align-items: center;
}
.information-card li.wrap li strong {
    font-weight: 700;
    color: var(--gray-lev10);
    margin-right: .8rem;
    font-size: var(--fs-1-4);
}
.information-card li.wrap li a,
.information-card li.wrap li button,
.information-card li.wrap li span {
    font-weight: 400;
    font-size: var(--fs-1-4);
    line-height: var(--fs-1-6);
}


.aboutUs-main {
    min-height: 50.4rem;
    margin: 10rem auto 8rem;
    position: relative;
}
.aboutUs-main .aboutUs-main-bg {
    content:'';
    width: 58.4rem; height: 43.8rem;

    position: absolute;
    right: 0; top: 6.8rem;
}
.aboutUs-main ul {
    max-width: 59.1rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap:5rem;
    grid-row-gap:5rem;
}
.aboutUs-main li {
    padding: 1.6rem;
}
.aboutUs-main .top {
    margin-bottom: 1.5rem;
    content: '';
    width: 8rem; height: 8rem;
}
.aboutUs-main .bottom {
    position: relative;
}
.aboutUs-main h4 {
    font-size: var(--fs-2-4);
    line-height: var(--line-3-2);
    font-weight: 600;
    color: var(--black-lev1);
    margin-bottom: 1.5rem;
}
.aboutUs-main .top.icon1 {
    background-image: url(../images/icon/icon_target.svg);
    background-repeat: no-repeat;
    background-position: left center;
}
.aboutUs-main .top.icon2 {
    background-image: url(../images/icon/icon_CoreValue_02.svg);
    background-repeat: no-repeat;
    background-position: left center;
}
.aboutUs-main .top.icon3 {
    background-image: url(../images/icon/icon_CoreValue_03.svg);
    background-repeat: no-repeat;
    background-position: left center;
}
.aboutUs-main .top.icon4 {
    background-image: url(../images/icon/icon_CoreValue_04.svg);
    background-repeat: no-repeat;
    background-position: left center;
}
.main-affiliated {
    overflow-x: hidden;
}

.main-affiliated ul {
    width: 120rem;
    margin: 8rem auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.product-wrap-main {
    display: flex;
    flex-direction: row;
    gap: 2.5rem;
}

.product-wrap-main .product-item-01 {
    background-color: #053553;
    padding: 4rem;
    border-radius: 1.6rem;
}
.product-item-01 .top {
    margin-bottom: 3.2rem;
}
.product-item-01 .top h4 {
    color: var(--white);
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    font-weight: 700;
    margin: 1.4rem 0 0;
}
.product-item-01 .top .logo {
    display: inline-block;
    background-color: var(--white);
    border-radius: .8rem;
    overflow: hidden;
}
.product-item-01 .top .logo img {
    width: 8rem; height: 8rem;
}
.product-item-01 .mid ul {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-row-gap:.8rem;
    padding-bottom: 5.6rem;
    border-bottom: .1rem solid var(--gray-lev9);
}

.product-item-01 .mid li:first-child p {
    text-align: left;
}

.product-item-01 .mid li p {
    color: var(--white);
    font-size: var(--fs-1-2);
    font-weight: 700;
    margin-bottom: .8rem;
    text-align: right;
}
.product-item-01 .mid li div {
    color: var(--white);
    font-size: var(--fs-3-8);
    line-height: var(--line-4-6);
    font-weight: 700;
    display: flex;
    flex-direction: row;

}
.product-item-01 .mid li div em {
    font-size: var(--fs-1-2);
    font-weight: 400;
    line-height: var(--line-1-2);
}

.product-item-01 .mid li:nth-child(1) div {
    justify-content: flex-start;
}
.product-item-01 .mid li:nth-child(2) div {
    justify-content: flex-end;
}

.product-item-01 .bottom .star-aria {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1.2rem 0 4rem;
}
.product-item-01 .bottom .star-aria p {
    width: 14rem;
    font-size: var(--fs-1-4);
    line-height: var(--line-1-4);
    text-align: right;
    color: var(--white);
}
.product-item-01 .bottom .sub-txt {
    font-size: var(--fs-1);
    line-height: var(--line-1-2);
    font-weight: 600;
    color: var(--white);
}

.product-wrap-main .right .items-wrap {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    grid-row-gap: 2.4rem;
    grid-column-gap: 2.4rem;
    transform: revert;
}
.product-wrap-main .right li.item {
    width: 38.4rem; height: 23.2rem;
    padding: 2.4rem;
    background: var(--white);
    border-radius: 1.6rem;
    /* box-shadow: 2px 2px 8px #D1D2D4; */
    box-shadow:2px 2px 8px 2px rgba(202, 204, 207, 0.2)

}
.product-wrap-main .right li.item .top {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: .8rem;
}
.product-wrap-main .right li.item .top img {
    border-radius: .6rem;
    overflow: hidden;
    background: #fff;
    width: 4rem; height: 4rem;
}
.product-wrap-main .right li.item .top h4 {
    font-size: var(--fs-1-8);
    color: var(--blue);
}
.product-wrap-main .right li.item .mid ul {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-row-gap:.8rem;
    padding-bottom: 1.6rem;
    border-bottom: .1rem solid var(--gray-lev9);
}
.product-wrap-main .right li.item .mid li p{
    font-size: var(--fs-1-2);
    line-height: var(--line-1-4);
    margin-bottom: .8rem;
    color: var(--gray-lev10);
    text-align: right;
}
.product-wrap-main .right li.item .mid li div {
    font-weight: 700;
    font-size: var(--fs-3-2);
    line-height: var(--line-3-8);
    color: var(--black-lev3);
    display: flex;
    flex-direction: row;
}
.product-wrap-main .right li.item .mid li div em {
    font-size: var(--fs-1-2);
    line-height: var(--line-1-4);
    color: var(--gray-lev10);
}
.product-wrap-main .right li.item .mid li:first-child p {
    text-align: left;
}
.product-wrap-main .right li.item .mid li:nth-child(2) div {
    justify-content: flex-end;
}
.product-wrap-main .right li.item .bottom {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: .8rem;
}

.product-wrap-main .right li.item .bottom p {
    font-size: var(--fs-1-2);
    line-height: var(--line-1-4);
    color: var(--gray-lev9);
}
.product-wrap-main .right li.item .bottom .left {
    width: 15.7rem;
}
.product-wrap-main .right li.item .bottom .left p {
    margin-top: .5rem;
}
.product-wrap-main .right li.item .bottom .right {
    width: 18rem;
    text-align: right;
}
.product-wrap-main .swiper-pagination {
    display: none;
}
.product-swiper {
    overflow: visible !important;
}

.star-range {
    width: 13.6rem; height: 2.4rem;
    position: relative;
    background: url(../images/icon/icon_star_off.svg);
    background-repeat: no-repeat;
    background-position: left center;
    z-index: 10;
}

.star-range span {
    height:100%;
    background: url(../images/icon/icon_star_on.svg);
    background-repeat: no-repeat;
    background-position: left center;
    position: absolute;
    left: 0; top: 0;
    z-index: 15;
}

.reseach-main .bottom-area ul {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
}
.reseach-main .bottom-area ul li.on a,
.reseach-main .bottom-area ul li a:hover{
    color: var(--blue-lighter);

}
.reseach-main .bottom-area ul li.on a::after,
.reseach-main .bottom-area ul li a:hover::after{
    animation-duration: .7s;
    animation-name: widthfull;
    animation-fill-mode: forwards;
}
.reseach-main .bottom-area ul li a {
    color: var(--blue-light);
    font-size: var(--fs-1-6);
    font-weight: 700;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 5.8rem;
    padding-top: 1.6rem;
    border-top: 1px solid var(--blue-light);
}
.reseach-main .bottom-area ul li a::after {
    content:'';
    width: 0%; height: .1rem;
    background: var(--white);
    position: absolute;
    left:0; top:0;
}
.reseach-main .bottom-area ul li a::before {
    content: '';
    width: 4rem; height: 4rem;
    margin-right: .8rem;
    display: block;
    background-image: url(../images/icon/icon_marketReport_01.svg);

}
.reseach-main {
    padding: 11.8rem 0 13.3rem;
    min-height: 88rem;
}
.reseach-main .top-area {
    position:relative;
    margin-bottom: 6.4rem;
    height: 38.8rem;
}
.research-main-contents {
    width: 40.8rem; height: 34rem;
    overflow: hidden;
}
.research-main-contents .title {
    display: grid;
    grid-template-columns: 4rem 1fr 2rem;
    justify-content: flex-start;
    align-items: center;
    padding-bottom: 2.4rem;
    gap: .8rem;
    border-bottom: 1px solid var(--blue-light);

}
.research-main-contents .tabTarget {
    animation: quickmenusDown .1s cubic-bezier(.3,0,.3,1) forwards;
    display: none;
}
.research-main-contents .tabTarget.on{
    animation: quickmenusUp .25s cubic-bezier(.3,0,.3,1) forwards;
    display: block;
}
.research-main-contents .title::before {
    content: '';
    width: 4rem; height: 4rem;
    display: block;
    background-image: url(../images/icon/icon_marketReport_01.svg);
}

.research-main-contents .tabTarget.on .title h4 {
    animation: quickmenusUp 1s cubic-bezier(.3,0,.3,1) forwards;
}
.research-main-contents .title h4 {

    font-weight: 600;
    font-size: var(--fs-2-4);
    line-height: var(--line-3-2);
    color:var(--white);
}
.research-main-contents .title span {
    width: 1rem ; height: 1rem ;
    right: .6rem ; margin-top: -.4rem ;
    top: 50%;
    border-bottom: 0;
    border-right: .1rem solid var(--white);
    border-left: 0;
    border-top: .1rem solid var(--white);
    transform: rotate(45deg);
}
.research-main-contents .research-main-list {
    margin-top: 2.4rem;
}
.research-main-contents .research-main-list li {
    margin-top: 1.6rem;
}
.research-main-contents .research-main-list li a {
    display: block;
    position: relative;
    padding-bottom: 2rem;
    border-bottom: .1rem solid var(--blue-light);
}
.research-main-contents .research-main-list li a::before {
    content:'';
    width: 0%; height: .1rem;
    background: var(--white);
    position: absolute;
    left:0; bottom:0;
}
.research-main-contents .research-main-list li a:hover::before {
    animation: widthfull .5s cubic-bezier(.3,0,.3,1) forwards;
}
.research-main-contents .research-main-list li a .frist {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;
}
.research-main-contents .research-main-list li a .frist p {
    width: 35rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--white);
    font-weight: 700;
    font-size: var(--fs-1-8);
}
.research-main-contents .research-main-list li a .frist span {
    background-color: var(--pink);
    padding: .4rem .8rem;
    font-size: var(--fs-1);
    line-height: var(--line-1-2);
    color: var(--white);
    text-align: center;
    border-radius: .4rem;
    display: inline-block;
}
.research-main-contents .research-main-list li a .second {
    margin-top: .8rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.research-main-contents .research-main-list li a .second span {
    line-height: var(--line-2);
    color: var(--gray-lev9);
    display: block;
}

.research-main-contents .research-main-list li a .second em {
    font-size: var(--fs-1-2);
    color: var(--gray-lev9);
    display: block;
}


.research-swiper {
    width: 71.4rem; height: 38.8rem;
    overflow: hidden;
    position: absolute;
    right: 0px; top: 0px;
}

.research-swiper .swiper-pagination {
    bottom: 2rem;
    text-align: right;
    display: block;
}
.research-swiper .swiper-pagination .swiper-pagination-bullet {
    background-color: var(--gray-lev11);
}
.research-swiper .swiper-pagination .swiper-pagination-bullet-active {
    width: 3rem;
    border-radius: .4rem;
    background-color: var(--gray-lev9);
}
.main-banner {
    position: relative;

}
.main-banner-pc {
    margin: 0 auto ;
    position: relative;
    overflow: hidden;
}
.main-banner-pc .swiper-slide a.banner-01 {
    display: block;
    width: 100%; height: 83rem;
    background-position: top center;
    background-repeat: no-repeat;
    background-size: cover;
}
.world-index {
    width: 120rem;
    position: absolute;
    bottom: 4.5rem; left: 50%;
    z-index: 15;
    margin-left: -60rem;
}


.world-index .wrap{
    width: 36.6rem;
    padding: 1rem .8rem 1rem 1.6rem;
    border-radius: 10rem;
    box-shadow: 0px 4px 8px 0px rgba(192, 192, 192, 0.40);
    background-color: var(--white);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap:.8rem;
}
.world-index .title {
    font-size: var(--fs-1-4);
    font-weight: 700;
    color: var(--blue);
}
.world-index ul {
    width: calc(100% - 9rem); height: 3.6rem;

    display: block;
    overflow: hidden;
    border-radius: 10rem;
    background: var(--blue-lev9);
    position: relative;
}
.world-index ul li {
    width: 100%; height: 3.6rem;
    padding: 0 .8rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    opacity: 0;
    transform: translate3d(0, 100%, 0);
    position: absolute;
    left: 0; top: 0;
}

.world-index ul li.on {
    animation: quickmenusUp .5s cubic-bezier(.3,0,.3,1) forwards;
}


.world-index ul > li span {
    font-size: var(--fs-1-2);
    line-height: var(--line-2);
}
.world-index ul > li span:nth-child(1) {
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: .4rem;
    color: var(--black-lev1);
}

.world-index ul > li span:nth-child(1)::before {
    content: ''; display:block;
    width: 1.2rem; height: 1.2rem;
    background: url(../images/icon/icon_global.svg) no-repeat 0 0 ;
}
.world-index ul > li span:nth-child(2) {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: .4rem;
    color: var(--black-lev3);
}
.world-index ul > li span:nth-child(2)::before {
    content:'';
    width: 1.6rem; height: 1.6rem;
    display: block;
    background-image: url(../images/icon/icon_unchange.svg);
    background-position: center center;
}
.world-index ul > li.up span:nth-child(2)::before {
    background-image: url(../images/icon/icon_up.svg);
}
.world-index ul > li.down span:nth-child(2)::before {
    background-image: url(../images/icon/icon_down.svg);
}


.world-index ul > li.up span:nth-child(3) {
    color: var(--blue-lev10);
}


.world-index ul > li.down span:nth-child(3) {
    color: var(--red-lev2);
}
.world-index ul > li span:nth-child(3) {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: .4rem;
    color: var(--black-lev3);
}




.swiper-main-sub-pc {
    width: 120rem;
    height: 2.4rem;
    position: absolute;
    left: 50%; bottom: 5.1rem;
    margin-left: -60rem;
    display: flex;
    z-index: 20;
    align-items: center;
    justify-content: space-between;
}


#main-swiper-pagination-pc {
    position: absolute;
    left: auto; top:.2rem !important;
    right: 12.4rem;
    width: 12rem;
    background-color: var(--gray-lev11);
}

#main-swiper-bullets-mo .swiper-pagination-current,
#main-swiper-bullets-pc .swiper-pagination-current {
    display: block;
    font-size: var(--fs-1-4);
    color:var(--blue);
}
#main-swiper-bullets-mo .swiper-pagination-total,
#main-swiper-bullets-pc .swiper-pagination-total {
    display: block;
    font-size: var(--fs-1-4);
    color:var(--gray-lev10);
}
#main-swiper-pagination-mo .swiper-pagination-progressbar-fill,
#main-swiper-pagination-pc .swiper-pagination-progressbar-fill {
    background-color: var(--blue-light);
}
#main-swiper-bullets-pc {
    width: 15rem;
    display: flex;
    justify-content: space-between;
    right:10.9rem !important;
    top: -.7rem;
    position: absolute;
}

.swiper-main-sub-pc .swiper-move-btn-area {
    position: absolute;
    right: 0rem; top: 1.3rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}
.swiper-main-sub-pc #main-swiper-next,
.swiper-main-sub-pc #main-swiper-prev{
    width:2.4rem; height: 2.4rem;
    background: var(--white);
    border-radius: 7.5rem;
    position: relative;
    left: 0 !important; right: 0 !important;
}

.swiper-main-sub-pc #main-swiper-next::after{
    content: '';
    display:block;
    width: 0.9rem; height: 0.9rem;
    border-top: 0.1rem solid var(--gray-lev11);
    border-right: 0.1rem solid var(--gray-lev11);
    transform: rotate(45deg);
}



.swiper-main-sub-pc #main-swiper-next::before {
    content:'';
    width: 1.4rem;
    position: absolute;
    left: .45rem; top: 1.145rem;
    display:inline-block;
    border-top: 0.1rem solid var(--gray-lev11);
}
.swiper-main-sub-pc #main-swiper-prev::after{
    content: '';
    display:block;
    width: 0.9rem; height: 0.9rem;
    border-top: 0.1rem solid var(--gray-lev11);
    border-right: 0.1rem solid var(--gray-lev11);
    transform: rotate(225deg);
}
.swiper-main-sub-pc #main-swiper-prev::before {
    content:'';
    width: 1.4rem;
    position: absolute;
    left: .45rem; top: 1.145rem;
    display:inline-block;
    border-top: 0.1rem solid var(--gray-lev11);
}

.swiper-main-sub-pc #main-swiper-next:hover::after,
.swiper-main-sub-pc #main-swiper-prev:hover::after{
    border-top: 0.1rem solid var(--blue);
    border-right: 0.1rem solid var(--blue);
}
.swiper-main-sub-pc #main-swiper-next:hover::before,
.swiper-main-sub-pc #main-swiper-prev:hover::before {
    border-top: 0.1rem solid var(--blue);
}


.swiper-main-sub-mo {
    width: 50%;
    position: absolute;
    left: 25%; bottom: 14.4rem;
    z-index:10;
}
#main-swiper-pagination-mo {
    width: 80%;
    left: 10%;
    top: .9rem;
}

.swiper-main-sub-mo .count{
    position: relative;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}


.researchTeam-wrap {
    width: 99.2rem;
    margin: 4rem auto 6rem;
}

.researchTeam-wrap .researchTeam-list {
    width: 100%; height: 38.4rem;
    border-radius: .8rem;
    display: grid;

    align-items: center;
    margin-bottom: 4rem;
}
.researchTeam-wrap .researchTeam-list.bg1 {
    background: var(--blue-lighter);
    grid-template-columns: 38.4rem 1fr;
}
.researchTeam-wrap .researchTeam-list.bg2 .text {
    grid-area: 1;
    margin-left: 14.6rem;
}
.researchTeam-wrap .researchTeam-list.bg2 {
    background: var(--gray-lev6);
    grid-template-columns:  1fr 38.4rem;
}
.researchTeam-wrap .researchTeam-list.bg3 {
    background: var(--pink-lighter);
    grid-template-columns: 38.4rem 1fr;

}
.researchTeam-wrap .researchTeam-list.bg5 {
    background: rgba(216, 225, 250, 1);
    grid-template-columns:  1fr 38.4rem;

}
.researchTeam-wrap .researchTeam-list.bg5 .text {
    grid-area: 1;
    margin-left: 14.6rem;
}
.researchTeam-wrap .researchTeam-list.blue-bg {
    background: rgba(216, 225, 250, 1) !important;
}
.researchTeam-wrap .researchTeam-list.gray-bg {
    background: #e6e6e8;
}

.researchTeam-wrap .researchTeam-list .text {
    margin-left: 16.1rem;

}
.researchTeam-wrap .text h4 {
    font-weight: 600;
    font-size: var(--fs-2-8);
    line-height: var(--line-4);
    color: var(--black-lev1);
    margin-bottom: 1.6rem;
}
.researchTeam-wrap .text p {
    font-weight: 700;
    font-size: var(--fs-1-8);
    color: var(--black-lev3);
    margin-bottom: 1.6rem;
}
.researchTeam-wrap .text div {
    color: var(--gray-lev10);
}

.sub-tit-button-style {
    text-align: center;
}
.sub-tit-button-style span {
    display: inline-block;
    padding: .8rem 2.4rem;
    border-radius: 10rem;
    background-color: var(--blue-lighter);
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--blue);
    font-weight: 700;

}

.introduction-icon {
    width: 120rem;
    margin: 3rem 0 9rem;
}
.introduction-icon .txt {
    text-align: center;
    line-height: var(--line-3-2);
    color:var(--black-lev3);
    font-weight: 600;
    margin-bottom: 4rem;
}
.introduction-icon .txt span {
    font-size: var(--fs-2-4);

}
.introduction-icon ul {
    display: flex;
    justify-content: space-evenly ;
    flex-direction: row;
}
.introduction-icon ul span {
    display: block;
    font-size: var(--fs-1-8);
    color: var(--black-lev1);
    font-weight: 700;
    margin-top: 3rem;
}
.introduction-icon ul li {
    width: 30rem;
    text-align: center;
}
.introduction-icon ul li::before {
    content: '';
    width: 6.4rem; height: 6.4rem;
    display: block;
    background-position: center center;
    margin: 0 auto;
}
.introduction-icon ul li:nth-child(1)::before {
    background-image: url(../images/icon/icon_Introduction_01.svg);
}
.introduction-icon ul li:nth-child(2)::before {
    background-image: url(../images/icon/icon_Introduction_02.svg);
}
.introduction-icon ul li:nth-child(3)::before {
    background-image: url(../images/icon/icon_Introduction_03.svg);
}


.introduction-swipe-01 li .scene-01 {
    background-image: url(../images/temp/guide_02.png);
}
.introduction-swipe-01 li .scene-02 {
    background-image: url(../images/temp/guide_01.png);
}
.introduction-swipe-02 li .scene-03 {
    background-image: url(../images/temp/guide_03.svg);
}
.introduction-swipe-02 li .scene-04 {
    background-image: url(../images/temp/guide_04.svg);
}
.introduction-swipe-02 li .scene-05 {
    background-image: url(../images/temp/guide_05.svg);
}
.introduction-swipe-02 li .scene-06 {
    background-image: url(../images/temp/guide_06.svg);
}
.introduction-swipe-01 {
    margin-bottom: 5.4rem;
}
.introduction-swipe-01 ul {
    display: flex;
    gap: 2.4rem;
    align-items: center;
    justify-content: center;
}
.introduction-swipe-01 li{
   width: 58.8rem; height: 38.6rem;
   filter: drop-shadow(2px 2px 8px #D1D2D4);
   border-radius: .8rem;
   background: var(--white);
   overflow: hidden;
}
.introduction-swipe-01 li div {
    width: 100%; height: 33.2rem;
    background-repeat: no-repeat;
}
.introduction-swipe-01 li span {
    display: block;
    height: 5.4rem;
    text-align: center;
    padding: 1.6rem 0;
    font-size: var(--fs-1-8);
    font-weight: 700;
    color: var(--blue);
}
.introduction-swipe .swiper-pagination {
    display: none;
}

.introduction-swipe-02 {
    padding-bottom: 5.4rem;
}
.introduction-swipe-02 ul {
    display: flex;
    gap: 2.4rem;
    align-items: center;
    justify-content: center;
}
.introduction-swipe-02 li{
    width: 28.2rem; height: 51.4rem;
    filter: drop-shadow(2px 2px 8px #D1D2D4);
    border-radius: .8rem;
    background: var(--white);
    overflow: hidden;
}
.introduction-swipe-02 li div {
    width: 100%; height: 44.2rem;
    background-repeat: no-repeat;
}
.introduction-swipe-02 li span {
    display: block;
    height: 7.18rem;
    text-align: center;
    padding: 2.4rem 0;
    font-size: var(--fs-1-8);
    font-weight: 700;
    color: var(--blue);
}



.box-upper-style-01 {
     width: 120rem; height: 19.5rem;
     background-color: var(--white);
     border-radius: 1.6rem;
     box-shadow: 0px -8px 8px rgba(196, 196, 196, 0.2);
     margin-top: -10rem;
     position: relative;
     display: flex;
     justify-content: center;
     align-items: center;
     gap: 4rem;
}
.box-upper-style-01 .icon {
    width: 16.5rem; height: 11.5rem;
    background-image: url(../images/icon/icon_kiwoomMonitor.svg);
    background-repeat: no-repeat;
    background-position: center center;
}
.box-upper-style-01 .wrap {
    width: 27.3rem;
}
.box-upper-style-01 .wrap p {
    color: var(--gray-lev10);
}
.box-upper-style-01 .wrap .sub-contents {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 2.4rem;
    margin-top: 1.6rem;
}
.box-upper-style-01 .wrap .sub-contents .btn {
    width: 11.7rem; height: 4.8rem;
    border-radius: 10rem;
    font-weight: 600;
}

.box-upper-style-02 {
    width: 120rem; height: 19.5rem;
    background-color: var(--white);
    border-radius: 1.6rem;
    box-shadow: 0px -8px 8px rgba(196, 196, 196, 0.2);
    margin-top: -10rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
}
.box-upper-style-02 .icon {
   width: 16.5rem; height: 11.5rem;
   background-image: url(../images/icon/icon_kiwoomMonitor.svg);
   background-repeat: no-repeat;
   background-position: center center;
}

.box-upper-style-02 .wrap p {
   color: var(--gray-lev10);
}
.box-upper-style-02 .wrap .sub-contents {
   display: flex;
   flex-direction: row;
   align-items: center;
   gap: 2.4rem;
   margin-top: 1.6rem;
   color: var(--pink);
   font-weight: 700;
}
.box-upper-style-02 .wrap .sub-contents .btn {
   width: 11.7rem; height: 4.8rem;
   border-radius: 10rem;
   font-weight: 600;
}


.howToUse {
    position: relative;
    margin-bottom: 6rem;
}
.howToUse ul {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    justify-content: center;
    justify-items: center;
    grid-row-gap: 14.9rem;
}

.howToUse ul li {
    width: 42rem; height: 42rem;
    padding: 2.4rem;
    background: var(--white);
    border-radius: 1.6rem;
    box-shadow: 2px 2px 8px #D1D2D4;
}

.howToUse.hts ul li:nth-child(3) {
    order: 4;
}
.howToUse.hts ul li:nth-child(4) {
    order: 3;
}
.howToUse ul li .top {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 3.2rem;
}

.howToUse ul li .top h3 {
    width: 9.9rem; height: 4rem;
    padding: .8rem 1.2rem;
    border-radius: 10rem;
    text-align: center;
    font-size: var(--fs-2);
    line-height: var(--line-2-2);
    color: var(--blue);
    background-color: var(--blue-lighter);
    margin-bottom: 1.6rem;
}
.howToUse ul li:nth-child(2n) .top h3 {
    color: var(--pink-high);
    background-color: var(--pink-lighter);
}
.howToUse .arrow {
    width: 10rem; height: 2.7rem;
    display: block; position: absolute;
    background-repeat: no-repeat;
}
.howToUse .arrow-01 {
    left: 55rem; top: 19.8rem;
    background-image: url(../images/common/arrow_round_down_next.svg);
}

.howToUse .arrow-02 {
    left: 85rem; top: 48rem;
    transform: rotate(90deg);
    background-image: url(../images/common/arrow_round_down.svg);
}
.howToUse .arrow-03 {
    left: 55rem; top: 78rem;
    background-image: url(../images/common/arrow_round_down_back.svg);
}


.howToFeatures {
    width: 120rem; height:64.2rem;
    background-color: var(--blue-lighter);
    border-radius: 1.6rem;
    margin: 8.82rem auto 10rem;
    padding: 8.8rem 10.2rem;

}
.howToFeatures ul {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 7.8rem;
    grid-row-gap:8rem;
}
.howToFeatures li:first-child {
    background: none;
    justify-content: flex-start;
}
.howToFeatures li:first-child p {
    font-weight: 700;
    font-size: var(--fs-3-8);
    line-height: var(--line-4-6);
    color: var(--blue-light);

}
.howToFeatures li {
    width: 28rem; height: 18rem;
    background: var(--white);
    padding: 1.6rem 1.6rem 0;
    border-radius: 1.6rem;
    display: grid;
    align-items: start;
    align-content: flex-start;
    justify-items: center;
    grid-row-gap: 1.6rem;

}
.howToFeatures li .icon {
    width: 6.4rem; height: 6.4rem;
    background-repeat: no-repeat;
    background-position: center center;
    display: block;
}
.howToFeatures li .icon.icon1 {
    background-image: url(../images/icon/icon_features_01.svg);
}
.howToFeatures li .icon.icon2 {
    background-image: url(../images/icon/icon_features_02.svg);
}
.howToFeatures li .icon.icon3 {
    background-image: url(../images/icon/icon_features_03.svg);
}
.howToFeatures li .icon.icon4 {
    background-image: url(../images/icon/icon_features_04.svg);
}
.howToFeatures li .icon.icon5 {
    background-image: url(../images/icon/icon_features_05.svg);
}
.howToFeatures li p {
    font-weight: 700;
    font-size: var(--fs-1-8);
    color: var(--black-lev1);
    text-align: center;
}


.howToUseMtsStep {
    position: relative;

}
.howToUseMtsStep ul {
    display:grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-column-gap: 10.8rem;
}

.howToUseMtsStep ul li {
    width: 32.8rem; height: 22.8rem;
    background-color: var(--white);
    border-radius: 1.6rem;
    padding: 2.4rem;
    box-shadow: 2px 2px 8px #D1D2D4;
    display: flex;
    gap: 1.9rem;
    justify-content: space-between;
    cursor: pointer;
}
.howToUseMtsStep ul li:hover {
    background-color: var(--blue);
}
.howToUseMtsStep li:hover h3 {
    background-color: transparent !important;
    color:var(--white) !important;
    padding: .8rem 0 !important
}
.howToUseMtsStep li:hover p {
    color:var(--white) !important;
}
.howToUseMtsStep li:hover .left button {
    background-color: var(--pink) !important;
    color:var(--white) !important;
    filter:none !important
}
.howToUseMtsStep li:hover .left button + button {
    background-color: var(--blue-lighter) !important;
    color:var(--blue) !important;

}
.howToUseMtsStep ul.hts li {
    width: 32.8rem; height: auto;
    flex-direction: column-reverse;
}
.howToUseMtsStep ul.hts li .right {
    display:flex;
    align-items: center;
    justify-content: center;
    border-radius: 1.6rem;
    overflow: hidden;
}
.howToUseMtsStep li h3 {
    height: 4rem; display: inline;
    border-radius: 10rem;
    background-color: var(--blue-lighter);
    padding: .8rem 1.2rem;
    font-weight: 700;
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--blue);

}
.howToUseMtsStep li:nth-last-child(2n) h3 {
    background-color: var(--pink-lighter);
    color: var(--pink-high);
}
.howToUseMtsStep li .left p {
    width: 100%; height: 7.6rem;
    margin-top: 1.6rem;

    display: flex;
    align-items: center;
    line-height: var(--line-2-2);
}
.howToUseMtsStep li .left div {
    width: 100%;
    display: flex;
    gap: 2.4rem;
}
.howToUseMtsStep li .left div button {
    width: 100%; height: 4.8rem;
    font-size: var(--fs-1-6);
    font-weight: 600;
}
.howToUseMtsStep .arrow {
    width: 7.1rem; height: 2.7rem;
    display: block;
    position: absolute;
}
.howToUseMtsStep.hts .arrow-01 {
    top: 17rem;
}
.howToUseMtsStep.hts .arrow-02 {
    top: 17rem;
}
.howToUseMtsStep .arrow-01 {
    left: 35rem; top: 10rem;
    background-image: url(../images/icon/arrow_round_down_small.svg);
}
.howToUseMtsStep .arrow-02 {
    left: 78.5rem; top: 10rem;
    background-image: url(../images/icon/arrow_round_up_small.svg);
}

.gray-box-type-01 {
    height:13.2rem;
    background: var(--gray-lev7);
    border-radius: 1.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap:8rem;
}
.gray-box-type-01 .left h5 {
    font-weight: 700;
    font-size: var(--fs-1-8);
    color: var(--blue);
    margin-bottom: .8rem;
}
.gray-box-type-01 .btn {
    padding: 1.4rem 4rem;
    font-size: var(--fs-1-6);
    font-weight: 400;

}
.howto-tab {
    margin-bottom: 4rem;
}
.howto-tab ul{
    width: 64rem;
    display: flex;
    justify-content: center;
    margin: 0 auto;
}
.howto-tab ul li {
    width: 100%; height: 8rem;
    display: flex;
    align-items: center;
    gap: 1.6rem;
    justify-content: center;
    position: relative;
}
.howto-tab ul li a::before {
    content:'';
    width: 4rem; height:4rem;
    display: block;
    background-image: url(../images/icon/icon_howTo_tab_01.svg);
    background-position: left top;
    background-size: contain;
}
.howto-tab ul li:nth-child(2) a::before {
    background-image: url(../images/icon/icon_howTo_tab_02.svg);
}
.howto-tab ul li a {
    width: 100%; height: 100%;
    display: flex;
    gap: 1rem;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--black-lev1);

    text-align: center;
    line-height: 8rem;
}
.howto-tab ul li::after {
    content: '';
    width: 0; height: .2rem;
    background: var(--blue-mid);
    position: absolute;
    left: 0; bottom: 0;
}
.howto-tab ul li.on::after,
.howto-tab ul li:hover::after {
    animation: widthfull .25s cubic-bezier(.3,0,.3,1) forwards;
}

.howto-tab ul li.on a,
.howto-tab ul li:hover a {
    color: var(--blue);
}
.howto-tab-wrap {
    margin-bottom: 6.2rem;
}

.howto-tab-wrap .tabTarget {
    animation: quickmenusDown .1s cubic-bezier(.3,0,.3,1) forwards;
    display: none;
    position: relative;
}
.howto-tab-wrap .tabTarget.on{
    animation: quickmenusUp .25s cubic-bezier(.3,0,.3,1) forwards;
    display: block;
}
.howto-tab-02 .arrow-list-pink2,
.howto-tab-01 .arrow-list-pink2 {
    margin-bottom: 6.4rem;
}

.howToApp {
    padding-top: 5.6rem;
    background-color: var(--blue);
    position: relative;
}
.howToApp.hts {
    margin-top: 8rem;
    padding: 4rem 0 15rem;
}
.howToApp.hts .contents-wrap::before,
.howToApp.hts .contents-wrap::after {
    bottom: 8rem;
}
.howToApp .contents-wrap::before,
.howToApp .contents-wrap::after {
    width: 2.4rem; height: 2.4rem;
    content: ''; display: block;
    position: absolute;
    background: url(../images/icon/icon_CaretRight.svg) no-repeat 0 0;
}
.howToApp .contents-wrap::before {
    left: calc(100%/3 - 2.4rem); bottom: 14rem;
}
.howToApp .contents-wrap::after {
    left: calc(100%/3*2); bottom: 14rem;
}

.howToApp .howToApp-btnWrap {
    display: none;
}
.howToApp .swiper-wrapper {
    align-items: flex-end;
}
.howToApp.hts li.swiper-slide .img {
    border: .6rem solid var(--blue-mid);
    border-radius: 1.8rem;
}

.howToApp li.swiper-slide .img img {
    width: 100%;
}
.howToApp li.swiper-slide .txt {
    display: flex;
    padding: .8rem 0;
    gap: 2.4rem;
    flex-direction: row;
    align-items: center;
}
.howToApp li.swiper-slide .txt strong {
    font-size: 6rem;
    line-height: 7rem;
    color: var(--blue-lighter);
}

.howToApp li.swiper-slide .txt span {
    color: var(--white);
    font-size: var(--fs-1-6);
}



.howToInstall {
    width: 100%;
    margin-top: 6.4rem;
    background-color: var(--white);
    border-radius: 1.6rem;
    padding: 4.8rem 10.2rem;
    box-shadow: 2px 2px 8px #D1D2D4;
    position: relative;

}
.howToInstall ul {
    display: grid;
    grid-template-columns: repeat(3, 28.2rem);
    justify-content: center;
    gap: 7.5rem;

}
.howToInstall ul li:nth-child(4),
.howToInstall ul li:nth-child(5) {
    margin-left: 14.1rem;
    margin-right: -14.1rem;
}
.howToInstall ul li .top {
    margin-bottom: 3.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.howToInstall ul li .top h5 {
    border-radius: 10rem;
    background-color: var(--blue-lighter);
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--blue);
    padding: .8rem 1.2rem;
    margin-bottom: 1.6rem;
}
.howToInstall ul li:nth-child(2n) h5 {
    background-color: var(--pink-lighter);
    color: var(--pink-high);
}
.howToInstall ul li .top span {
    height: 5.6rem;
    color: var(--black-lev3);
    max-width: 18.2rem;
    display: block;
}
.howToInstall ul li .top span strong {
    font-weight: 700;

}
.howToInstall .arrow {
    width: 10rem; height: 2.7rem;
    display: block; position: absolute;
    background-repeat: no-repeat;
}
.howToInstall .arrow-01{
    left: 38rem; top: 6rem;
    background-image: url(../images/common/arrow_round_up.svg);
}
.howToInstall .arrow-02{
    left: 73rem; top: 4.5rem;
    background-image: url(../images/common/arrow_round_down.svg);
}
.howToInstall .arrow-03{
    left: 52rem; top: 53rem;
    background-image: url(../images/common/arrow_round_down.svg);
}


.screenShot-tab .tabTarget {
    animation: quickmenusDown .1s cubic-bezier(.3,0,.3,1) forwards;
    display: none;
    position: relative;
}
.screenShot-tab .tabTarget.on{
    animation: quickmenusUp .25s cubic-bezier(.3,0,.3,1) forwards;
    display: block;
}

.screenshot-mts {
    display: grid;
    grid-template-columns: 40rem 71.4rem;
    justify-content: space-between;
    margin-bottom: 14.8rem;
}
.screenshot-mts .screenshot-menu {
    width: 40rem;
}
.screenshot-mts .screenshot-menu li {
    height: auto !important;
}
.screenshot-mts .screenshot-next,
.screenshot-mts .screenshot-prev {
    display: none;
}
.screenshot-mts .screenshot-menu li a {
   display: block;
   width:100%; height: 100%;
   padding: 1.6rem 0 1.6rem;
   border-bottom: .1rem solid var(--gray-lev6);
   position: relative;
   overflow: hidden;
}
.screenshot-mts .screenshot-menu li a::before {
    content:''; display: block;
    width: .2rem; height: 6.2rem;
    opacity: 0; position: absolute;
    left: 0; top: -6.2rem;
    background-color: var(--blue-light);
}

.screenshot-mts .screenshot-menu li h6 {
    padding-left: 1.6rem;
    font-size: var(--fs-2);
    line-height: var(--line-2-4);
    color: var(--black-lev1);
    margin-bottom: .8rem;
}
.screenshot-mts .screenshot-menu li span {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
    padding-left: 1.6rem;
    display: none;
}
.screenshot-mts .screenshot-menu li.on a::before,
.screenshot-mts .screenshot-menu li a:hover::before  {
    animation: screenshotMts .25s cubic-bezier(.3,0,.3,1) forwards;
}
.screenshot-mts .screenshot-menu li.on a h6,
.screenshot-mts .screenshot-menu li a:hover h6 {
    color: var(--blue);
}
.screenshot-mts .screenshot-menu li.on a span,
.screenshot-mts .screenshot-menu li a:hover span {
    display: block;
    font-size: var(--fs-1-6);
    animation: quickmenusUp .5s cubic-bezier(.3,0,.3,1) forwards;
}

.screenshot-mts .screenShot-tab .wrap{
    width: 100%;
    background: var(--gray-lev7);
    border-radius: 1.6rem;
    padding: 2rem;
    position: relative;
    z-index: 10;
}
.screenshot-mts .screenShot-tab .wrap .wrap-detail {

    overflow: hidden;
}
.screenshot-mts .screenShot-tab ul.swiper-wrapper {
    width:100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

}
.swiper-pagination2 {
    display: none;
}
/* DUMMY */
.dummy-banner {
    margin: 0 auto;
}
.dummy-banner .pc {
    display: block;
}
.dummy-banner .mo {
    display: none;
}


.screenshot-swiper-move-btn {
    position: absolute;
    width:75.4rem; height: 4rem;
    z-index: 10;
    left: -2rem; top: 50%;
    display: flex;
    margin-top: -2rem;
    justify-content: space-between;

}

.screenshot-swiper-move-btn .screenshot-swiper-prev{
    display: block;
    width:4rem; height: 4rem;
    background-image: url(../images/btn/btn_swiper_aboutus_arrow_right.svg);
    transform: rotate(180deg);
    left: 0rem;
}
.screenshot-swiper-move-btn .screenshot-swiper-next{
    display: block;
    width:4rem; height: 4rem;
    background-image: url(../images/btn/btn_swiper_aboutus_arrow_right.svg);
    right: 0rem;
}

.screenshot-swiper-move-btn .screenshot-swiper-next::after,
.screenshot-swiper-move-btn .screenshot-swiper-prev::after{
    display: none;
}

.screenshot-mts .swiper-pagination {
    display: block;

    bottom: 4rem !important;
    z-index: 10;
}
.screenshot-mts .swiper-pagination {
    display: block;
    bottom: 1.5rem !important;
}
.screenshot-mts .swiper-pagination .swiper-pagination-bullet {
    background-color: var(--gray-lev11);
}
.screenshot-mts .swiper-pagination .swiper-pagination-bullet-active {
    width: 3rem;
    border-radius: .4rem;
    background-color: var(--gray-lev9);
}

.login-area {
    width: 100vw; height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}
.login-area.pop {
    filter: blur(1rem);
    transition: filter 0.25s ease-in-out;
}
.dimm {
    width: 100vw; height: 100%;
    position: fixed;
    left: 0; top: 0;
    z-index: 20;
    background: transparent;
    display: none;
}
.login-area.pop .dimm {
    display: block;
}
.login-area .login-area-wrap {
    width: 99.5rem; height: 39rem;
    display: grid;
    grid-template-columns: 54.2rem 38.3rem;
    justify-content: space-between;

}
.login-area .login-main-img {
    width: 54.2rem; height: 39rem;
    background-image: url(../images/common/bg_login.svg);
}

.login-area h3 {
    font-size:var(--fs-3-8);
    line-height: var(--line-4-6);
    color: var(--blue);
    font-weight: 700;
    margin-bottom: 4.8rem;
}
.login-area li {
    margin-bottom: 1.6rem;
}
.login-area li:nth-child(1) input {
    background-image: url(../images/icon/icon_User.svg);
    background-repeat: no-repeat;
    background-position: 1.2rem center;
    padding-left: 4rem !important;
}
.login-area li:nth-child(2) input {
    background-image: url(../images/icon/icon_LockKey.svg);
    background-repeat: no-repeat;
    background-position: 1.2rem center;
    padding-left: 4rem !important;
}
.login-area li input {
    min-height: 5.6rem;
    border-radius: 1.2rem;
}
.login-area li input:focus {
    background-color: var(--gray-lev7);
}
.login-area .tit {
    font-weight: 600;
    color: var(--gray-lev8);
    margin-bottom: .8rem;
}
.login-area .resetPW {
    display: inline-block;
    margin-bottom: 1rem;
    text-align: right;
    color: var(--black-lev4);
}
.login-area .resetPW:hover {
    color: var(--pink);
}

.pop-up-type-01 {
    padding: 4rem 8rem;
    background-color: var(--white);
    border-radius: .8rem;
    position: absolute;
    left:50%;
    top: 50%;
    z-index: 50;
    display: none;
}
.pop-up-type-01.on {
    display: block;
    animation: quickmenusUp .5s cubic-bezier(.3,0,.3,1) forwards;
    -webkit-filter: drop-shadow(2px 2px 8px var(--gray-lev4));
    filter: drop-shadow(2px 2px 8px var(--gray-lev4));
}
.pop-up-type-01 h5 {
    color: var(--black-lev3);
    font-weight: 600;
    line-height: var(--line-3-2);
    text-align: center;
    font-size: var(--fs-2-4);
    margin-bottom: 4rem;
}

.err-page-msg {
    width: 100vw; height: 100vh;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 11.3rem;

}
.err-page-msg .left-wrap {
    max-width: 69rem;
}
.err-page-msg .left-wrap h1 {
    font-weight: 700;
    font-size: var(--fs-3-2);
    line-height: var(--line-3-8);
    color: var(--black-lev3);
    margin-bottom: 2.4rem;
}
.err-page-msg .left-wrap .err-sub-txt {
    font-size: var(--fs-1-8);
    color: var(--gray-lev9);
    line-height: var(--line-2-8);
    margin-bottom: 4.8rem;;
}
.err-page-msg .err-point {
    color: var(--red-lev1);
    font-size: var(--fs-1-8);
    margin-bottom: 4.8rem;
}
.err-page-msg .err-call {
    font-size: var(--fs-1-8);
    color: var(--black-lev3);
}

.err-page-msg .btn-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    justify-content: center;
    gap: 2rem;
    margin-top: 5.8rem;
    align-items: center;
}
.err-page-msg .btn-area li {
    width: 100%;
}

.nodata-area {
    width: 33rem;
    text-align: center;
    margin: 0 auto;
}
.nodata-area h3 {
    font-weight: 700;
    font-size: var(--fs-3-8);
    line-height: var(--line-4-6);
    color: var(--blue);
}
.management-list .swiper-move-btn-area {
    width: 100%; height: 4rem;
    position: absolute;
    left: .5rem; top: 20rem;
    display: flex;
    justify-content: space-between;
}
.management-list #aboutUs-01-prev{
    display: block;
    width:4rem; height: 4rem;
    background-image: url(../images/btn/btn_swiper_aboutus_arrow_right.svg);
    transform: rotate(180deg);
    left: 0rem;
}
.management-list #aboutUs-01-next{
    display: block;
    width:4rem; height: 4rem;
    background-image: url(../images/btn/btn_swiper_aboutus_arrow_right.svg);
    right: 0rem;
}
.management-list #aboutUs-01-prev::after,
.management-list #aboutUs-01-next::after {
    display: none;
}

.introduction-swipe-02-wrap {
    width: 120rem;
    margin: 0 auto;
}
/* S:MOBILE To PC */
#menu-open-mo {
    display: none;
}
.iphone-swiper-pagination {
    display: none;
}
.android-swiper-pagination {
    display: none;
}

#aboutUs-01-next {
    display: none;
}
.notice-header-wrap .lang-select  {
    margin-right: 5rem;
}
.notice-header-wrap .lang-select ul {
    left: unset !important;
    right: 0;
}
.notice-system-alarm {
    width: 120rem;
    margin: 0 auto;
    position: relative;
}
.notice-system-alarm .top {
    text-align: center;
    margin-top: 1.35rem;
}
.notice-system-alarm .top h2 {
    font-size: var(--fs-3-2);
    font-weight: 700;
    color: var(--black-lev3);

}
.notice-system-alarm .top div{
    width: 20rem; height: 20rem;
    margin: .8rem auto;
}
.notice-system-alarm .top div img {
    width: 100%;
}

.notice-system-alarm .middle .title {
    text-align: center;
    font-size: var(--fs-2);
    font-weight: 700;
    color: var(--gray-lev10)
}
.notice-system-alarm .middle .contents {
    max-width: 56rem;
    margin: 2.4rem auto;
    padding: 2.4rem;
    background-color: var(--white);
    box-shadow: 2px 2px 8px 0px var(--gray-lev5);
    border-radius: 1.6rem;

}

.notice-system-alarm .middle .contents dt {
    font-size: var(--fs-1-8);
    color: var(--blue);
    font-weight: 700;
}
.notice-system-alarm .middle .contents dd {
    padding-left: 1.8rem;
    margin: .8rem 0 2.4rem;
    color: var(--gray-lev10);
}
.notice-system-alarm .bottom {
    max-width: 66rem;
    text-align: center;
    padding-bottom: 5rem;
    margin: 0 auto;
}

.mapouter
{
	position:relative;
	text-align:right;
    width:100%;
	max-width:500px;
	height:400px;
}
.gmap_canvas
{
    height:100%;
	overflow:hidden;
	background:none!important;

}
.gmap_iframe
{
	width:100% !important;
	height:100% !important;
}

.rounding-off {
    margin-top: -8rem;
}

.mts-intro-top {
    background: var(--white);
    border-radius: 1.6rem 1.6rem 0 0;
    display: grid;
    grid-template-columns: 1fr;
    justify-content: center;
    padding: 4rem 10rem;
    text-align: center;
}

.mts-intro-top h2 {
    font-size: var(--fs-3-8);
    font-weight: 700;
    color: var(--blue);
    margin-bottom: 4rem;
}
.mts-intro-top h4 {
    margin-bottom: 2.4rem;
}

.mts-intro-top h4 img {
    max-width: 30.4rem;
}
.mts-intro-top span {
    padding: .8rem 2.4rem;
    border-radius: 10rem;
    background-color: var(--blue-lighter);
    line-height: var(--line-2-4);
    margin:  0 auto;
    font-size: var(--fs-2);
    color: var(--blue);
}
.mts-intro-top h3 {
    font-size: var(--fs-2-4);
    line-height: var(--line-2-8);
    font-weight: 400;
    color: var(--gray-lev10);
    margin: 2.5rem 0 2.73rem;
}
.mts-intro-mid {
    padding: 0 10.2rem 0 9.8rem;
    position: relative;
    text-align: center;
}

.mts-intro-mid .sticker {
    position: absolute;
    right: 8rem; top: 1.8rem;
}

.mts-intro-mid .down {
    position: absolute;
    right: 8.5rem; top: -25.2rem;
    display: flex;
    flex-direction: column;
    gap: 1.6rem;
}
.mts-intro-mid .sticker2 {
    position: absolute;
    right: 8rem; top: 1.8rem;
}

.mts-intro-mid .down2 {
    position: absolute;
    right: 8.5rem; top: 20.8rem;
    display: flex;
    flex-direction: column;
    gap: 1.6rem;
}
.mts-intro-top .download {
    position: absolute;
    right: 7.7rem; top: 14.7rem;
    display: block;
}
.comprehensiveFeature {
    width: 100%; height: 96rem;
    background: url(../images/common/bg_mtsComprehensiveFeature.png) no-repeat center center;
    background-size: 110% 105%;
    background-color: #0D266B;
    padding: 5.8rem 0 0 0;

}
.comprehensiveFeature .contents-wrap {
    display: grid;
    grid-template-columns: 48.4rem 60rem;
    justify-content: space-between;
}
.comprehensiveFeature .top {
    height: 4.6rem;
    grid-column: 1 / span 2;
    margin-bottom: 6.9rem;
}
.comprehensiveFeature .top h4 {
    line-height: var(--line-3-8);
    text-align: center;
    color: var(--white);
    font-size: var(--fs-3-8);
    font-weight: 700;
}
.comprehensiveFeature .left {
    grid-row: 2 / span 1;

}
.comprehensiveFeature .left .left-pagenation {

    font-size: var(--fs-2);
    font-weight: 700;
    line-height: var(--line-2-4);
    color: var(--gray-lev9);

    display: flex;
    flex-direction: column;
    gap: 2.3rem;
    opacity: 1;
    border-radius: 0;
    background: none;
}
.comprehensiveFeature .left .swiper-pagination-bullet {
    width: 100%;
    height: 7.2rem;
    background: none;
    flex-wrap: nowrap;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1.6rem;
    position: relative;
    opacity: 1;
}
.comprehensiveFeature .left .swiper-pagination-bullet::after {
    width: .1rem; height: 5.5rem;
    background: var(--blue-mid);
    content: '';
    display: block;
    position: absolute;
    left: 2rem; top: 5.5rem;
}
.comprehensiveFeature .left .swiper-pagination-bullet:last-child::after {
    display: none;
}
.comprehensiveFeature .left .swiper-pagination-bullet span {
    min-width: 4rem; height: 4rem;
    display: flex;
    line-height: 4rem;
    justify-content: center;
    align-items: center;
    border: .1rem solid var(--blue-mid);
    border-radius: 10rem;
    color: var(--white);
}
.comprehensiveFeature .left .swiper-pagination-bullet-active {
    color: var(--white);
}
.comprehensiveFeature .left .swiper-pagination-bullet-active span {
    background-color: var(--blue-mid);
}
.comprehensiveFeature .right {
    width:60rem;
    grid-row: 2 / span 2;
    position: relative;

}
.comprehensiveFeature .right ul {
    align-items: center;
}

.comprehensiveFeature .right  li.swiper-slide img {
    width: 100%;
}

.comprehensiveFeature .right .comprehensiveFeature-nav {
    width: 68rem; height: 4rem;
    position: absolute;
    left: -4rem; bottom: 50%;
    margin: -2rem 0 0 0;
    z-index: 7;
    display: flex;
    justify-content: space-between;
}

.howToUse-mts h4 {
    font-size: var(--fs-3-8);
    color: var(--blue);
    text-align: center;
    margin: 4rem 0;
}
.howToUse-mts .youtube-area {
	width: 56rem; height: 31.5rem;
    display: flex;
    justify-content: center;
    margin: 0 auto 7.2rem;
}

.requestTrialID {
    height: 27rem;
    margin: -5rem 0 2.4rem;
    padding: 12rem 0 5.6rem 0;
    background-color: var(--blue-lighter);
    text-align: center;
}
.requestTrialID h3 {
    color: var(--blue);
    font-size: var(--fs-1-8);
    line-height: var(--line-2-2);
    margin-bottom: 2.4rem;
}
.requestTrialID.hts {
    margin: -5rem 0 0rem;
}
.comprehensiveFeature-pagination {
    display: none;
}

.img-pop {
    width: 41.4rem;
    position: absolute;
    left: 50%; top: 50%;
    display: none;
    z-index: 100;
    transform: translate(-30%, -50%);
    box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.46);
    border-radius: 1.6rem;
    overflow: hidden;
}
.img-pop .img-pop-con img{
    width: 100%;
    display: block;
}
.img-pop-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.2rem 1.6rem;
    background-color: var(--white);
}
.img-pop-bottom .close button{
    color: var(--blue);
    font-weight: 700;
}

/*ETC CONTENTS*/
#sub-contens-body {
    height: 100%;
}
.sub-header-type01 {
    width: 100%; height: 8rem;
    border-bottom: 0.1rem solid var(--gray-lev6);
    box-shadow: 8px 8px 16px 4px rgba(202, 204, 207, 0.2);
}
.sub-header-type01 .wrap {
    width: 144rem; height: 8rem;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.sub-header-type01 .customer {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: var(--fs-1-4);
    color: var(--gray-lev10);
}
.sub-header-type01 .customer::before {
    content: ''; display: inline-block;
    width: 1.6rem; height: 1.6rem;
    background-image: url(../images/hts/icon_hts_01.svg);
    background-repeat: no-repeat;
    background-size: contain;
}

.sub-contents-type01 {
    width: 100rem; min-height: calc(100% - 8rem);
    margin: auto;
    position: relative;
    display: grid;
    grid-template-columns: 48.6rem 1fr;
    align-items: center;
    gap:12.7rem;
}
.sub-contents-type01 .contents-bg-area {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.sub-contents-type01 .title {
    margin-bottom: 3.2rem;
}
.sub-contents-type01 .title h1 {
    font-size: var(--fs-3-8);
    line-height: var(--line-3-8);
    font-weight: 700;
    color: var(--blue);
}
.sub-contents-type01 .title ol {
    padding-left: 1.5rem;
    margin-top: 2.4rem;
}
.sub-contents-type01 .title ol li {
    color: var(--gray-lev10);
    font-size: var(--fs-1-4);
    list-style-type:decimal-leading;
}
.sub-contents-type01 .form-list-sub {
    margin: 0 0 .8rem;
}
.sub-contents-type01 .form-list-sub dt {
    font-size: var(--fs-1-6);
    color: var(--gray-lev8);
    font-weight: 600;
    margin-bottom: .8rem;
}
.sub-contents-type01 .form-list-sub dd {
    margin-bottom: 1.6rem;
}
.sub-contents-type01 .form-list-sub dd.err .err-inp-txt {
    display: flex;
    animation: quickmenusUp .5s cubic-bezier(.3,0,.3,1) forwards;
}
.sub-contents-type01 .inp-txt {
    position: relative;
}
.sub-contents-type01 .inp-txt input{
    width: 100%; min-height: 5.6rem;
    border: .1rem solid var(--gray-lev14);
    border-radius: .6rem;
    padding: 0 1.2rem 0 4.4rem !important;
    color: var(--gray-lev8);
    font-size: var(--fs-1-6);
    line-height: var(--line-1-8);

}
.sub-contents-type01 .inp-txt.sub-icon::before{
    content:'';
    width: 2.4rem; height: 2.4rem;
    position: absolute;
    left: 1.2rem; top: 50%;
    margin-top: -1rem;
}
.sub-contents-type01 dd.err  .inp-txt input{
    border: 0.15rem solid var(--pink-high);
}
.sub-contents-type01 .err-inp-txt::before {
    content: '';
    background: url(../images/icon/icon_warning.svg) transparent no-repeat center center;
}

.sub-contents-type01 .forgotPassword {
    text-align: right;

}
.sub-contents-type01 .forgotPassword a {
    text-decoration: underline;
    color: var(--blue-mid);
}
#sub-contens-body .sub-contents-btn-area {
    margin-top: 3.2rem;
}

.inp-txt.sub-icon-01::before {
    background: url(../images/icon/icon_sub_contents_02.svg);
}
.inp-txt.sub-icon-02::before {
    background: url(../images/icon/icon_sub_contents_04.svg);
}
.inp-txt.sub-icon-03::before {
    background: url(../images/icon/icon_sub_contents_01.svg);
}
.inp-txt.sub-icon-04::before {
    background: url(../images/icon/icon_sub_contents_03.svg);
}
.inp-txt.sub-icon-05::before {
    background: url(../images/icon/icon_sub_contents_05.svg);
}



/*ANIMATION*/
@keyframes widthfull{
    from{width:0;}
    to{width:100%;}
}
@keyframes screenshotMts{
    from{
        opacity: 0;
        top: -6.2rem;
    }
    to{
        opacity: 1;
        top: 1rem;
    }
}

@keyframes quickmenusUp{
    from{
        opacity: 0;
        transform: translate3d(0, 100%, 0);
    }
    to{
        opacity: 1;
        transform: translateZ(0);
    }
}
@keyframes quickmenusDown{
    from{
        opacity: 1;
        transform: translateZ(0);
    }
    to{
        opacity: 0;
        transform: translate3d(0, 100%, 0);
    }
}
.flex-column-center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-grow: 1;
    margin-bottom: 7.8rem;
}
.mt-2 {
    margin-top:  7.8rem;
}
.avatar-border {
    border-radius: 8px
}
.slider-container {
    position: relative; /* Important: Makes the container the reference for absolute positioning */
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%; /* Adjust as needed */
    max-width: 800px; /* Adjust as needed */
    margin: 0 auto;
}

.slider {
    overflow: hidden;
    width: 100%; /* Ensures the slider fills the container */
}

.slider-images {
    display: flex;
    transition: transform 0.3s ease-in-out; /* Smooth transition */
    align-items: flex-start; /* Ensure items align at the top */
    height: auto
}

.slider-images img {
    width: 100%; /* Ensures each image fills the slider's width */
    height: auto;
    flex-shrink: 0; /* Prevent images from shrinking */
}

.slider-button {
    position: absolute; /* Positions the buttons relative to the slider-container */
    top: 20%; /* Adjusted to move the button higher up */
    transform: translateY(-50%); /* Adjust for vertical centering */
    background-color: #ffffff;
    border: none;
    color: white;
    padding: 10px;
    font-size: 20px;
    cursor: pointer;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
}

#left-button {
    left: 10px; /* Position the left button on the left side */
    z-index: 9999;
}

#right-button {
    right: 10px; /* Position the right button on the right side */
}

.slider-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.hide {
    display: none !important;
}
.report-list-event li .mid a {
    word-break: keep-all;
    font-weight: 700;
    line-height: var(--line-2-2);
    font-size: var(--fs-1-6);
    color: var(--blue);
    white-space: normal;
    display: inline-block;
}
.report-list-event li .bottom {
    display: flex;
    justify-content: flex-start;
}


.sub-ul-menu {
    display: block;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.85);
    transition: height 0.05s;
    padding: 0 20px 2px 20px;
    border-top: 0.1rem solid var(--gray-lev6);
    border-radius: 1rem;
    margin-top: 0.5rem;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2); /* X-offset, Y-offset, Blur-radius, Color */
}


.info-promo-list-empty {
    margin: 0rem 0 5.4rem;
    margin-top: 15px;
    padding: 0 0 0;
}
.info-promo-list {
    margin: 0rem 0 5.4rem;
    margin-top: -30px;
    padding: 0 0 0;
}
.info-promo-list ul{
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 2.4rem;
    grid-row-gap: 4rem;
    grid-auto-flow:row;
}
.info-promo-image {
    border: none;
    width: 100%;
    /* height: 20rem; */
    border-radius: 1rem;
}
.promo-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: gray;
    padding-bottom: 4px;
}
.left {
    display: flex;
    align-items: center;
    gap: 5px;
}
.underline {
    border-bottom: 1px solid gray;
}
.right {
    display: flex;
    align-items: center;
    gap: 8px;
}
.divider {
    color: lightgray;
}
.info-promo-list li .mid a::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -.3rem;
    width: .6rem;
    height: .6rem;
    border: none;
    border-width: .1rem .1rem 0 0;
    transform: rotate(45deg);
}
.info-promo-list li:hover > .mid a::after {
    border: none;
    border-width: .1rem .1rem 0 0;
    transition: all .65s ease-in;
}

.info-promo-container {
    display: flex;
    align-items: center;
    gap: 4px; /* Optional: adds spacing between items */
    text-decoration: none;
}
a.info-promo-container {
    color: var(--blue-lev4) !important;

}
a.info-promo-container span.underline {
    border-bottom: 1px solid var(--blue-lev4) !important;
}
.arrow {
    color: var(--gray-lev4) !important;
}
.promo-rgst-info{
    width: 100%;
    min-height: 8.4rem;
    padding: 0 7.8rem;
    background: var(--gray-lev7);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.promo-rgst-info h1{
    white-space:wrap;
    line-height: var(--line-3-2);
    font-size: var(--fs-2-4);
    color: var(--black);
    font-weight: 600;
}
