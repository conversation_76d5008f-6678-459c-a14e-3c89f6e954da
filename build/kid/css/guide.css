.guide { 
    background:  #F8F6F2;
}
.guide header { 
    position: fixed;
    top: 0; left: 0;
    width: 100%; 
    color: #666;
    background: var(--white);
    z-index: 100;
}
.guide h1 {
    display: inline-flex;
    align-items: center;
    font-size: 2.2rem;
    font-weight: 700;
    padding: 1.5rem;
    margin-left: 1rem;
    font-size: 2.4rem;
    font-weight: 500;
}
.guide h1::before {
    content: '';
    width:14.4rem; height: 4rem;
    display: inline-block;
    background: url(../images/guide/logo.gif) no-repeat 0 center ;
}
.guide header ul {
    display: grid;
    grid-auto-flow: column;

}
.guide header ul li { background: #dddbd6;}
.guide header ul li.on ,.guide header ul li:hover { background: #4fc8d9;}
.guide header ul a {
    display: grid;
    place-content: center;
    height: 4.5rem;
}
.guide main {
    counter-reset: table-num;
    padding-top: 4.5rem;
    position: relative;
}
.guide .menu {
    position: fixed;
    height: 100%;
    top: 4.5rem; left: 0;
    width: 20rem;
    background: var(--gray-lev6);
}
.guide  .menu ul li {
    line-height: 5.5rem;
    text-align: left;
}
.guide .menu ul li > ul li {
    padding-left: 2rem;
}
.guide .menu ul li a {
    display: block;
    padding-left: 1rem;
    color: #111;
    width: 100%; height: 100%;
    position: relative;
    transition: all .2s ease-in-out;
}
.guide .menu ul li a:hover {
    font-weight: 500;
    color: var(--white);
}
.guide .menu ul li a::after{
    position: absolute;
    content: '';
    left:0; bottom: 0;
    width: 0; height: 100%;
    background-color: #4FC9DA;
    transform-origin:left;
    transition:width 0.25s ease-in-out;
    z-index:-1;
}
.guide .menu ul li a:hover::after, 
.guide .menu ul li.on a::after {
    width: 100%;
}
.guide .guide-list {
    padding: 5rem 5rem 5rem 25rem;
}
.guide .guide-list dl {
    margin: 0 0 3rem;
}
.guide .guide-list dl dt {
    background: #4FC9DA;
    padding: 1.5rem 1rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    color: var(--white);
    font-size: 1.6rem;
    font-weight: 700;
}

.guide .guide-list dl.on dt {
    animation-duration: 1s;
    animation-name: mySelect;
    animation-fill-mode: forwards;
    animation-direction: alternate;
    
}

.guide .guide-list dl dd {
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}
.guide .guide-list dl dd a {
    display: block;
    height: 5rem;
    border-radius: 1rem;
    line-height: 4.8rem;
    padding-left: 2rem;
    width: 100%; height: 100%;
    background: var(--gray-lev6);
    transition: all .5s;

}
.guide .guide-list dl dd a:hover {
    background: var(--gray-lev1);
    color: var(--white);
}
.guide .guide-list dl dd .state {
    position: absolute;
    right: 1.3rem; top: .9rem;
}
.guide .guide-list dl dd span {
    display: inline-block;
    padding: 0.5rem 0.5rem;
    border-radius: .5rem;
    color: var(--white);
    margin-left: 1rem;
}
.guide .guide-list dl dd span.guide-icon1 {
    background: #eb148b;
}
.guide .guide-list dl dd span.guide-icon2 {
    background: #ecb12f
}
.guide .guide-list dl dd span.guide-icon3 {
    background: #0f3c77;
}
.guide .guide-list dl dd span.guide-icon4 {
    background: #7330f8;
}
.guide .guide-list dl dd span.guide-icon5 {
    background: #f80404;
}
.guide .guide-list dl dd span.guide-icon6 {
    background: #0496f8;
}
@keyframes mySelect {
    0% {background-color: #4FC9DA;;}
    100% {background-color: #f0592a;}
}
@media screen and  (max-width:1000px) {
    .menu {
        display: none;
    }
    .guide-list {
        padding: 5rem 5% !important;
    }
}