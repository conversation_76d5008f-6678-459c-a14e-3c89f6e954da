@charset "UTF-8";
@import url(font.css?20240502);
@import url(common.css?20240228);
@import url(mobile.css?20240228);
@import url(etc.css?20240228);

:root {
  color-scheme: only light;
  /*COLOR VALUE*/
  --trans: transparent;
  --white: #fff;
  --black: #0A0A0B;
  --black-lev1: #070707;
  --black-lev2: #5A5A5A;
  --black-lev3: #414143;
  --black-lev4: #23262F;
  --black-lev5: #3A3A3A;
  --shadow: #E8E8E8;
  --gray-lev14: #c9c9c9;
  --gray-lev13: #FAFAFA;
  --gray-lev12: #F4F5F7;
  --gray-lev11: #BFC1C5;
  --gray-lev10: #616264;
  --gray-lev9: #9d9d9d;
  --gray-lev8: #414143;
  --gray-lev7: #F4F5F6;
  --gray-lev6: #E6E6E8;
  --gray-lev5: #D1D2D4;
  --gray-lev4: #AEAFB1;
  --gray-lev3: #7F7F81;
  --gray-lev2: #444546;
  --gray-lev1: #aeaeae;
  --red-lev2 : #E8132D;
  --red-lev1 : #F50E38;

  --pink:#F0027F;
  --pink-high:#AB015F;
  --pink-light:#D370A6;
  --pink-lighter:#FBE6F1;


  --blue:#081D58;
  --blue-mid:#313E75;
  --blue-light:#676D99;
  --blue-lighter:#D8E1FA;
  --blue-lev10:#155FCE;
  --blue-lev9:#EAEEFC;
  --blue-lev8:#F2F6FF;
  --blue-lev7:#A9C6FF;
  --blue-lev6:#7FA9FF;
  --blue-lev5:#548DFF;
  --blue-lev4:#2970FF;
  --blue-lev3:#215ACC;
  --blue-lev2:#194399;
  --blue-lev1:#00359E;

  --green-lev8: #EEFFF7;
  --green-lev7: #A2E1C1;
  --green-lev6: #73D1A2;
  --green-lev5: #45C283;
  --green-lev4: #16B364;
  --green-lev3: #128F50;
  --green-lev2: #0D6B3C;
  --green-lev1: #094828;

  --yellow-lev8: #FFFAEE;
  --yellow-lev7: #FDE3A0;
  --yellow-lev6: #FDD571;
  --yellow-lev5: #FCC741;
  --yellow-lev4: #FBB912;
  --yellow-lev3: #C9940E;
  --yellow-lev2: #976F0B;
  --yellow-lev1: #644A07;

  --fs-1:1rem;
  --fs-1-2:1.2rem;
  --fs-1-4:1.4rem;
  --fs-1-6:1.6rem;
  --fs-1-8:1.8rem;
  --fs-2:2rem;
  --fs-2-2:2.2rem;
  --fs-2-4:2.4rem;
  --fs-2-8:2.8rem;
  --fs-3-2:3.2rem;
  --fs-3-8:3.8rem;
  --fs-4-8:4.8rem;

  --line-1-2: 1.2rem;
  --line-1-4: 1.4rem;
  --line-1-6: 1.6rem;
  --line-1-8: 1.8rem;
  --line-2: 2rem;
  --line-2-2: 2.2rem;
  --line-2-4:2.4rem;
  --line-2-6:2.6rem;
  --line-2-8:2.8rem;
  --line-3:3rem;
  --line-3-2:3.2rem;
  --line-3-4:3.4rem;
  --line-3-8:3.8rem;
  --line-4:4rem;
  --line-4-6:4.6rem;


  /*FONT SIZE*/
  --fs-xs:1rem;
  --fs-s:1.2rem;
  --fs-m:1.4rem;
  --fs-l:1.6rem;
  --fs-xl:1.8rem;
  --fs-2x:2rem;
  --fs-xxl:2.4rem;
  --fs-xx:2.8rem;
  --fs-xxxl:3.2rem;
  --fs-xxxxl:3.8rem;
  --fs-big: 4.8rem;

  /* LINE HEIGHT */
  --line-s: 2rem;
  --line: 2.2rem;
  --line-x:2.4rem;
  --line-xx:2.8rem;
  --line-l:3rem;
  --line-xl:3.2rem;
  --line-2x:3.8rem;
  --line-xxl:4rem;
  --line-xxxl:4.6rem;

}

.fs16 {font-size: var(--fs-l);}
.fs18 {font-size: var(--fs-xl);}
.fs20 {font-size: var(--fs-2x);}
.fs24 {font-size: var(--fs-xxl);}
.fs28 {font-size: var(--fs-xx);}
.fs32 {font-size: var(--fs-xxxl);}
.fs38 {font-size: var(--fs-xxxxl);}
.fs48 {font-size: var(--fs-big);}

.bold { font-weight: 700;}
.italic { font-style: italic !important;}
.none { display: none;}
.block { display: block;}

.al-c { text-align: center;}
.al-l { text-align: left;}
.al-r { text-align: right;}

.w80 { width: 80%;}
.w70 { width: 70%;}
.w60 { width: 60%;}

.pt0 { padding-top:0;}
.mb10 { margin-bottom: 1rem;}
.mb20 { margin-bottom: 2rem;}
.blue { color: var(--blue);}
.blue-mid { color: var(--blue-mid);}
.pink { color: var(--pink) !important;}
.black { color:var(--black);}
.green-4 { color: var(--green-lev4)}

html { width: 100%; overflow-x: hidden; font-size: 62.5%;  scroll-behavior: smooth;}
body, body > * { font-size: var(--fs-1-4); line-height: var(--line-2-2); letter-spacing: -.015rem; color: var(--gray-lev3); font-weight: 400;}

/* reset */
* {box-sizing: border-box;}
html {width: 100%; height: 100%;}
body { width: 100%; height: auto;}
html, body, div, span, button, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote,
pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small,
strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label,
legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure,
figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
  margin: 0;
  padding: 0;
}
html.fix {
  overflow: hidden;
}
ul, ol, li {
  list-style: none;
}
button {
  cursor: pointer;
}
article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  display: block;
  margin: 0;
  padding: 0;
}

form {
  height:100%;
  margin: 0;
  padding: 0;
}

fieldset {
  display: block;
  margin: 0;
  padding: 0;
  border: 0;
}

legend {
  position: absolute;
  visibility: hidden;
  overflow: hidden;
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
}

a, a:link, a:visited, a:active, a:hover,
button, button:link, button:visited, button:active, button:hover,
select, select:link, select:visited, select:active, select:hover {
  text-decoration: none;
  color: var(--gray-lev3);
}

input, button {
  padding: 0;
  margin: 0;
  border: 0;

  letter-spacing: .2px;
  color: var(--gray-lev3);
  vertical-align: middle;
  background: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-appearance: none;
}

button[dasable],button:disabled {
  cursor:default;
}

button, label {
  cursor: pointer;
}

select {

  -moz-appearance: none;
  appearance: none;
  -webkit-appearance: none;
  border: none;
  background-repeat: no-repeat;
  background-position: center right;
}

input[type='text'], input[type='password'] {
  height: 2.4rem;
  padding: 0 .5rem;
  background: #fff;
}

input[type='radio'], input[type='checkbox'] {
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
}

input::-webkit-input-placeholder {
  color: #aaa;
}

input::placeholder {
  color: #aaa;
}

img {
  border: 0;
  vertical-align: top;
}

hr {
  display: none;
}

em, address {
  font-style: normal;
}

h1, h2, h3, h4, h5, h6, strong {
  font-weight: 700;
}

table {
  width: 100%;
  border-collapse: collapse;
}
table caption {
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
  visibility: hidden;
  overflow: hidden;
  padding: 0;
  margin: 0;
}
li {
  list-style: none;
}

