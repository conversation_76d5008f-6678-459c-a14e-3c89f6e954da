@charset "UTF-8";

.img > img {
    width: 100%;
}

.portrate {
    filter: none;
}
.landscape {
    filter: blur(1rem);
    transition: filter 0.25s ease-in-out;
}

.unlandscape {
    width: 100vw; height: 100vh;
    z-index: 1000;
    position: absolute;
    top: 0%; left: 0;
    background-image: url(../images/icon/icon_mo_rotate.svg);
    background-position: center center;
    background-size: 50vw 50vh;
    background-repeat: no-repeat;
    animation-duration: .7s;
    animation-name: rotate;
    animation-delay: 1500;
    animation-timing-function: ease-in-out;
}





/* MOBILE WIDTH 720PX FOR 360px */
@media screen and  (max-width:1000px) {
    html {
        height: 100vh;
        overflow-x: hidden;
        height: calc(var(--vh, 1vh) * 100) !important;

    }
    body { width: 100vw !important; overflow-x: hidden;   padding-top: 60px !important;}

    #header{
        position: fixed;
        left: 0; right: 0; top: 0;
        z-index: 90;
        border-bottom: 0;
        transition: top .3s ease;
        background: var(--white);

    }
    #header.hide {
        top: -6rem;
    }
    main {

    }
    #header-wrap {
        width: 100%;
        padding: 0 1.6rem;
        display: flex;
        height: 6rem;
    }

    #header-wrap h1 {
        display: flex;
        justify-self: center;
        align-items: center;
    }
    #header-wrap h1 .history-back {
        margin-right: 2.2rem;
        height: 2rem;
    }
    #header-wrap .global-menu-pc{
        display: none;
    }
    #header-wrap h1 .logo-pc img {
        max-width: 9.7rem;
        max-height: 3rem;
    }
    #sub-menu {
        width: 6.2rem; height: 1.6rem;
        border: 0; border-radius: 0;
        display:inline-flex;
        position: absolute;
        right: 8rem;
        align-items: center;
        display: none;
        justify-content: space-between	;
    }
    #sub-menu .btn-openaccount, #sub-menu em {
        display: none;
    }
    #header .lang-select {
        width: 100%;
        margin-right: 3rem;
        text-align: right;
    }
    #header .lang-select, #sub-menu .lang-select .target{
        width: 100%;
    }

    #header .global-menu-bg {
        position: fixed;
        right:0; top:0; left:auto;
        width: 100vw; height: 100vh;
        z-index: 50; opacity: .8;
        background: #000;
        overflow: hidden;
        display: none;
    }
    #header .global-menu-bg.on {
        display: block;
    }
    #header .global-menu-bg::after {
        width: 200vw; height: 200vh;
        background: #000; opacity: .8;
        -webkit-filter:  blur(10rem);
        backdrop-filter: blur(10rem);

    }
    #quick-link ul::after {
        content: ' ';
        display: block;
        width: .5rem; height: .5rem;
        border: solid var(--blue-light);
        border-width: .1rem .1rem 0 0;
        position: absolute;
        left: 50%; top: 1.5rem;
        margin-left: -.25rem;
        transform: rotate(-45deg);
    }
    #quick-link ul.up::after {
        display: none;
    }
    .contents-gray {
        position: relative;
        padding: 3rem 0 6.5rem;
    }
    .mo-menu-bg {
        display: block;
        position: fixed;
        right:-80vw; top:0; z-index: 51;
        width: 80vw; height: 100%;
        background: var(--white);
        overflow-y: auto;
    }
    .mo-menu-wrap {
        height: calc(100%);
        padding: 1.6rem ;
        display: grid;
        grid-auto-rows: max-content;
        position: relative;
    }

    .mo-menu-top {
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }
    .mo-menu-close {
        width: 3.2rem; height: 3.2rem;
        text-indent: -999rem;
        background-image: url(../images/btn/btn_mo_globalmenu_close.svg);
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: contain;
        position: absolute;
        right: 1.6rem; top: 1.6rem;
    }
    .mo-menu-top .logo-pc {
        padding-top: 2rem;
    }

    .mo-menu-mid {
        margin: 2.421rem 0 3.2rem;
    }
    .mo-menu-mid ul {
        display: flex;
        justify-content: center;
        gap: 1.6rem;
        flex-direction: row;
        margin-bottom: 2.4rem;
    }
    .mo-menu-mid ul li img {
        -webkit-filter: drop-shadow(0 0.2rem 0.8rem rgba(192, 192, 192, 0.6));
        filter: drop-shadow(0 0.2rem 0.8rem rgba(192, 192, 192, 0.6));
    }
    .mo-menu-mid .btn-wrap {
        width: 100%; height: 5.6rem;
        border: .1rem solid var(--gray-lev6);
        padding: 0 .8rem;
        margin: 0 auto;
        border-radius: 10rem;
        display: grid; gap: .8rem;
        grid-template-columns:  13.4rem 0.1rem 1fr;
        align-items: center;
        justify-content: space-between;

    }
    .mo-menu-mid .btn-wrap .btn-openaccount {
        max-width: 18rem; height: 4rem;
        padding: 0 2rem;
        border-radius: 2.4rem;
        background: var(--blue-lighter);
        box-shadow: 0px 4px 8px 0px rgba(192, 192, 192, 0.40);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--blue);
        font-weight: 900;
    }
    .mo-menu-mid .btn-wrap em {
        width: .1rem; height: 2.2rem;
        background: var(--gray-lev6);
        display: inline-block;
    }
    .mo-menu-mid .btn-wrap .btn-forgot {
        color: var(--gray-lev10);
        text-align: center;
    }
    .mo-menu-list {
         height: calc(100% );
        overflow-y: auto;
        scroll-behavior: smooth;
        overflow-x: hidden;
    }

    .mo-menu-list .menu-item  {
        margin-bottom: 0.8rem;
    }

    .mo-menu-list .menu-item .menu-name {
        font-size:var(--fs-1-8);
        font-weight: 700;
        padding: 1.6rem 0;
        margin-bottom: 0.8rem;
        display: block;
        position: relative;
    }
    .mo-menu-list .menu-item .menu-name::after {
        width: 0px; height: 0px;
        content:'';
        position: absolute;
        right: 1.85rem; top: 50%;
        margin-top: -.3rem;
        border-right: 0.55rem solid transparent;
        border-left: 0.55rem solid transparent;
        animation-duration: .25s;
        animation-name: arrowUpOff;
        animation-fill-mode: forwards;
        transition: all;
    }
    .mo-menu-list .menu-item.active .menu-name::after,
    .mo-menu-list .menu-item.on .menu-name::after {
        animation-duration: .7s;
        animation-name: arrowUpOn;
        animation-fill-mode: forwards;
        transition: all;
    }
    .mo-menu-list .menu-item.on .menu-name,
    .mo-menu-list .menu-item.active .menu-name,
    .mo-menu-list .menu-item li.active > a ,
    .mo-menu-list .menu-item li.on > a {
        color:var(--blue-lev1);
    }

    .mo-menu-list .menu-item ul {
        overflow: hidden;
        max-height: 0;
        transition: max-height 0.4s ease-out;
    }

    .mo-menu-list .menu-item.active ul,
    .mo-menu-list .menu-item.on ul {
        max-height: 50rem;
        transition: max-height 0.6s ease-in;

    }

    .mo-menu-list .menu-item li {
        padding-left: 1.6rem;
        border-left: 0.2rem solid  var(--blue-lev1);
        padding-top: 1.6rem;
    }
    .mo-menu-list .menu-item li:first-child{
        padding-top: 0;
    }

    .mo-menu-list .menu-item li a{
        display: block;
        line-height: var(--line-2-2);
        font-size: var(--fs-1-6);
    }

    #menu-open-mo {
        width: 1.8rem; height: 1.4rem;
        min-width: 1.8rem;
        position: relative; display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    #menu-open-mo span {
        width: 100%; height:0.2rem;
        background: var(--blue-lev1);
        border-radius: 2px;
    }
    #menu-open-mo span:nth-child(3) {
        width: 50%;
    }

    #footer {
        width: 100%;
    }

    #footer .footer-top{
        width: 100%;
        grid-template-columns: 1fr;
        grid-column-gap: 0;
        padding: 3.2rem 0;

    }
    #footer .footer-top h4 {
        text-align: center;

    }
    #footer .footer-top h4 img {
        max-width: 60%;
    }
    #footer .footer-top address {
        padding: 0 1.6rem;
        text-align: center;
        margin-top: 2.4rem;
    }
    #footer .footer-top address h5 {
        margin-bottom: .8rem;
    }

    #footer .footer-top dl {
        padding: 0 1.6rem;
        margin-top: 2.4rem;
        column-gap: 4.4rem;
    }
    #footer .footer-top dl dt {
        text-align: center;
        margin-bottom: .8rem;
    }
    #footer .footer-top dl dd a{
        width: 4rem; height: 4rem;
    }
    #footer .footer-top dl dd.icon2 a {
        width: 3.4rem; height: 3.4rem;
    }
    #footer .footer-bottom-wrap {
        width: 100%; height: auto;
        flex-direction: column-reverse;
    }
    #footer .footer-bottom ul {
        width: 100%;
        flex-wrap: wrap;
        /* gap: 1px; */
    }
    #footer .footer-bottom ul li {
        width: 50%; height: 5.2rem;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        border: solid var(--blue-mid);
        border-width: 0 .1rem .1rem 0;
    }
    #footer .footer-bottom ul li:nth-child(2),
    #footer .footer-bottom ul li:nth-child(4){
        border-width: 0 0 .1rem 0;
    }
    #footer .footer-bottom ul li::after {
        content: '';
        margin: 0;
    }
    #footer .footer-bottom article {
        padding: 1.6rem 0 1.6rem;
    }
    /*SELECT*/
    .select-item.on ul {
        left: 0%;
    }
    .contents-wrap {
        width: 100%;
    }
    /* MARKET REPORT */

    .grand-banner h2 {
        margin-top: -5.7rem;
        font-size: var(--fs-3-2);
        line-height: var(--line-3-8);
    }

    .report-list {
        margin: 3rem 0 5.4rem;
        padding: 12.8rem 1.6rem 0 1.6rem;
    }
    .report-list ul {
        grid-template-columns: 1fr;
        grid-row-gap: 2.4rem;
    }
    .report-list li .mid p {
        width: 23rem;
    }
    .report-list .nodata-area {
        width: 21.8rem;
        text-align: center;
        margin: 0 auto;
    }
    .report-list .nodata-area h3 {
        font-size: var(--fs-2-4);
        line-height: var(--line-3-4);
    }
    .report-list .nodata-area img {
        width: 100%;
    }

    .info-event-list {
        margin: 0 0 5.4rem;
        margin-top: -5rem;
        padding: 0 1.6rem 0 1.6rem;
    }
    .report-list ul {
        grid-template-columns: 1fr;
        grid-row-gap: 2.4rem;
    }



    .search-bar {
        flex-direction: column;
        width: calc(100% - 3.2rem);
        margin-left:0%;
        left: 1.6rem;
        top: -14rem;
    }
    .search-bar .search-date {
        width: 100%;
    }
    .search-bar .search-info {
        width: 100%; height: auto;
        flex-direction: column;
        padding:0; margin-top: 1.6rem;
        -webkit-filter: drop-shadow(0px 0px 0px var(--gray-lev4));
        filter: drop-shadow(0px 0px 0px var(--gray-lev4));
        border-radius: 0; background-color: transparent;
    }
    .search-bar .search-select {
        width: 100%; height: 6.4rem;
        -webkit-filter: drop-shadow(2px 2px 8px rgba(202, 204, 207, 0.2));
        filter: drop-shadow(2px 2px 8px rgba(202, 204, 207, 0.2));
        background: var(--white);
        border-radius: 10rem;
        z-index: 50;
    }
    .search-bar .search-select ul {
        top: 7rem;
        max-width: 100%; width: 100%;
    }
    .search-select button.target {
        height: 100%;
        padding-left: 2rem;
    }
    .search-bar .search-box {
        width: 100%; height: 6.4rem;
        -webkit-filter: drop-shadow(2px 2px 8px rgba(202, 204, 207, 0.2));
        filter: drop-shadow(2px 2px 8px rgba(202, 204, 207, 0.2));
        background: var(--white);
        border-radius: 10rem;
        padding: .8rem 1.6rem;   margin: 1.6rem 0 0 0;
    }
    .search-bar .search-inp {
        height: 100%;
    }
    .title-report {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        margin: 2rem 0 2.8rem;
        padding: 0 1.6rem;
    }
    .title-report .left {
        margin-bottom: 3rem;
    }
    .title-report .left h1 {
        width: 100%;
        word-break:break-all;
        margin-bottom: .8rem;
        overflow:visible;
        text-overflow: clip;
        white-space:normal;
        line-height: 3.2rem;
    }
    .viewer-pdf {
        width: 100%;
    }



    .promo-title-news {
        height: auto;
        padding: 0 1.6rem;
        background-color: var(--white);
        flex-direction: column;
        align-items: flex-start;
    }
    .promo-title-news h1 {

        white-space:wrap;

        margin-bottom: 2rem;
    }
    .promo-title-news .date em {
        margin-left: 3.6rem;
    }
    /* Daily */
    .round-view {
        width: 100%;
        margin: 0rem 0 4rem;
        padding: 3.2rem 0 0;
        border-radius: 0;
    }
    .title-news .date em {
        margin-left: 3.6rem;
    }
    .title-news  {
        height: auto;
        padding: 0 1.6rem;
        background-color: var(--white);
        flex-direction: column;
        align-items: flex-start;
    }
    .title-news h1 {

        white-space:wrap;

        margin-bottom: 2rem;
    }
    .round-content {
        padding: 4.3rem 1.6rem 0
    }
    .round-content dt .header-area {
        display: grid;
        grid-template-columns: 12.7rem 1fr;
        grid-template-rows: 8rem 1fr;
        gap: 1.6rem;
        margin-bottom: 2.4rem;
    }
    .round-content dt .header-area p.logo { grid-column: 1;}
    .round-content dt .header-area p.sub-tit { grid-column: 1 / span 2;}
    .round-content dt .header-area h2 { grid-column: 2; align-self: center;}

	.max-lines {
		display: inline-block; /* Set display to inline-block */
		width: 100%; /* Set the desired width */
		white-space: nowrap; /* Prevent line breaks */
		overflow: hidden; /* Hide overflowing text */
		text-overflow: ellipsis; /* Show ellipsis (...) */
	}

	.max-lines-detail {
	    display: inline-block;
	    width: 100%;
	    white-space: nowrap;
	    overflow: hidden;
	    text-align: end;
	    text-overflow: ellipsis;
	}

    .list-navi {
        padding: 2.4rem 2rem;
        grid-template-rows: 1fr 1fr;
        grid-template-columns: 1fr 1fr 1fr 1fr;
    }
    .list-navi a:nth-child(1) { grid-column: 1/span 2;}
    .list-navi a:nth-child(2) { grid-row: 2; grid-column: 2/span 2; }
    .list-navi a:nth-child(3) { grid-column: 3/span 2; justify-self:flex-end;}

    .list-navi .btn-prev span,
    .list-navi .btn-next span {
        display: none;
    }
    .pdf-viewer-height{
        height: 54rem;
    }

    /* ABOUT US */
    .content-tit {
        font-size: var(--fs-2-8);
        line-height: var(--line-3-4);
    }

    .content-tit.type01 {
        max-width: 98%;
        margin: 7.5rem 0 1.6rem;
    }
    .content-tit.mo-line2 {
        width: 70%;
        text-align: center;
        margin: 3.2rem auto 4.4rem;
        line-height: var(--line-3-4);
    }
    .content-tit.hero-title {
        padding: 0 1.6rem;
    }
    .content-tit.hero-title p span{
        font-size: var(--fs-2-4);
        font-weight: 600;

    }
    .grand-banner.type03 h2 {
        display: block;
        font-size: var(--fs-2-8);
        font-weight: 700;
    }

    .grand-banner.info-event {
        background-image: url(../images/common/banner_info_event_mobile.png);
    }
    /* Style the parent <a> to act as a container */
    .info-event-container {
        display: block; /* Ensures the <a> behaves as a block element */
        width: 100%; /* Makes it span the full width of its parent */
        max-width: 100%; /* Prevents it from overflowing */
        text-align: center; /* Optional: Centers content inside if needed */
    }
    .info-event-list li .mid {
        padding: 2.4rem 0;
        text-align: center;
    }
    .info-event-list li .mid p {
       font-size: 3rem;
    }
    .info-event-list li .bottom {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        padding: 0 15px 0 15px;
    }

    /* Style the image */
    .info-event-image {
        width: 100%; /* Makes the image span the full width of its parent */
        height: auto; /* Maintains the aspect ratio */
        display: block; /* Removes inline gaps */
        object-fit: contain; /* Adjust this based on your design: 'cover', 'contain', or 'none' */
    }
    .profile {
        width:90%;
        padding:0;
        border-radius: 1.4rem;
        margin: -14.4rem auto 0;
    }
    .profile h2 {
        display: none;
    }
    .profile h4 {
        position: absolute;
        left: 50%; top: -3.5rem;
        margin-left: -14.25rem;
    }
    .profile-box {
        margin: 0;

    }
    .profile-box .profile-box-wrap {
        margin: 0;
        grid-template-columns:  1fr 1fr;
    }
    .profile-box .col1 {
        order: 1;
    }
    .profile-box .col2 {
        order: 2;
        border: solid var(--gray-lev6);
        border-width: 0 0 .1rem 0;
    }
    .profile-box .col3 {
        order: 3;
        grid-row: 2;
        border: solid var(--gray-lev6);
        border-width: 0 .1rem .1rem  0;
    }
    .profile-box .col4 {
        order:3;
        grid-row: 2;
        grid-column:1 /span 2;
        border: solid var(--gray-lev6);
        border-width: 0 0rem .1rem  0;
    }
    .profile-box .col5 {
        order:5;
        grid-column:1 /span 2;
        grid-row: 3;
        border: solid var(--gray-lev6);
        border-width: 0 0 0 0;
    }
    .profile-box .col6 {
        order:6;
        grid-row: 4;
        grid-column:1 /span 2;
        border: solid var(--gray-lev6);
        border-width: 0 0 .1rem 0;
    }
    .profile-box .col7 {
        order:7;
        grid-row: 5;
        grid-column:1 /span 2;
    }
    .profile-box .col7 .mod-pc {
        display: none;
    }
    .profile-box .col7 .mod-mo {
        display: block;
    }
    .profile-box .col7 .mod-mo tbody td {
        font-size: var(--fs-1-4);
        border: solid var(--white);
        border-width: 0 0 .1rem .1rem;
    }
    .profile-box .col7 .mod-mo tbody tr > td:nth-child(odd){
        background: var(--gray-lev6);

    }
    .profile-box .col7 .mod-mo tbody tr > td:nth-child(even){
        background: var(--gray-lev7);

    }
    .profile-box .col7 .mod-mo tbody tr:nth-child(even) > td:first-child {
        background: var(--blue-mid) !important;
        color: var(--white);
        height: 5.6rem;
        border: solid var(--white);
        border-width: 0 0 .1rem 0rem;
    }
    .profile-box .col7 .mod-mo tbody tr:nth-child(odd) > td:first-child {
        background: var(--blue)  !important;
        color: var(--white);
        height: 5.6rem;
        border: solid var(--white);
        border-width: 0 0 .1rem 0rem;
    }
    .profile-box .col7 .mod-mo tr th {
        color: var(--black-lev5);
        font-size: var(--fs-1-4);
        height: 5.6rem;
        border: solid var(--white);
        border-width: 0 0 .1rem 0;
    }
    .profile-box .col7 .mod-mo tr td {
        text-align: center;
    }
    .profile-box .col7 .mod-mo tr th:nth-child(odd) {
        background: var(--gray-lev6);

    }
    .profile-box .col7 .mod-mo tr th:nth-child(even) {
        background: var(--gray-lev7);

    }
    .profile-box .col8 {
        order:8;
        grid-row: 6;
        grid-column:1 /span 2;
    }
    .management-list {
        overflow: hidden !important;
    }

    .management-list li {
        overflow: hidden;
    }
    .management-list li img {
        filter:none;
    }
    .about-future {
        grid-template-columns: 1fr;
        height: auto;
    }
    .about-future::after {
        width: 20rem;
        right:10vw; top: 23.7rem;
        margin-right: 0rem;
        background-size: 100% 100%;

    }
    .about-future .left{
        height: 35.4rem;
    }
    .about-future .left span {
        left: 2rem;
    }
    .about-future .left::after {
        left: 47rem; top:5rem;
    }
    .about-future .left div {
        left: 2rem;
    }
    .about-future .left div p {
        line-height: var(--line-3-8);
        font-size: var(--fs-3-2);
    }
    .about-future .right {
        height: 35.4rem;
    }
    .about-future .right span {
        left: 2rem; top: 4rem;
    }
    .about-future .right ul {
        left: 2rem; top: 9rem;
    }
    .core-value h3 {
        padding: 0 1.6rem;
    }
    .core-value ul {
        grid-template-columns: 1fr 1fr;
        margin-top: 3.86rem;
    }
    .core-value ul li {
        width: 100%;
    }
    .history-year {
        width:100%;
        padding: 0 2rem !important;
        margin-bottom: 4rem;
    }

    .history-year-contents .symbol{
        display: none;
    }
    .history-year-contents li.swiper-slide {
        grid-template-columns: 1fr;
    }
    .history-year-contents .swiper-wrapper {
        align-items: center;
        padding-bottom: 1rem;
    }
    .history-nav {
        width: 90%;
        left: 5%; top: 50%;
        margin-top: -1.2rem;
    }
    .history-nav span {
        width: 2.4rem; height: 2.4rem;
        background-size: contain;
    }
    .history-year-contents li.swiper-slide {
        padding: 0;
    }
    .history-year-contents li.swiper-slide ul {
        width: 80%;
    }

    .core-business-tab li {
        width: 13.2rem;
    }
    .core-business-content li.swiper-slide {
        flex-direction: column;
        padding: 0 1.6rem;
    }
    .why-us ul {
        grid-template-columns: 1fr;
        justify-items: center;
        gap: 1.6rem;
    }
    .why-us ul li {
        width:90%;
        height: auto;
        padding: .8rem;
    }

    /* CORPORATE GOVERNANCE */


    .corporate-board .top h3 {
        font-weight: 400;
        font-size: var(--fs-2);
    }
    .corporate-board {
        flex-direction: column;
    }
    .corporate-board .left {
        width: 90%;
        height: auto;
        margin: 0 5% 7.7rem;
        padding: 13rem  2.4rem 0;
    }
    .corporate-board .top {
        width: 90%;
        left: 5%;
        margin: 0;
    }
    .corporate-board .top p {
        width: 85%;
        padding-left: 5%;
        font-size: var(--fs-1-8);
    }

    .corporate-board .right {
        width: 90%;
        height: auto;
        margin: 0 5% 0rem;
        padding: 13rem  2.4rem 0;
    }

    .content-box-01  {
        display: flex;
        justify-content: stretch;
        align-items: start;
        flex-direction: column;
        margin-bottom: 2.4rem;
    }
    .content-box-01 .contenta-area {
        max-width: 100%;
        padding: 0 1.6rem;
        margin-top: 4rem;
    }
    .content-box-01 .img-area {
        display: none;
    }
    .content-box-01.contents-01 .contenta-area {
        margin-bottom: 0rem !important;
    }
    .content-box-01.contents-02 .contenta-area {
        margin-top: 0rem !important;
    }
    .gray-bg-pc {
        background-color: transparent;
    }
    .intenalAuditFunction {
        display: flex;
        justify-content: flex-start;
        gap:0;
        flex-direction: column;
        padding: 0 1.6rem;
    }
    .intenalAuditFunction img {
        width: 100%;
    }
    .intenalAuditFunction .left .mod-mo {
        margin-bottom: 2.4rem;;
        display: block;
    }
    .intenalAuditFunction .right {
        display: none;
    }
    .complianceAndInternalAuditPolicy {
        grid-template-columns: 1fr;
        margin: 2. 4rem 0 0rem;
        padding: 0 1.8rem;
    }
    .complianceAndInternalAuditPolicy .right {
        order: 1;
        margin-bottom: 4.4rem;
    }
    .complianceAndInternalAuditPolicy .left {
        order: 2;
    }
    .complianceAndInternalAuditPolicy .right img{
        width: 100%;
    }
    .complianceAndInternalAuditPolicy ol {
        grid-template-columns: 1fr;
    }
    .complianceAndInternalAuditPolicy li {
        width: 100%;
    }
    .riskManagementPolicy .list-area {
        width: 100%;
    }
    .codeOfEthics {
        margin: 9rem 0 0;
        padding: 3.9rem 0 7rem;
    }
    .codeOfEthics .top h4 {
        font-size: var(--fs-2-8);
        line-height: var(--line-3-4);
        margin-bottom: 1.6rem;
    }
    .codeOfEthics .top p {
        max-width: 95%;
        font-size: var(--fs-1-6);
        line-height: var(--line-2-2);
    }
    .codeOfEthics .mid  {
        margin: 1.9rem 0 0;
    }
    .codeOfEthics .mid .txt {
        max-width: 95%;
        font-size: var(--fs-1-8);
        line-height: var(--line-2-8);
        font-weight: 600;
    }
    .codeOfEthics .mid ol {
        flex-direction: column;
    }
    .codeOfEthics .bottom {
        max-width: 95%;
        font-size: var(--fs-1-8);
        line-height: var(--line-2-8);
        font-weight: 600;
    }
    .riskManagementPolicy {
        background-position-x:35%;
    }
    .riskManagementPolicy .wrap {
        width:100%;
        flex-direction: column;
    }
    .riskManagementPolicy .wrap h3 {


    }
    .riskManagementPolicy .wrap h3 span {
        font-size: var(--fs-2-8);
        margin-top: .8rem;
        display: inline-block;
    }
    .organizationStructure .pc {
        display: none;
    }
    .organizationStructure .mo {

        display: block;
        padding: 0 1.6rem;
    }
    .organizationStructure .top{
        width: 40%;
        margin: 0 auto;
    }
    .organizationStructure .top li {
        margin: 0 0 4rem;
        position: relative;
    }
    .organizationStructure li span {
        font-weight: 400;
        font-size: var(--fs-1-6);
    }
    .organizationStructure .top li::after {
        content: '';
        width: .1rem; height: 100%;
        background-color: var(--gray-lev11);
        position: absolute;
        left: 50%; top: 4.2rem;
        margin-left: -.1rem;
    }

    .organizationStructure .mid {
        display: flex;
        justify-content: space-between;
        position: relative;
        margin-top: -2rem;
    }
    .organizationStructure .mid li {
        width: 9rem;
        z-index: 10;
    }
    .organizationStructure .mid::after {
        content: ''; z-index: 9;
        width: 50%; height: .1rem;
        background-color: var(--gray-lev11);
        position: absolute; left: 25%; top: 50%;
    }

    .organizationStructure .mid-button {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap:calc(100% - 17rem);
        position: relative;
        align-items: center;
        z-index: 15;
        justify-items: center;
    }
    .organizationStructure .mid-button li {
        width: 4rem;
        position: relative;
        padding-top: 2.4rem;
        text-align: center;
    }
    .organizationStructure .mid-button a {
        display: inline-block;
        width: 4rem; height: 4rem;
        border-radius: 10rem;
        background-color: var(--pink-light);
        color: var(--white);
        font-size: var(--fs-2-8);
        font-weight: 400;
        line-height: 4rem;
    }
    .organizationStructure .mid-button li em {
        content: '';
        width: .1rem; height:2.4rem;
        background-color: var(--gray-lev11);
        position: absolute; top: 0;
        margin-left: -.1rem;
    }

    .organizationStructure .mid-button li:nth-child(1) em {
        left: 53%;
    }
    .organizationStructure .mid-button li:nth-child(2) a {
        margin-left: -.1rem;
    }
    .organizationStructure .mid-button li:nth-child(2) em {
        left: 48%;

    }
    .organizationStructure .mid-button li:nth-child(3) a {
        margin-left: -4.1rem;
    }
    .organizationStructure .mid-button li:nth-child(3) em {
        right:100%;
    }

    .organizationStructure .bottom {
        z-index: 10;
    }


    .organizationStructure .bottom li {
        display: none;
        position: relative;
        text-align: center;
    }
    .organizationStructure .bottom li:nth-child(1) {
        padding-top: 14rem;
        margin-top: -6.4rem;
    }
    .organizationStructure .bottom li:nth-child(1)::after {
        content: '';
        width: .1rem; height:14rem;
        background-color: var(--gray-lev11);
        position: absolute; left: 10.5%; top: 0;
        margin-left: -.1rem;
    }

    .organizationStructure .bottom li:nth-child(2) {
        padding-top: 10rem;
        margin-top: -6.4rem;
        text-align: right;
    }
    .organizationStructure .bottom li:nth-child(2)::after {
        content: '';
        width: .1rem; height:10rem;
        background-color: var(--gray-lev11);
        position: absolute; left: 89.6%; top: 0;
        margin-left: -.1rem;
    }


    .riskManagementPolicy .list-area {
        padding: 3.1rem 3.8rem;
    }
    .riskManagementPolicy .list-area ol {
        grid-template-columns: 1fr;
    }
    .sub-txt-01 {
        width: 95%;
    }

    .contactUs {
        padding: 0 1.6rem;
    }
    .contactUs .left,
    .contactUs .right {
        padding: 2.4rem;
    }
    .contactUs .left .info-01 {
        margin-top: 3rem;
    }
    .contactUs .left .info-01 h4 {
        text-align: center;
    }
    .contactUs .right {
        grid-template-columns: 1fr;
        grid-column-gap: 0;
        grid-row-gap:0;
    }
    .contactUs .right h3.tit {
        grid-column: 1;
        margin-bottom: 2.4rem;
    }
    .contactUs .font-25 {
        font-size: 22px !important;
    }
    .contactUs .right .con-left {
        width:100%;
        margin-bottom: 4rem;
    }
    .contactUs .customerService {
        padding: 2.4rem;
    }
    .contactUs .customerService .service-con {
        width:100%;
        margin: 4rem auto 0;
        grid-template-columns:1fr;

    }
    .contactUs .map-contents {
        flex-direction: column;
        justify-content: stretch;

    }
    .contactUs .map-contents .info-01 {
        margin-bottom: 2.4rem;
    }

    .contactUs .customerService .service-con .right-con {
        order: 1;
        margin: 0 auto;
    }
    .contactUs .customerService .service-con .left-con {
        order: 2;

    }
    .contactUs .customerService .service-con .left-con .list {
        grid-template-columns:1fr;
        margin-left: 6rem;
    }
    .investment-custom {
        display: flex;align-items:center; width: 100%;
    }
    .margin-left-20 {
        margin-left: 0;
    }
    .contactUs .con-right {
        margin-bottom: 4rem
    }
    .contactUs  ul.list li::before{
        content: '';
        width: 1.4rem;
        height: 2.2rem;
        display: block;
        padding-left: 2rem
    }
    .contactUs .koreanDesk {
        padding: 2.4rem;
    }
    .contactUs .map-contents {
        margin-top: 1rem !important;
    }
    .contactUs .koreanDesk .desk-con {
        width:100%;
        margin: 4rem auto 0;
        grid-template-columns:1fr;

    }
    .contactUs  .representative .label {
        display: none !important;
    }
    .contactUs .koreanDesk .desk-con .left-con {
        order: 1;
        margin: 0 auto;
    }
    .contactUs .koreanDesk .desk-con .right-con {
        order: 2;

    }

    .contactUs .koreanDesk .desk-con .right-con .list {
        grid-template-columns:1fr;
        margin-left: 6rem;
    }
    .contactUs .koreanDesk .desk-con .right-con li {
        margin-top: 0;
        margin-left: 0;
    }
    .faq-list {
        grid-template-columns: 1fr;
        gap: 1.2rem;
        padding: 1.6rem;
        margin: 0 0 3.2rem;
    }
    .faq-list dl {
        padding: .8rem;
    }
    .faq-list dt {
        padding: .8rem;
    }
    .faq-list dt p {
        font-size: var(--fs-1-8);
    }
    .faq-list dt button {
        align-self: self-start;
        margin-top: .6rem;
    }
    .faq-list .title {
        height: 4.8rem;
        font-size: var(--fs-2);
        align-items: center;
    }
    .faq-list .title::before {
        width: 4rem;
    }
    .pRIVACYPOLICY-top {
        width: calc(100% - 3.6rem);
        margin: -6rem auto 6rem;
        padding: 1.6rem;
        grid-template-columns: 1fr;
        gap: 1.6rem;
        justify-content: center;
    }
    .pRIVACYPOLICY-top::before {
        margin: 0 auto;
    }
    .two-col-content {
        grid-template-columns: 1fr;
        padding: 0 1.6rem;
        gap: 1.6rem;
    }
    .only-text-wrap {
        padding: 0 1.6rem;
        margin: 4rem 0;
    }
    .only-text-wrap p {
        margin-bottom: 2rem;
    }
    .OpenAccount-main {
        max-width: 100%;
    }
    .OpenAccount-main .arrow-01,
    .OpenAccount-main .arrow-02,
    .OpenAccount-main .arrow-03 {
        display: none !important;
    }
    .openAccountStep {
        grid-template-columns: 1fr;
    }
    .openAccountStep li {
        width: 60%;
        margin: 0 auto;
    }
    .openAccountStep {
        gap:5.2rem;
    }

    .information-card {
        grid-template-columns: 1fr;
        padding: 0 1.6rem;
    }
    .openAccount-bottom-info p {
        width: 80%;
        margin: 0 auto;
        text-align: center;
    }
    .aboutUs-top p {
        width: 80%;
        margin: 0 auto;
        line-height: var(--line-2-2);
        font-weight: 400;
    }
    .aboutUs-main ul {
        grid-template-columns: 1fr;
        padding: 0 1.6rem;
    }

    .information-card li.wrap:nth-child(1),
    .information-card li.wrap:nth-child(2) {
        background-position: 95% 1.5rem;
    }
    .aboutUs-top {
        margin: 4rem 0 0;
    }
    .aboutUs-main {
        margin: 1.5rem 0 0;
    }
    .aboutUs-main .aboutUs-main-bg {
        width: 100%; height: auto;
        position:static ;

    }
    .aboutUs-main .aboutUs-main-bg img {
        width: 100%;
    }
    .aboutUs-main .top {
        width: 3.6rem; height: 3.6rem;
        background-size: 3.6rem 3.6rem;
        margin-bottom: 0;
    }
    .aboutUs-main .bottom::after {
        width: 0px; height: 0px;
        content:'';
        position: absolute;
        right: 0; top: 50%;
        margin-top: -.3rem;
        border-bottom: .6rem solid var(--gray-lev4);
        border-right: 0.55rem solid transparent;
        border-left: 0.55rem solid transparent;
        transform: rotate(180deg);
    }
    .aboutUs-main ul li.on .bottom::after {
        transform: rotate(0deg);
    }
    .aboutUs-main ul {
        max-width: 95%;
        grid-row-gap:0;
        grid-column-gap:0;
    }
    .aboutUs-main ul li {
        display: grid;
        grid-template-columns: 3.6rem 1fr;
        grid-template-rows: 3.2rem 1fr ;
        align-items: center;
        grid-column-gap: 1.6rem;
        grid-row-gap: 0;
        border-bottom: .1rem solid var(--gray-lev6);
        margin-bottom: .8rem;
    }
    .aboutUs-main h4 {
        margin-bottom: 0;
        font-size: var(--fs-2);
    }
    .aboutUs-main .sub-txt {
        grid-column: 1/span 2;
        font-size: var(--fs-1-6);
        overflow: hidden;
        animation-duration: .1s;
        animation-name: heightHide;
        animation-fill-mode: forwards;
        transition: maxHeight;
    }
    .aboutUs-main ul li.on  .sub-txt {
        margin-top: 2.3rem;
        animation-duration: .5s;
        animation-name: heightShow;
        animation-fill-mode: forwards;
        transition: maxHeight;
    }
    .main-affiliated {
        overflow-x: auto;
    }

    .main-affiliated ul {
        width: 98.5rem;
        margin: 4rem auto;
        display: flex;
        flex-direction: row;
        gap:4rem;

    }
    .product-tit {
        margin: 3.3rem 0  4.2rem 0;
    }
    .product-wrap-main .product-item-01 {
        padding: 2rem;
    }
    .product-item-01 .mid ul {
        padding-bottom: 2.4rem;
    }
    .product-item-01 .bottom .star-aria p {
        width: 9rem;
        font-size: var(--fs-1-2);
    }
    .product-wrap-main {
        display: block;
        padding: 0 1.6rem;
    }

    .product-wrap-main .left {
        margin-bottom: 2.4rem;
    }
    .product-wrap-main .right .items-wrap {
       display: flex;
       grid-row-gap:0;
       grid-column-gap:0;
       transform:unset;
    }
    .product-wrap-main .right li.item {
        width: 100%;
        padding: 1.2rem;
    }
    .product-wrap-main .right li.item .bottom {

    }
    .product-wrap-main .right li.item .bottom .right {
        width: 15rem;
    }
    .product-wrap-main .right li.item .bottom .left {
        margin-bottom: 0;
    }
    .product-wrap-main .right li.item .bottom p {
        font-size: var(--fs-1);
        line-height: var(--line-1-2);
    }
    .product-swiper {
        overflow: hidden !important;
    }
    .product-wrap-main .swiper-pagination {
        bottom: -3rem !important;
        display: block;
    }
    .product-wrap-main .swiper-pagination .swiper-pagination-bullet {
        background-color: var(--gray-lev11);
    }
    .product-wrap-main .swiper-pagination .swiper-pagination-bullet-active {
        width: 2rem;
        border-radius: .4rem;
        background-color: var(--gray-lev9);
    }
    .reseach-main {
        padding: 5.3rem 0 5.1rem;
        min-height: auto;
    }
    .reseach-main .reseach-tit {
        margin-bottom: 1.5rem;
    }
    .research-main-contents .title::before {
        width: 4.4rem; height: 4.4rem;
        background-repeat: no-repeat;
        background-size: cover;;
    }
    .reseach-main .top-area {
        display: grid; height: auto;
        grid-template-rows: minmax(auto, 17rem) 1fr;
        gap:4rem;
        margin-bottom: 3rem;
    }

    .reseach-main .research-swiper {
        width: 100%;
        grid-area:1;
        padding: 0 1.6rem;
        height: auto !important;
        overflow: visible; z-index: 20;

    }
    .reseach-main .research-swiper li img {
        width: 100%; height: auto; max-height: 17rem;
    }
    .research-swiper .swiper-pagination {
        bottom: -2.5rem !important;
        padding: 0 1.6rem;
    }
    .reseach-main .research-main-contents {
        grid-area: 2;
        padding: 0 1.6rem;
        width: 100%;
    }
    .research-main-contents .title {
        padding-bottom: 1.6rem;
    }
    .research-main-contents .title h4 {
        font-size: var(--fs-2);
    }
    .research-main-contents .research-main-list li a {
        padding-bottom: 1.6rem;
    }
    .research-main-contents .research-main-list li a .frist p {
        font-size: var(--fs-1-6);
    }
    .reseach-main .bottom-area ul {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
    }
    .reseach-main .bottom-area ul li a::before,
    .reseach-main .bottom-area ul li a::after {
        display: none;
    }
    .reseach-main .bottom-area {
        padding: 0 1.6rem;
    }
    .reseach-main .bottom-area ul li a {
        padding-top:0;
        height: 4.8rem;
        border: .1rem solid var(--blue-light);
    }
    .reseach-main .bottom-area ul li.on a {
        border: .1rem solid var(--white);
        background-color: var(--blue-light) ;
    }
	.login-area {
		height: auto;
	}
    .login-area .login-area-wrap {
        width: 95%; height: auto;
        grid-template-columns: 1fr;
        margin: 0 2.5%;
        padding: 6% 0;

    }
    .login-area .login-main-img {
        width: 100%;  min-height: 20rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .main-banner-mo .swiper-slide img{
        width: 100%;
    }
    /* DUMMY */
    .dummy-banner .pc {
        display: none;
    }
    .dummy-banner .mo {
        display: block;
    }
    .dummy-banner .mo img {
        width: 100%;
    }

    .researchTeam-wrap {
        padding: 0 1.6rem;
        width: 100%;
    }

    .researchTeam-wrap .researchTeam-list {
        grid-template-columns: 1fr !important;
        height: auto !important;
        padding: 0 0 2.4rem 0;
        justify-items: center;
    }
    .researchTeam-wrap .researchTeam-list .img img {
        width: 100%;
    }
    .researchTeam-wrap .researchTeam-list .text {
        margin-left: 0;
        margin-top: 2.4rem;
        text-align: center;
    }
    .researchTeam-wrap .text h4 {
        font-weight: 600;
        font-size: var(--fs-2);
        line-height: var(--line-2-8);
        color: var(--black-lev1);
        margin-bottom: .8rem;
    }
    .researchTeam-wrap .text p {
        font-weight: 700;
        font-size: var(--fs-1-3);
        color: var(--black-lev3);
        margin-bottom: .8rem;
    }
    .researchTeam-wrap .text div {
        font-size: var(--fs-1);
        line-height: var(--line-1-4);
        color: var(--gray-lev10);
    }
    .researchTeam-wrap .researchTeam-list.bg2 .text {
        margin-left: 0;;
        grid-area: 2;
    }
    .researchTeam-wrap .researchTeam-list.bg5 .text {
        margin-left: 0;;
        grid-area: 2;
    }
    .content-tit.openAccount {
        margin: 4rem 0 3.2rem;
    }

    .card-item-list {
        padding: 0 1.6rem;
        margin:0;
        width: 100%;
        grid-template-rows:auto;
    }
    .card-item-list .card-item .top p {
        font-size: var(--fs-2);
        line-height: var(--line-2-4);
    }
    .card-item-list .card-item .bottom {
        width: 100%;
        padding: 2.4rem 0;
        display: block;
    }
    .card-item-list .card-item .bottom .icon-group {
        margin: 0 auto;
    }
    .card-item-list .card-item .bottom.card-area-fixed {
        display: grid;
    }
    .card-item-list .card-item .bottom.card-area-fixed .text-wrap {
        grid-area: 2;
    }
    .card-item-list .card-item .bottom.card-area-fixed .text-wrap .caption-list {
        margin-top: 1.6rem;
    }
    .card-item-list .card-item .bottom.card-area-fixed .openAccount-icon2 {
        grid-area: 1;
    }
    .card-item-list .card-item .bottom .text-wrap {
        width:100%;
        padding: 0 2.4rem;

    }
    .card-item-list .card-item .bottom .openAccount-icon1,
    .card-item-list .card-item .bottom .openAccount-icon2,
    .card-item-list .card-item .bottom .openAccount-icon3 {
        width: 100%;
        margin-bottom: 4rem;
    }
    .sub-tit-button-style {
        width: calc(100% - 3.6rem);
        margin: 0 auto;
    }
    .introduction-icon {
        width: calc(100% - 3.6rem);
        margin: 2.4rem auto;
    }
    .introduction-icon ul {
        flex-direction: column;
        gap: 1.6rem;
    }
    .introduction-icon ul span {
        text-align: center;
        margin-top: 1.6rem;
    }
    .introduction-swipe {
        position: relative;
        margin-bottom: 5.4rem;
        padding: 3rem 1.6rem 6.5rem;
    }
    .introduction-swipe-01 {
        width: 100%;


    }
    .introduction-swipe-01 ul {
        justify-content: flex-start;
        gap:0;
    }
    .introduction-swipe-01 .scene-01 {
        background-size: 190% ;
        background-position: 68% 15% ;
    }
    .introduction-swipe-01 .scene-02 {
        background-size: 190% ;
        background-position: 68% 15% ;
    }
    .introduction-swipe .swiper-pagination {
        display: block;
        bottom: 8rem !important;
    }
    .introduction-swipe .swiper-pagination .swiper-pagination-bullet {
        background-color: var(--gray-lev11);
    }
    .introduction-swipe .swiper-pagination .swiper-pagination-bullet-active {
        width: 3rem;
        border-radius: .4rem;
        background-color: var(--gray-lev9);
    }

    .introduction-swipe .swiper-pagination2 {
        display: block;
        bottom: 2rem !important;
        text-align: center;
        position: absolute;
        z-index: 10;
    }
    .introduction-swipe .swiper-pagination2 .swiper-pagination-bullet {
        background-color: var(--gray-lev11);
    }
    .introduction-swipe .swiper-pagination2 .swiper-pagination-bullet-active {
        width: 3rem;
        border-radius: .4rem;
        background-color: var(--gray-lev9);
    }


    .box-upper-style-01 {
        width: 90%; height: auto;
        margin: -10rem auto 0;
        flex-direction: column;
        gap:0;
        padding: 2.4rem 0 0;
    }
    .howToUse {
        padding: 0 1.6rem;
    }
    .howToUse .arrow {
        display: none;
    }
    .howToUse ul {
        grid-template-columns: 1fr;
        gap: 2.4rem;
    }
    .howToUse ul li {
        width: 100%;
        height: auto;
    }
    .howToUse.hts ul li:nth-child(3) {
        order: 3;
    }
    .howToUse.hts ul li:nth-child(4) {
        order: 4;
    }
    .howToUse .bottom img {
        width: 100%;
    }
    .introduction-swipe-02-wrap {
        width: auto;
        padding: 0 1.6rem 5.6rem 1.6rem;
        position: relative;
    }
    .introduction-swipe-02 ul {
        gap:0;
        justify-content:unset;
    }
    .introduction-swipe-02 li {
        filter: none;
    }
    .introduction-swipe-02 li div {
        background-size: 100% 100%;
    }
    .introduction-swipe-02 li img {
        width: 100%;
    }

    .howToFeatures {
       width:calc(100% - 3.6rem); height: auto;
       padding: 3.7rem 1.6rem;
       margin: 0 auto;
    }
    .howToFeatures ul {
        grid-template-columns: 1fr;
        grid-column-gap:0;
        grid-row-gap:4rem;
    }
    .howToFeatures li {
        width: 100%;
    }
    .howToFeatures li:first-child {
        justify-content: center;
        height: 5rem;
    }
    .introduction-mts {
        padding: 3rem 0 15rem !important;
    }
    .box-upper-style-02 {
        width: 90%; height: auto;
        gap:2rem;
        margin: -15rem auto 0;
        flex-direction: column;
        padding: 2.4rem 0;
    }
    .box-upper-style-02 p {
        text-align: center;
    }
    .box-upper-style-02 p span {
        display: block;
        font-weight: 700;
    }
    .content-tit.hero-title-mts {
        padding: 0 1.6rem;
    }
    .howToUseMts ul {
        grid-template-columns: 1fr;
        padding: 0 1.6rem;
        grid-row-gap: 2.4rem;
    }
    .howToUseMts ul li {
        width: 100%;
    }
    .howToUseMts .arrow {
        display: none;
    }
    .trialId-box {
        display: none;
    }
    .howto-tab {
        padding: 0 1.6rem;
    }
    .howto-tab ul {
        width: 100%;
    }
    .howto-tab ul li::before {
        width: 4rem; height: 4rem;
        margin: -2rem 0 0 -6.5rem;
    }


    .howto-tab-wrap .arrow-list-pink2 {
        width: 80%;
        margin: 0 auto 3rem;
    }
    .howto-tab-wrap .arrow-list-pink2 li {
        align-items: flex-start;
        margin: 0 auto;
        max-width: 86%;
        justify-content:center;
        text-align: center;
    }
    .howto-tab-wrap .arrow-list-pink2 li::before {
        padding-top: 1.1rem;
    }
    .howto-tab-wrap  .howToInstall .arrow {
        display: none;
    }
    .howToInstall-swipe-01,
    .howToInstall-swipe-02 {
        padding: 2.4rem 1.7rem;
        position: relative;
    }
    .howToInstall .swiper-wrapper {
        display:flex;
        justify-content:stretch;
        gap:0;

    }
    .howToInstall ul li:nth-child(4),
    .howToInstall ul li:nth-child(5) {
        margin-left: 0;
    }
    .howToInstall-swipe-01 .swiper-pagination,
    .howToInstall-swipe-02 .swiper-pagination {
        display: block;
        bottom: -4rem !important;
    }
    .howToInstall-swipe-01 .swiper-pagination-bullet,
    .howToInstall-swipe-02 .swiper-pagination-bullet {
        background-color: var(--gray-lev11);
    }
    .howToInstall-swipe-01 .swiper-pagination-bullet-active,
    .howToInstall-swipe-02 .swiper-pagination-bullet-active {
        width: 3rem;
        border-radius: .4rem;
        background-color: var(--gray-lev9);
    }
    .screenshot-mts {
        width: 100%;
        grid-template-columns: 100%;
        grid-row-gap:2rem;
        grid-template-rows: auto;
        position: relative;
        margin-bottom: 5rem;;
    }

.screenshot-tap-triger  {
    width: 100vw !important;
}


    .screenshot-mts .screenShot-tab .wrap {
        padding: 3.4rem 2.4rem 8.8rem ; height: auto;

    }
    .screenshot-mts .screenShot-tab .wrap .wrap-detail {
        width: 100%;height: auto;

    }
    .screenshot-mts .screenShot-tab {
        width: calc(100vw - 3.6rem); height: auto;

    }
    .screenshot-mts .screenShot-tab ul {
        justify-content: stretch;
    }
    .screenshot-mts .screenShot-tab ul li {
        text-align: center;
    }
    .screenshot-mts .screenShot-tab ul li img {
        width: 100%;
    }
    .screenshot-swiper-move-btn {
        display: none;
    }

    .fit-letter {
        letter-spacing: -.1rem;
    }



    .err-page-msg {
        flex-direction: column;
        gap:0;
        justify-content: space-between;
        padding: 1.6rem;
    }
    .err-page-msg .err-msg-img {
        max-width: 70%;
        margin-top: 2rem;
    }
    .err-page-msg .err-msg-img img {
        width:100%;
    }
    .management-list li .wrap {
        box-shadow: 2px 2px 8px  var(--gray-lev5);
    }
    .management-list .swiper-move-btn-area {
        width: 100%; height: 4rem;
        position: absolute;
        left: 0; top: 20rem;
        display: flex;
        justify-content: space-between;
    }
    .management-list #aboutUs-01-prev{
        display: block;
        width:4rem; height: 4rem;
        background-image: url(../images/btn/btn_swiper_aboutus_arrow_right.svg);
        transform: rotate(180deg);
        left: 0rem;
    }
    .management-list #aboutUs-01-next{
        display: block;
        width:4rem; height: 4rem;
        background-image: url(../images/btn/btn_swiper_aboutus_arrow_right.svg);
        right: 0rem;
    }
    .management-list #aboutUs-01-prev::after,
    .management-list #aboutUs-01-next::after {
        display: none;
    }

    .introduction-swipe-01 li {
        filter: none;
    }

    .world-index {
        width: 90%; height: 5.6rem;
        position: absolute;
        left: 5%; bottom: 4.6rem;
        z-index: 20;
        justify-content: space-between;
        margin-left: 0;
    }
    .world-index .wrap {
        width: 100%;
    }
    .world-index ul {
        width: calc(100% - 9rem);

    }
    .notice-system-alarm {
        width: 100%;
        padding: 0 1.6rem;
        margin: 0 auto;
        position: relative;
    }

    .notice-system-alarm .top h2 {
        line-height: var(--line-3-4);
        font-size: var(--fs-2-4);
        text-align: left;
    }
    .rounding-off {
        margin-top: -10rem
    }
    .rounding-off .mts-intro-top {
        width:95%;
        margin: 0 auto;
        padding: 3.1rem 1.6rem 0;
    }
    .mts-intro-top h4 {
        max-width: 80%;
        margin: 0 auto 1.6rem;
    }
    .mts-intro-top span {
        font-size: var(--fs-1-8);
        font-weight: 700;
    }
    .mts-intro-top h3 {
        line-height: var(--line-3-4);
    }
    .mts-intro-mid {
        padding: 0;
        margin-top: 4rem;
    }
    .mts-intro-mid .bg {
        width: 100%;
        margin-left: 0%;
        padding-top: 7rem;
    }
    .mts-intro-mid .bg img {
        width: 100%;
    }
    .mts-intro-mid  .sticker {
        width: 8.1rem;
        right: 73%; top: 2rem;
    }
    .mts-intro-mid.hts  .sticker {
        width: 8rem;
        right: 5%; top: 10%;
    }
    .mts-intro-mid  .sticker img {
        width: 100%;
    }
    .mts-intro-mid .sticker2 {
        width: 8rem; height: 8rem;
        right: 3%; top: 2rem;

    }
    .mts-intro-mid .sticker2 img  {
        width: 100%;
    }
    .mts-intro-mid .down2 {
        width: 90%;
        top: -4rem;
        flex-direction: row;
        left: 5%;
        justify-content: space-around;
    }
    .mts-intro-mid .down {
        width: 90%;
        top: -4rem;
        flex-direction: row;
        left: 5%;
        justify-content: space-around;
    }
    .mts-intro-mid .down li a {
        max-width:14.2rem;
        display: block;
    }
    .mts-intro-mid .down li a img {
        width: 100%;
    }
    .mts-intro-top  .download {
        display: none;
    }
    .introduction-icon ul {
        display: table;
    }
    .introduction-icon ul li {
        display: table-cell;
    }
    .comprehensiveFeature {
        height: auto;
        padding-bottom: 15rem;
    }
    .comprehensiveFeature .contents-wrap {
        grid-template-columns: 100%;
        padding: 0 1.6rem;

    }
    .comprehensiveFeature .top {
        height: 3.4rem;
        margin:0 0 .2rem;
        grid-row: 1;
        grid-column:1;


    }
    .comprehensiveFeature .top h4 {
        font-size: var(--fs-2-8);
    }
    .comprehensiveFeature .left {
        grid-row: 3;
        padding-top: 0;
        height: 4rem;
        position: relative;
    }
    .comprehensiveFeature .left::before {
        content: ''; display: block;
        width: 100%; height: 100%;
        background: transparent;
        position: absolute;
        left:0; top:0;
        z-index: 30;
    }
    .comprehensiveFeature .right {
        width: 100%;

        grid-row:2;
    }
    .comprehensiveFeature .right li.swiper-slide {
        height: auto;
    }

    .comprehensiveFeature .right li.swiper-slide img {
        width: 100%;
    }

    .comprehensiveFeature .left .swiper-pagination-bullet-active {

        animation: quickmenusUp .5s cubic-bezier(.3,0,.3,1) forwards;
    }
    .comprehensiveFeature .left .swiper-pagination-bullet::after {
        display: none;
    }
    .comprehensiveFeature .left .swiper-pagination-bullet {
        position: absolute;
        left: 0; top: 0;
        opacity: 0;
        font-size: var(--fs-1-6);
        display: grid;
        grid-template-columns: 4rem 1fr;
        line-height: var(--line-2-2);

    }
    .comprehensiveFeature .left .swiper-pagination-bullet span {

        font-size: var(--fs-2);
    }
    .comprehensiveFeature .right .comprehensiveFeature-nav {
        width: 100%;
        position: absolute;
        left: 0; bottom: -11rem ;
    }
    .comprehensiveFeature-pagination {
        bottom: -10rem !important;  left:0;
        position: absolute;

        text-align: center;
        display: block;
    }
    .graybullet-page .swiper-pagination-bullet,
    .comprehensiveFeature-pagination .swiper-pagination-bullet {
        background-color: var(--gray-lev11);
    }
    .graybullet-page .swiper-pagination-bullet-active,
    .comprehensiveFeature-pagination .swiper-pagination-bullet-active {
        width: 3rem;
        border-radius: .4rem;
        background-color: var(--gray-lev9);
    }
    .howToUse-mts h4 {
        font-size: var(--fs-2);
    }
    .howToUseMtsStep span {
        display: none;
    }
    .howToUseMtsStep ul {
        grid-template-columns: 1fr;
        gap: 2.4rem;
    }
    .howToUseMtsStep ul li .right img {
        width: 100%;
    }
    .howToUse-mts .contents-wrap {
        padding: 0 1.6rem;
    }
    .howToUseMtsStep ul li {
        width: 100% !important;

    }

    .howToUseMtsStep li .left p {
        height: 4.4rem;
    }

    .howToUse-mts .youtube-area {
    	width: 100%; height: 31.5rem;
    }

    .requestTrialID {
        height: 38rem;
        padding: 17rem 0 0 0;
        margin: -15rem 0 0 0;
    }
    .requestTrialID.hts {
        margin: -13rem 0 0;
    }
    .requestTrialID .btn-wrap {
        flex-direction: column;
    }
    .howToInstall-txt.content-tit{
        margin: 3.2rem 0 1.6rem;
    }
    .howto-tab {
        margin-bottom: 1.6rem;
    }
    .howToApp .swiper-slide {
        padding: 0 4rem;
    }
    .howToApp .swiper-slide-active .txt {
        animation: quickmenusUp .7s cubic-bezier(.3,0,.3,1) forwards;
    }
    .howToApp.hts h4 {
        font-size: var(--fs-1-4);
    }

    .howToApp .swiper-slide-active .img {
        animation: quickmenusUp 1s forwards;
    }
    .howToApp .graybullet-page {
        position: absolute;
        left: 77% !important; top: 2rem !important;
    }
    .howToApp.hts .graybullet-page {
        position: absolute;
        left: 0% !important; top: 90% !important;
        text-align: center;
    }
    .howToApp.hts .howToApp-btnWrap {
        width: 95%;
        display: block;
        left:2.5%; top: 90%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }
    .howToApp-btnWrap {
        width: 95%;
        position: absolute;
        left:2.5%; top: 50%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        z-index: 10;
    }
    .howto-tab-wrap {
        margin-bottom: 0;
    }
    .preview-txt.content-tit{
        margin: 3.2rem 0 2.4rem;
    }
    .preview {
        padding: 0 1.6rem;
    }
    .screenshot-tap-triger span {
        display: none !important;
    }

    .screenshot-mts .screenshot-menu li.on a::before,
    .screenshot-mts .screenshot-menu li a:hover::before {
        display: none;
    }
    .screenshot-mts .screenshot-menu li {
        width: auto;
        max-width: 20rem;
        text-align: center;

        display: inline-block;
        padding: 1.2rem 1.6rem;
        border-radius: 4rem;



    }
    .screenshot-mts .screenshot-menu li.on {
        background: var(--blue-mid);
    }
    .screenshot-mts .screenshot-menu li a {
        border-bottom: 0;
        padding: 0;
        display: inline-flex;
        align-items: center;

    }
    .screenshot-mts .screenShot-tab .wrap {
        padding: 3.4rem 2.4rem;
    }
    .screenshot-mts .screenshot-menu li h6 {
        font-size: var(--fs-1-6);
        line-height: var(--line-1-6);
        color: var(--gray-lev10);
        line-height: var(--line-1-4);
        margin-bottom: 0;
        padding-left: 0;
        font-weight: 400;

    }
    .screenshot-mts .screenshot-menu li.on a h6,
    .screenshot-mts .screenshot-menu li a:hover h6 {
        color: var(--white);
    }

    .screenshot-mts .screenshot-next {
        width: 3.2rem; height: 3.2rem;
        position: absolute;
        right: -1.6rem; top: 1.2rem;
        background: url(../images/btn/btn_preview_right.svg) no-repeat 0 0;
        display: block;
        z-index: 20;
    }
    .screenshot-mts .screenshot-prev {
        width: 3.2rem; height: 3.2rem;
        position: absolute;
        left: -1.6rem; top: 1.2rem;
        background: url(../images/btn/btn_preview_left.svg) no-repeat 0 0;
        display: block;
        z-index: 20;
    }
    .img-pop {
        width: 100%;
        max-width: 95%;

        left: 2.5%; top: 50%;

        transform: translate(0%, -50%);


    }

    /*ETC CONTENTS*/
    .sub-header-type01 {
        position: fixed;
        left: 0; top: 0;
        z-index: 30;
        background-color: var(--white);
    }
    .sub-header-type01 .wrap {
        width: 100%;

        padding: 0 1.6rem;
    }
    .sub-contents-type01 {
        width: 100%;
        padding: 0 1.6rem;
        grid-template-columns: 1fr;
        gap: 2.3rem;
    }
    .sub-contents-type01 {
        padding-top: 8rem;
    }
    .sub-contents-type01 .contents-bg-area {
        width: 60%;
        margin: 0 auto;
        padding: 2.4rem 0 0;
    }
    .sub-contents-type01 .contents-bg-area img {
        width: 100%;
    }
    .sub-contents-type01 .title {

        margin-bottom: 2.4rem;
    }
    .sub-contents-type01 .title h1 {
        font-size: var(--fs-2-8);
        line-height: var(--line-2-8);

    }
    .sub-contents-btn-area {
        margin-bottom:4rem;

    }
    .sub-contents-btn-area .btn {
        height: 4.8rem;
    }

    .bg-title-gray {
        background-color: var(--gray-lev7);
    }
    .rgst-info {
        width: 100% !important;
    }
    .promo-report-list {
        padding: 1.8rem 1.6rem 0 1.6rem !important;
        margin-top: -7rem;
    }
    .rgst-info {
        display: flex;
        align-items: center;
        justify-content: start !important;
        font-size: 16px;
        color: #666;
        gap: 8px; /* Adds spacing between items */
        width: 30%
    }

    .promo-bottom {
        display: grid;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: gray;
        padding-bottom: 4px;
    }
    .promo-bottom .right {
        margin-top: 1rem;
    }

}


@keyframes arrowUpOn{
    from{
        transform: rotate(180deg);
        border-bottom: .6rem solid var(--gray-lev4);
    }
    to{
        transform: rotate(0deg);
        border-bottom: .6rem solid var(--blue-lev1);
    }
}
@keyframes arrowUpOff{
    from{
        transform: rotate(0deg);
        border-bottom: .6rem solid var(--blue-lev1);
    }
    to{
        transform: rotate(180deg);
        border-bottom: .6rem solid var(--gray-lev4);
    }
}
@keyframes heightShow{
    from {
        max-height: 0;
    }
    to{
        max-height: 50rem;
    }
}

@keyframes heightHide{
    from {
        max-height: 50rem;
    }
    to{
        max-height: 0rem;
    }
}
@keyframes rotate {
    from {
        transform:rotate(0deg);
    }
    to {
        transform:rotate(360deg);
    }
}
