@charset "UTF-8";

/* reset */
* {box-sizing: border-box;}
html, body {width: 100%; height: 100%;}
body {
  font-family: 'Arial';
}
html, body, div, span, button, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote,
pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small,
strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label,
legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure,
figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
  margin: 0;
  padding: 0;
}

ul, ol, li {
  list-style: none;
}
button {
  cursor: pointer;
}
article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
  display: block;
  margin: 0;
  padding: 0;
}

form {
  margin: 0;
  padding: 0;
}

fieldset {
  display: block;
  margin: 0;
  padding: 0;
  border: 0;
}

legend {
  position: absolute;
  visibility: hidden;
  overflow: hidden;
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
}

a, a:link, a:visited, a:active, a:hover,
button, button:link, button:visited, button:active, button:hover,
select, select:link, select:visited, select:active, select:hover {
  text-decoration: none;
  color: #292929;
}


img {
  border: 0;
  vertical-align: top;
}

hr {
  display: none;
}

em, address {
  font-style: normal;
}

h1, h2, h3, h4, h5, h6, strong {
  font-weight: 700;
}

table {
  width: 100%;
  border-collapse: collapse;
}

li {
  list-style: none;
}

.news-wrap {
  width: 300px; height: 89px;
  padding: 0 10px;
}
.news-wrap ul li:first-child {
  margin-top: 0;
}
.news-wrap li{
  margin-top: 6px;
 
}
.news-wrap li a {
  width: 100%;
  display: block;
  overflow: hidden;
  cursor: pointer;
}
.news-wrap li a span.txt {
  width: 210px;
  display: block;
  text-overflow: ellipsis;
  white-space:nowrap;
  overflow: hidden;
  float: left;
  color: #292929;
  font-size: 11px;
}
.news-wrap li a span.date {
  display: block;
  float: right;
  color: #666;
  font-size: 11px;
}




body.dark {
  background:#222529;
}

body.dark .news-wrap li a span.txt {
  color: #fff;
 
}
body.dark .news-wrap li a span.date {
 
  color: #8994A3;
 
}

.swiper-wrap {
  width: 220px; height: 220px;
  margin: 0 !important;
  overflow: hidden;
  position: relative;
}
#swiper-pagination {
  width: calc(100% - 18px);
  height: 5px;
  text-align: left;
  left: 18px;
  top: 195px !important;
  bottom:0;
  position: absolute;

  transition: .3s opacity;
 
}
#swiper-pagination .swiper-active-switch {
  width: 10px !important; height: 5px !important; 
  background-color: #E6007E !important;
  border-radius: 30px !important;
}
#swiper-pagination .swiper-pagination-switch {
  width: 5px; height: 5px;
  border-radius: 5px;
  -ms-border-radius: 5px;
  background: #C5D2EF;
  margin: 0 4px 0 0 !important;
  display: inline-block;
  cursor: pointer;
}

