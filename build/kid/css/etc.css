#header-hts {
    width: 100%; height: 6.4rem;
    border-bottom: .4rem solid var(--blue-mid);
    display: flex;
    justify-content: space-between;
    padding: 0 3rem;
    align-items: center;
}
#header-hts h1 {
    color: var(--blue);
    font-weight: 900;
    font-size: var(--fs-1-6);
    line-height: var(--line-2-4);
}
#header-hts div.icon-cs::before {
    content: ''; display: inline-block;
    width: 1.6rem; height: 1.6rem;
    background-image: url(../images/hts/icon_hts_01.svg);
    background-repeat: no-repeat;
    background-size: contain;
}
#header-hts div.icon-cs {
    font-size: var(--fs-1-4);
    line-height: var(--line-1-8);
    display: flex;
    align-items: center;
    gap: .6rem;
}
#header-hts div.lang {
    display: flex;
    align-items: center;
    gap:2rem;
}
#header-hts div.lang span {
    font-size: var(--fs-1-4);
}
#header-hts div.lang ul {
    display: flex;
    align-items: center;
    gap: 1.2rem;
}
#header-hts div.lang ul li button {
    width: 2.6rem; height: 2.6rem;

}
#header-hts div.lang ul li:nth-child(1) button {
    background-image: url(../images/etc/icon_lang_01_off.svg);
}
#header-hts div.lang ul li.on:nth-child(1) button {
    background-image: url(../images/etc/icon_lang_01_on.svg);
}
#header-hts div.lang ul li:nth-child(2) button {
    background-image: url(../images/etc/icon_lang_02_off.svg);
} 
#header-hts div.lang ul li.on:nth-child(2) button {
    background-image: url(../images/etc/icon_lang_02_on.svg);
}



.hts-contents {
    padding: 4.7rem 0; 
}
.hts-contents.complte {
    filter: blur(1rem);
    transition: filter 0.25s ease-in-out;
}
.hts-contents .wrap {
    width: calc(100% - 6rem);
    margin: 0 auto;
    border-bottom: .1rem solid #E0E0E0;
}
.form-list {
    max-width: 80%;
}

.form-list dt {
    font-size: var(--fs-1-4);
    line-height: var(--line-1-8);
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--gray-lev10);
}
.form-list dt span {
    color: var(--gray-lev9);
    margin-left: 1rem;
}
.form-list dd {
    margin-bottom: 1.6rem;
}
.form-list dd.err .inp-txt input {
    border: .1rem solid var(--pink-high);
}
.form-list dd.err .inp-txt input:focus {
    outline: max(.3rem, .1rem) solid var(--pink-high);
    transition: outline 0.25s ease-in-out;
}
.form-list dd.err .err-inp-txt {
    display: flex;
}
.inp-txt input {
    width: 100%; min-height: 4rem;
    border: .1rem solid var(--gray-lev14);
    border-radius: .6rem;
    padding: 0 1rem !important;
    color: var(--gray-lev8);
    font-size: var(--fs-1-4);
    line-height: var(--line-1-8);
}
.inp-radio {
    position: relative;
}
.inp-radio label {
    display: inline-flex;
    gap: .6rem;
    align-items: center;
    position: relative;
    font-size: var(--fs-1-4);
    line-height: var(--line-1-8);
}
.inp-radio input[type="radio"]:checked + label::before {
    border: .1rem solid var(--blue-mid);
}

.inp-radio input[type="radio"]:checked + label::after {
    background:var(--blue-mid);
    transition: background 0.25s ease-in-out;
}
.inp-radio input[type="radio"]:checked + label {
    color: var(--blue-mid);
}
.inp-radio input[type="radio"]:focus-visible + label::before{
    outline-offset: max(.2rem, 0.1rem);
    outline: max(.2rem, .1rem) dotted var(--blue-light);
}

.inp-radio input[type="radio"]:hover  + label::before {
    box-shadow: 0 0 0 max(.2rem, 0.2em) var(--blue-light);
    cursor: pointer;
    transition: box-shadow 0.3s ease-in-out;
}
  
.inp-radio label::before {
    content: '';
    display: block;
    width: 1.8rem; height: 1.8rem;
    border: .1rem solid var(--gray-lev9);
    border-radius: 100%;
}
.inp-radio label::after {
    content: '';
    display: block;
    width: .8rem; height: .8rem;
    position: absolute;
    left: .6rem; top: .7rem;
    background:var(--gray-lev9);
    border-radius: 100%;
}



.form-center-contents {
    padding-top: 2rem;
    text-align: center;
}
.form-center-contents .txt {
    color: var(--gray-lev10);
    font-size: var(--fs-1-4);
    line-height: var(--line-1-8);
}
.form-center-contents .inp-area {
    margin: 1.6rem 0 1.8rem;
}
.form-center-contents .inp-area .inp-radio {
    margin-left: 1.8rem;
}
.form-center-contents .inp-area .inp-radio:first-child {
    margin-left: 0rem;
}
.form-center-btn {
    max-width: 50%;
    margin: 2rem auto;
    display: flex;
    align-items: center;
    gap: 1rem;
}
.form-center-btn .btn {
    box-shadow: none;
    filter:none;
    font-size: var(--fs-1-4);
    line-height: var(--line-1-8);
}
.err-inp-txt {
    color: var(--pink-high);
    font-size: var(--fs-1-4);
    line-height: var(--line-1-6);
    display: none;
    align-items: center;
    margin-top: 1rem;
    gap: .6rem;
}
.err-inp-txt::before {
    content:'!';
    width: 1.6rem; height: 1.6rem;
    background-color:var(--pink-high);
    color: var(--white);
    line-height: var(--line-1-6);
    text-align: center;
    border-radius: 100%;
    display: block;

}

.complte-pop {
    position: absolute;
    left: 0; top: 6.4rem;
    z-index: 10;
    width: 100vw; height: 100vh; 
}

.complte-pop.find-id {
    border-radius: 5rem; 
    width: 90%; height: 30rem; 
    background: var(--white); 
    left: 50%; top: 10rem; 
    transform: translateX(-50%);
    box-shadow: 2px 2px 8px #D1D2D4;
}


.complte-pop .wrap {
    width: calc(100% - 6rem);
    margin: 0 auto;
    padding: 6.8rem 0;
    border-bottom: .1rem solid #E0E0E0;
}

.hts-contents h3 {
    font-weight: 700;
    font-size: var(--fs-3-2);
    line-height: var(--line-3-8);
    color: var(--blue);
   
}
.hts-contents h4 {
    margin: 4rem 0 2rem;
    font-weight: 700;
    color: var(--gray-lev10);
    font-size: var(--fs-1-6);
}
.table-type-01 table th {
    color: var(--white);
    background-color: #00709B;
    font-size: var(--fs-1-4);
    padding: 1rem 1.4rem;
    text-align: center;
}
.table-type-01 table td {
    padding: 1rem 1.4rem;
    border: solid #E0E0E0;
    border-width: 0 0 .1rem .1rem;
    color: var(--black-lev3);
    text-align: center;
}
.table-type-01 table tr > td:first-child {
    border-width: 0 0 .1rem 0rem;
}
.table-type-01 table td.left {
    text-align: left;
}
.sub-txt {
    margin-top: 2rem;
    color: #616264;
    font-size: var(--fs-1-4);
}

.content-txt .sub-txt{
    line-height: var(--line-1-8);
}
.content-txt h5 {
    font-size: var(--fs-1-4);
    font-weight:700;
    margin: 1.8rem 0 .6rem;
}


.mt2 {
    margin-top: 2rem;
}