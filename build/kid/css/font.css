@charset "UTF-8";
@font-face {
    font-family: 'Lato';
    font-weight: 400;
   
    src: local("Lato-Regular"), url("../font/Lato.woff2") format('woff2');;
}

@font-face {
    font-family: 'Lato';
    font-weight: 500;
    src: local("Lato-Regular"), url("../font/Lato.woff2")  format('woff2');
}

@font-face {
    font-family: 'Lato';
    font-weight: 700;
    src: local("Lato-Bold"), url("../font/Lato-bold.woff2")  format('woff2');
}
@font-face {
    font-family: 'Lato';
    font-weight: 800;
    src: local("Lato-Black"), url("../font/Lato-bold.woff2")  format('woff2');
}

@font-face {
    font-family: 'Montserrat';
    font-weight: 900;
    font-display: swap;
    src: local("Montserrat-Bold"), url(../font/Montserrat.woff2) format('woff2');
  
}
@font-face {
    font-family: 'Montserrat';
    font-weight: 600;
    font-display: swap;
    src: local("Montserrat-SemiBold"), url(../font/Montserrat-SemiBold.ttf) format('ttf');
  
}
@font-face {
    font-family: 'Montserrat';
    font-weight: 700;
    font-display: swap;
    src: local("Montserrat-Bold"), url(../font/Montserrat-Bold.ttf) format('ttf');
  
}


body, body * {
    font-family: 'Lato', 'AppleGothicNeoSD',  'Droid sans', sans-serif;
    -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: none; 
}
