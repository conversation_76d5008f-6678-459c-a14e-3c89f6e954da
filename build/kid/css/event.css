@charset "UTF-8";


/*S:FONT*/

/*E:FONT*/

/* S:COMMON */
.pc-object {
    display: block;
}
.mo-object {
    display: none;
}
.rel {
    position: relative;
}
.img {
    width: 100%;
    display: block;
}
.evt-detail {
    width: 144rem;
    margin: 0 auto;
    position: relative;
}
.evt-wrap.pop{
    filter: blur(1rem);
    transition: filter 0.25s ease-in-out;
}
/* E:COMMON */
.evt-01-top {
    height: 89.6rem;
    margin-bottom: 20.4rem;
    background-color: #E9F4FF;
}
.evt-01-top .evt-detail {
    height: 110rem;
    background: url(../images/event/2024-01/evt_01.png?20240502) no-repeat 0 0;
}
.evt-01-top .evt-top {
    padding-top: 5.6rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 4rem;
}
.evt-01-top .evt-top h2 {
    width: 12rem; height: 3rem;
}
.evt-01-top .evt-top h1 {
    text-transform: uppercase;
    font-size: 7.2rem;
    color: #222;
    font-weight: 900;
    letter-spacing: 0.072rem;
    line-height: 110%;
    font-family: 'Montserrat';
    margin-top: .8rem;
  
}
.evt-01-top .evt-top .evt-symbol {
    margin: 4rem 0 .4rem;
}
.evt-01-top .evt-top p.point-01 {
    color:#222;
    font-size: 2.6rem;
    line-height: 130%;
    font-weight: 600;
}

.evt-01-top .evt-top p.point-02 {
    color: #222;
    font-size: 2.8rem;
    line-height: 130%;
    font-weight: 700;
}
.evt-01-top .evt-top p.point-02 span {
    color: #FB2FFF;
}


.evt-01-top .evt-top p.point-03 {
    color:#6C8298;
    font-size: 2.4rem;
    font-weight: 600;
    line-height: 130%;
    font-family:  'Montserrat';
}
.evt-01-top .evt-mid {
    width: 125rem; height: 28rem;
    margin: 0 auto;
    display: flex;
   flex-direction: row;
}
.evt-01-top .evt-interation-wrap {
   width: 125rem;
   

}
.evt-interation-wrap.item2 {
    display: none;
  
}
.evt-01-top .evt-interation-wrap ul {
    width:125rem;
    display: flex;
    flex-direction: row;
    position: relative;
}
.evt-01-top .evt-interation-wrap ul li {
    position: absolute;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(1) {
    left: 0; top: 0;
    transform: rotate(-4deg); 
    scale: 95%;
    z-index: 10;
    animation: rotation1 1.4s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(2) {
    left: 14rem; top: 3rem;
    transform: rotate(4deg);
    z-index: 9;
    animation: rotation2 1.4s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(3) {
    left: 27rem; top: 5rem;
    transform: rotate(-7deg);
    scale: 90%; z-index: 10;
    animation: rotation3 1.4s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(4) {
    left: 39rem; top: 1rem;
    transform: rotate(15deg);
    z-index: 9;
    animation: rotation4 1.7s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(5) {
    left: 53rem; top: -2rem;
    transform: rotate(-3deg);
    scale: 90%; z-index: 10;
    animation: rotation5 1.3s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(6) {
    left: 64rem; top: 1rem;
    transform: rotate(4deg);
    animation: rotation6 1.4s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(7) {
    left: 75rem; top: 3rem;
    transform: rotate(-10deg);
    scale: 90%; z-index: 10;
    animation: rotation7 1.5s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(8) {
    left: 88rem; top: -2rem;
    transform: rotate(12deg);
    z-index: 9;
    animation: rotation8 1.4s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(9) {
    left: 97rem; top: -3rem;
    transform: rotate(-14deg);
    scale: 90%; z-index: 10;
    animation: rotation9 1.4s cubic-bezier(.3,0,.3,1) infinite;
}
.evt-01-top .evt-interation-wrap ul li:nth-child(10) {
    left: 110rem; top: 1rem;
    transform: rotate(2deg);
    animation: rotation10 1.4s cubic-bezier(.3,0,.3,1) infinite;
}




.evt-01-top .evt-bottom p{
    color: #6C8298;
    text-align: center;
}
.evt-01-top .evt-bottom button {
    width: 28rem; height: 5.6rem;
    margin: 0 auto 2.4rem;
    line-height: 5.6rem;
    display: block;
    border-radius: 1.2rem;
    color: var(--white);
    font-size: var(--fs-2-2);
    font-weight: 700;
    background: #EF2E93;
    font-family: 'Montserrat';
}

   
.evt-count-ticket {
    width: 13.6rem; height: 9.6rem;
    background: url(../images/event/2024-01/evt_05.svg?20240502) no-repeat 0 0;
    display: grid;
    grid-template-rows: 3.6rem 1fr;
    justify-items: center;
    align-items: center;
}

.evt-count-ticket span {
    color: var(--white);
    
}
.evt-count-ticket strong {
    font-size: var(--fs-2-8);
    font-weight: 700;
}
.evt-count-ticket strong a {
    text-decoration: underline;
    color: var(--white);
}
.evt-count-ticket strong.type-01 {
    color: #FFC700;
}

.evt-count-ticket strong.type-02 {
    color: var(--white);
}
.evt-count-ticket-wrap {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap:.8rem;
    margin-bottom: .8rem;
}

.evt-top-banner {
    width: 100vw; height: 83rem;
    background-position:50% 50%;
    background-repeat: no-repeat;
    text-indent: -9999rem;
}
.evt-con-detail,
.evt-benefit {
    width: 102.4rem;
    margin: 0 auto;
    padding: 8rem 0;
}
.evt-benefit .evt-top {
    position: relative;
    display: table;
    margin: 0 auto;
}
.evt-benefit .evt-top span {
    width: 28.6rem; height: 5.6rem;
    display: inline-flex;
    background: #282832;
    border-radius: 8rem;
    align-items: center;
    justify-content: left;
    padding-left: 3.2rem;
    color: var(--white);
    font-size: var(--fs-2-4);
    font-weight: 700;
    font-family: 'Montserrat';
    z-index: 1;
    text-transform: uppercase;
}
.evt-benefit .evt-top::before {
    content:'';
    width: 2.8rem; height: 2.2rem;
    display: block;
    background: url(../images/event/2024-01/evt_06.svg?20240502) no-repeat 0 0;
    position: absolute;
    right:32px; top: 47px;
}
.evt-benefit .evt-top em {
    width: 5.6rem; height: 5.6rem;
    background-color:  #C63583;
    border-radius: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--fs-2-4);
    font-weight: 700;
    font-family: 'Montserrat';
    margin-left: -5.6rem;
    z-index: 10;
    position: relative;

}
.evt-benefit .evt-top strong {
    width: 35.9rem; height: 5.6rem;
    border-radius: 8rem;
    margin-left: -5.6rem;
    background: #EE3897;
    display: inline-flex;
    align-items: center;
    justify-content: right;
    padding-right: 3.2rem;
    color: var(--white);
    font-size: var(--fs-2-4);
    font-weight: 700;
    font-family: 'Montserrat';
    z-index: 5;
    text-transform: uppercase;
}

.evt-benefit .evt-mid {
    margin-top: 6.6rem;
    display: grid;
    justify-content: space-between;
    grid-template-columns:  47.2rem 40rem;
    align-items: center;
}

.evt-benefit .evt-mid .evt-mid-count {
    height: 6.4rem;
    text-align: center;
    order: 1; grid-row: 1 / 1;
    font-family: 'Montserrat';
    font-size: var(--fs-3-2);
    font-weight: 700;

}
.evt-benefit .evt-mid .left {
    width: 47.2rem;
    order: 2;
    grid-row: 2 / 2;
   
}
.evt-benefit .evt-mid .left .evt-mid-top {
    font-size: 4rem;
    font-weight: 800;
    color: #000;
    line-height: 120%;
    text-transform: uppercase;
    text-align: center;
    letter-spacing: -0.04rem;
}

.evt-benefit .evt-mid .left .evt-mid-top strong {
    font-weight: 800;
    position: relative;
}
.evt-benefit .evt-mid .left .evt-mid-top strong i {
    color:#EE3897;
    font-style: normal;
    font-weight: 800;
}

.evt-benefit .evt-mid .left .evt-mid-top p {
    color: #333;
    font-size: var(--fs-1-8);
    margin: 1.6rem 0 0rem;
    line-height: var(--line-1-8);
    text-transform: none;
    font-weight: 400;
}


.evt-benefit.evt-02-benefit .evt-mid .left .evt-mid-top p {
    color: #666;
}
.evt-benefit.evt-02-benefit .evt-mid .left .evt-mid-top p + p {
    font-size: 1.4rem;
}
.evt-benefit .evt-mid .evt-mid-bottom {
    display: flex;
    justify-content: space-between;
    order: 3 ;
    grid-row: 3 / 3;
    margin-top: 4rem;
  
}
.evt-benefit .evt-mid .evt-mid-bottom .evt-left,
.evt-benefit .evt-mid .evt-mid-bottom .evt-right {
    text-align: center;
}

.evt-benefit .evt-mid .evt-mid-bottom .evt-left button {
    width:23rem;  height: 5.6rem;
    border-radius: 1.2rem;
    background: #0F1249;
    font-size: 2rem; 
    font-weight: 700;
    color:var(--white);
}
.evt-benefit .evt-mid .evt-mid-bottom .evt-right span,
.evt-benefit .evt-mid .evt-mid-bottom .evt-left span {
    color: #666;
    margin-top: .2rem;
    display: inline-block;
}
.evt-benefit .evt-mid .evt-mid-bottom .evt-right button {
    width:23rem; height: 5.6rem;
    border-radius: 1.2rem;
    background-color: #EF2E93;
    font-size: 2rem; 
    font-weight: 700;
    color:var(--white);
    text-align: center;
}

.evt-benefit .evt-mid .right {   
    width: 40rem; height: 21.2rem;
  
    position: relative;
    order: 2;
    grid-row:  1 /  span 3;
    /* grid-row: 1 / -1; */
}
.evt-benefit .evt-mid .right .evt-ticket-01 {
    width: 40rem; height: 21rem;
    /* filter:drop-shadow(0 1.2rem 1.25rem rgba(0, 0, 0, 0.2)); */
    background: url(../images/event/2024-01/evt_02.svg?202405021) no-repeat 0 0;
}

.evt-benefit .evt-mid .right h3.logo {
    width: 8rem; height: 2rem;
    position: absolute;
    left: 2.97rem; top: 3.5rem;
}

.evt-benefit .evt-mid .right .mid {
   
    display: flex;
    justify-content: start;
    position:absolute;
    left: 3rem; top:7.5rem;
    padding: 0;
 
}

.evt-benefit .evt-mid .right .mid span {
    display: inline-flex;
    align-self: flex-end;
    color: #222;
    font-size: 3rem;
    font-weight: 700;
    line-height: 2.3rem;
    font-family: 'Montserrat';
    text-transform: uppercase;
    margin-left: .5rem;
}
.evt-benefit .evt-mid .right  p.sub-txt {
    width: 40rem;
    display: flex;
    justify-content: start;
    position:absolute;
    left: 3rem; bottom: 4rem;
    color: #222;
    margin-top: 0;
    font-size: var(--fs-1-8);
}
.evt-02-benefit  .evt-mid .right  p.sub-txt{
    bottom: 2.8rem;
}

.evt-03-benefit  .evt-mid .right  p.sub-txt{
    bottom: 2.8rem;
    font-weight: 600;
}

.evt-02-benefit  .evt-mid .evt-mid-bottom .evt-left {
    width: 100%;
}
.evt-02-benefit  .evt-mid .evt-mid-bottom .evt-left button {
    width: 100% !important;
    background-color: #EF2E93 !important;
    color: var(--white);
}
.evt-benefit.evt-02-benefit .evt-mid  {
    grid-template-columns: 47rem 40rem;
}


.evt-benefit.evt-02-benefit .evt-top strong{
    width: 24.9rem;
    background-color: #CC23B9;
}
.evt-benefit.evt-02-benefit .evt-top em {
    background-color: #AB249E;
}
.evt-benefit.evt-02-benefit .evt-mid .left {
    grid-row: 2/3;
    align-items: center;
}
.evt-benefit.evt-01-benefit .evt-mid .right::after {
    width: 34.9rem; height: 25.2rem;
    content:'';
    display: block;
    position: absolute;
    right: .974rem; top: -13.7rem;
    background: url(../images/event/2024-01/evt_37.svg?20240502) no-repeat 0 0;

}

.evt-benefit.evt-02-benefit .evt-mid .right::after {
    width: 33.8rem; height: 29.5rem;
    content:'';
    display: block;
    position: absolute;
    right: 0rem; top: -16.3rem;
    background: url(../images/event/2024-01/evt_36.png?20240502) no-repeat 0 0;

}

.evt-benefit.evt-02-benefit .evt-mid .right .evt-ticket-01 {
    position: relative;
    background:url(../images/event/2024-01/evt_03.svg?202405021) no-repeat 0 0;
}
.evt-benefit.evt-02-benefit .evt-top::before {
    background: url(../images/event/2024-01/evt_08.svg?20240502) no-repeat 0 0;
    background-size: contain;
}

.evt-benefit.evt-02-benefit .evt-mid .evt-mid-bottom  {
    display: none;
}
.evt-benefit.evt-03-benefit .evt-mid {
    grid-template-columns: 47.2rem 40rem;
}

.evt-benefit.evt-03-benefit .right .evt-ticket-01 {
   position:relative;
    background: url(../images/event/2024-01/evt_04.svg?202405021) no-repeat 0 0;
}
.evt-benefit.evt-03-benefit .evt-top strong {
    width: 19.6rem;
    background: #8F28F5;
    text-transform: uppercase;
}
.evt-benefit.evt-03-benefit .evt-top::before {
    background: url(../images/event/2024-01/evt_11.svg?20240502) no-repeat 0 0;
    background-size: contain;
}
.evt-benefit.evt-03-benefit .evt-top em {
    background-color: #7A28CE;
}

.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom {
    flex-direction: row;
    flex-wrap: wrap;
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code-wrap {
    width: 100%;
    display: block;
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code-wrap.none {
    display: none;
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code {
    width:100%; height: 7.2rem;
    border-radius: .8rem;
   
    background: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code span {
    font-size: var(--fs-1-8);
    color: var(--black);
    margin-right: 1.6rem;
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code em {
    width: 11.2rem; height: 3.6rem;
    border-radius: .8rem;
    background-color: #f5f5f5;;
    font-weight: 800;
    align-items: center;
    justify-content: center;
    display: inline-flex;
    color: #0F1249;
    font-size: var(--fs-1-8);
    margin-right: .4rem;
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code button {
    width: 12.5rem; height: 3.6rem;
    border: 0;
    border-radius: .8rem;
    background: #0F1249;
    align-items: center;
    justify-content: center;
    display: inline-flex;
    color: var(--white);
    font-weight: 700;
    font-size: var(--fs-1-8);
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-left {
    margin-top: 1.6rem;
}
.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-left button {
    min-width: 47.2rem; height: 5.6rem;
    border-radius:1.2rem;
    background: #3AAA35;
    border:0;
    color: var(--white);
    display: flex;
    justify-content: center;
    align-items: center;
}

.evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-left button::after {
    content: '';
    width: 3.2rem; height: 3.2rem;
    display: block;
    background: url(../images/event/2024-01/evt_13.svg?20240502) no-repeat 0 0;
    margin-left: 1.2rem;
}

.evt-benefit.evt-03-benefit .evt-mid .right {
    width:40rem; height: 21.1rem;
    display: inline-flex;
 
}
.evt-benefit.evt-03-benefit .evt-sub-01 {
    width: 28.1rem; height: 24.4rem;
    background: url(../images/event/2024-01/evt_14.svg?20240502) no-repeat 0 0;
    position: absolute;
    right: -1rem; top: -11.3rem;
}
.evt-benefit.evt-03-benefit .evt-sub-01 span {
    color: var(--white);
    font-weight: 600;
    line-height: 130%;
    font-family: 'Montserrat';
    position: absolute;
    left: 6.2rem; top: 4rem;
    padding-left: 1rem;
}
.evt-benefit.evt-03-benefit .evt-sub-02 {
    width: 16rem; height: 7.2rem;
    background: url(../images/event/2024-01/evt_15.svg?20240502) no-repeat 0 0;
    position: absolute;
    right: -2.3rem; bottom: -4.6rem;
}
.evt-benefit.evt-03-benefit .evt-sub-02 span {
    color: var(--white);
    font-weight: 600;
    line-height: 130%;
    font-family: 'Montserrat';
    position: absolute;
    left: 1rem; top: 4rem;
    padding-left: 2rem;
}

.evt-01 .evt-btn-01{
    text-align: center;
    margin: 4rem 0;
 }
 .evt-01 .evt-btn-01 button {
     width: 38.7rem; height: 5.6rem;
     line-height: 5.6rem;
     border-radius: 1.2rem;
     background: #EF2E93;
     color: var(--white);
     font-size: var(--fs-2-4);
     font-weight: 700;
 }
 .evt-01-bottom .tit {
     font-size: 3.2rem;
     font-weight: 700;
     color: #000;
     text-align: center;
 }
 .evt-01-bottom .evt-table {
     width: 100rem;
     margin: 2.4rem auto 0;
 }
 .evt-01-bottom .evt-table thead th {
     height: 4.8rem;
     font-weight: 400;
     background: #282832;
     color: var(--white);
     line-height: 130%;  
 }
 .evt-01-bottom .evt-table thead tr th:nth-child(1) {
     border-radius: 8px 0px 0px 0px;
 } 
 .evt-01-bottom .evt-table thead tr th:nth-child(5) {
     border-radius: 0px 8px 0px 0px;
 }
 .evt-01-bottom .evt-table td {
     border-bottom: .1rem solid #EAEAEA;
     background: var(--white);
     padding: 1.2rem 0;
     line-height: 130%;
     text-align: center;
 
 }
 .evt-entry-tiket button {
     color:#EE3897;
     display: inline-flex;
     gap: 1rem;
     flex-direction: row;
     font-weight: 700;
 }
 .evt-entry-tiket button::after {
     content: ''; display: inline-block;
     width: 1.6rem; height: 1.6rem;
     background: url(../images/event/2024-01/evt_17.svg?20240502) no-repeat 0 0;
 }
.evt-01-pop {
    width: 99.6rem;
    border-radius: 1.6rem;
    overflow: hidden;
    background: linear-gradient(180deg, #DCEDFF 52.53%, #B5DAFF 100%);
    position: absolute;
    left: 0%; top: 20%;
    z-index: 30;
    display: none;
    box-shadow: 0 .2rem 1.5rem -.1rem rgba(0,0,0,0.35);
}
.evt-01-pop .evt-pop-top {
    width: 100%; height: 11.8rem;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: #313E75;
    text-align: center;
}
.evt-01-pop .evt-pop-top h2 {
    color: #FFF;
    font-size: 3.2rem;
    font-weight: 700;
    text-transform: uppercase;
}
.evt-01-pop .evt-pop-mid {
    min-height: 43.3rem;
    background: url(../images/event/2024-01/evt_51.png?20240502) no-repeat center top;
    background-size: 100%;
    padding-top: 4.8rem;
    
}
.evt-01-pop .evt-pop-mid .evt-resulte-01 {
    width: 40rem; height: 6rem;

    background-image:  url(../images/event/2024-01/evt_18.svg?20240502);
    background-position: center center;
    display: grid;
    grid-template-columns: 26.7rem 1fr;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}
.evt-01-pop .evt-pop-mid .evt-resulte-01 span {
    font-size: 2rem;
    color:#081D58;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.evt-01-pop .evt-pop-mid .evt-resulte-01 em {
    color: #FB2FFF;
    font-size: var(--fs-2-4);
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.evt-01-pop .evt-pop-mid .evt-resulte-01 em i {
    font-style: normal;
    margin-right: .5rem;
}

.evt-01-pop .evt-pop-mid .evt-resulte-02 {
    height: 42.8rem;
    position: relative;
} 
.evt-01-pop .evt-pop-mid .evt-resulte-02 .bg-0 { 
    text-align: center;
    position: absolute;
    left: 50%; top: 5.4rem;
    transform: translateX(-50%);
}

.evt-01-pop .evt-pop-mid .evt-resulte-02 .bg-1 {
    mix-blend-mode: screen;
    text-align: center;
    position: absolute;
    left: 50%; top: .2rem;
    transform: translateX(-50%);
  
}
.evt-01-pop .evt-pop-mid .evt-resulte-02 .card {
    text-align: center;
    position: absolute;
    left: 50%; top: .4rem;
    transform: translateX(-50%);
}
.evt-01-pop .evt-pop-mid .evt-resulte-02 .card img {
    width: 100%;
}

.evt-01-pop .evt-icon-mark {
    width: 15rem; height: 4.8rem;
    background: url(../images/event/2024-01/evt_31.svg?20240502) no-repeat 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-weight: 600;
    font-family: Montserrat;
    text-align: center;
    font-size: var(--fs-2-4);
    position: absolute;
    left: 50%; top: 33.4rem;
    transform: translateX(-50%);
}

.evt-01-pop .evt-icon-mark em {
    font-size: var(--fs-1-8);
    margin-left: .4rem;
}

.evt-01-pop  .evt-btn-wrap {
    display: flex; 
    padding: 0 0 4.1rem;
    gap: 1.6rem;
    justify-content: center;
    
}
.evt-01-pop  .evt-btn-wrap button {
   text-align: center;
   position: relative;
}
.evt-01-pop  .evt-btn-wrap button::after {
    content: '';
    width: 1.44rem; height: 1.44rem;
    background: url(../images/event/2024-01/evt_32.svg?20240502) no-repeat 0 0;
    display: block;
   position: absolute;
   right: 2.4rem; top: 50%;
   transform: translateY(-50%);
}

.evt-01-pop  .evt-btn-wrap button:nth-child(1) {
    width: 29.6rem; height: 4.8rem;
    border-radius:1.2rem;
    background: #313E75;
  
    color: var(--white);
    letter-spacing: normal;
}
.evt-01-pop  .evt-btn-wrap button:nth-child(2) {
    width: 29.6rem; height: 4.8rem;
    border-radius:1.2rem;
    background: #EF2E93;
    color: var(--white);
    letter-spacing: normal;
}

.evt-01-pop .evt-pop-top button {
    width: 2.5rem; height: 2.5rem;
    position: absolute;
    right: 1.25rem; top: 1.25rem;
    text-indent: -9999rem;
}
.evt-01-pop .evt-pop-top button::after {
    width: .1rem; height: 100%;
    background: var(--white);
    content: '';
    position: absolute;
    left: 1.25rem; top: 0;
    display: block;
    transform: rotate(45deg);
}
.evt-01-pop .evt-pop-top button::before {
    width: .1rem; height: 100%;
    background: var(--white);
    content: '';
    position: absolute;
    left: 1.25rem; top: 0;
    display: block;
    transform: rotate(320deg);
}




.notice-pop {
    display: none;
    border-radius: 1.6rem;
    overflow: hidden;
    background: var(--white);
    position: absolute;
    left: 50%; top: 20%;
}
.notice-pop .contents {
    width: auto;
    padding: 4rem 4rem 2.4rem;
    background: var(--white);
    text-align: center;
    font-size: var(--fs-2-4);
    line-height: var(--line-3-2);
    color: var(--gray-lev10);
}
.notice-pop .top {
    padding: 1.6rem 2.4rem;
    background: var(--gray-lev6);
    text-align: center;
    font-size: var(--fs-3-2);
    color: var(--blue);
    font-weight: 700;
}
.notice-pop .bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    gap:3rem;
    padding: 0 0 5.6rem;
}

.text-box-01 {
    width: 80rem;
    padding: 2.4rem 4rem;
    background: var(--pink-light);
    border-radius: 10rem;
    font-size: var(--fs-3-8);
    font-weight: 700;
    color: var(--white);
    position: absolute;
    left: 50%; top: 7.5rem;
    transform: translateX(-50%);
}
.text-box-01::after {
    content: '';
    width: 2rem; height: 2rem;
    background: var(--pink-light);
    position: absolute;
    left: 50%; bottom: -1rem;
    margin-left: -1rem;
    transform: rotate(45deg);
}
.text-box-02 {
    color: var(--blue);
    position: absolute;
    left: 50%; bottom: 7rem;
    transform: translateX(-50%);
}
@keyframes rotate_image{
	100% {
    	transform: rotate(360deg);
    }
}

/* MOBILE WIDTH 720PX FOR 360px */
@media screen and  (max-width:1000px) {
    .pc-object {
        display: none;
    }
    .mo-object {
        display: block;
    }
    .evt-detail {
        max-width:100%;
        
    }
    .evt-01-top {
        height: auto;
        margin-bottom: 0;
        background-color: transparent;
    }
    .evt-01-top .evt-top{
        padding: 3.6rem 0 0;
    }
    .evt-01-top .evt-top h1 {
        width: 100%;
        font-size: 4.4rem;
        text-align: center;
        line-height: 110%;
        margin-top: 1.6rem;
    }
    .evt-01-top .evt-top p.point-01 {
        font-size: var(--fs-1-6);  
        margin-top: .4rem;
    }
    .evt-01-top .evt-top p.point-02 {
        font-size: var(--fs-1-6);  
        margin-top: .4rem;
    }
    .evt-01-top .evt-top p.point-03 {
        font-size: var(--fs-1-6);  
        margin-top: 2.4rem;
    }
    .evt-01-top .evt-top .evt-symbol {
        max-width: 68%;
    }
    .evt-01-top .evt-top .evt-symbol img {
        width: 100%;
    } 
    evt-interation-wrap {
        margin: 2rem 0 2rem;
    }
    .evt-01-top .evt-detail {
        width: 100%;
        background:url(../images/event/2024-01/evt_34.svg?20240502) no-repeat center center;
        background-size:  cover;
    }
    .evt-count-ticket {
        background-size:  contain;
    }
    .evt-01-top .evt-detail::after {
        content: '';
        width: 100%; height: 100%;
        background: url(../images/event/2024-01/evt_35.svg?20240502) no-repeat right top;
        background-size: 100%;
        position: absolute;
        left: 0; top: 0;
        z-index: -1;
    }

    .evt-con-detail, .evt-benefit {
        width: 100%;
        padding: 4.5rem 0;
    }

    .evt-benefit .evt-top span {
        width: 15.2rem; height: 3.2rem;
        font-size: 1.3rem;
        padding-left: 1.6rem;
    }
    .evt-benefit .evt-top em {
        width: 3.2rem; height: 3.2rem;
        margin-left: -3.2rem;
        font-size: 1.3rem;
    }
    .evt-benefit .evt-top strong {
        width: 18.7rem; height: 3.2rem;
        font-size: 1.3rem;
        margin-left: -3.2rem;
        padding-right: 1.6rem;
    }
    .evt-benefit.evt-02-benefit .evt-top strong {
        width: 13rem; height: 3.2rem;
        font-size: 1.3rem;
        margin-left: -3.2rem;
        padding-right: 1.6rem;
    }
    .evt-benefit.evt-03-benefit .evt-top strong { 
        width: 11.7rem; height: 3.2rem;
        font-size: 1.3rem;
        margin-left: -3.2rem;
        padding-right: 1.6rem;
    }
    .evt-benefit .evt-top::before {
        width: 1.4rem; height: 1.1rem;
        right: 1.5rem; top: 2.9rem;
        background-size: contain;
    }
    .evt-benefit .evt-mid {
        margin-top: 2.4rem;
        grid-template-columns: 1fr;
    }
    .evt-benefit .evt-mid .left {
        width: 100%;
        order: 1;
    }
    .evt-benefit .evt-mid .left .evt-mid-top {
        width: 74%;
        margin: 0 auto;
        font-size: 2.6rem;
        text-align: center;   
    }
    .evt-benefit .evt-mid .left .evt-mid-top p {
        max-width:80%;
        margin: 1.2rem auto 0;
        font-size: 1.3rem;
        
    }
    .evt-benefit .evt-mid .right {
        width: 26.4rem;  height: 14rem;
        margin: 3.3rem auto 2.4rem;
        background-size: 100% 185px;
        order: 2;
        grid-row: inherit;
    }
    .evt-benefit.evt-02-benefit .evt-mid .right {
        margin: 14rem auto 2.4rem;
    }
    .evt-benefit.evt-03-benefit .evt-mid .right {
        width: 26.4rem;  height: 14rem;
        margin: 8rem auto 5.5rem;
        background-size: 100% 185px;
        order: 2;
        grid-row: inherit;
    }

    .evt-benefit.evt-01-benefit .evt-mid .right {
        width: 26.4rem;height: 14rem;
        margin: 8.3rem auto 2.4rem; 
        
    }
    .evt-benefit .evt-mid .right .evt-ticket-01 {
        max-width: 26.4rem;
        width: 100%; height: 14rem;
        background-size: 100% 100%;
        margin: 0 auto;
        position: relative;
    }
    .evt-benefit.evt-01-benefit .evt-mid .right::after {
        width: 17rem; height: 14rem;
        background-size: contain;
        right: -1rem; top: -6.3rem;
        transform: rotate(350deg);
    }
    .evt-benefit.evt-03-benefit .evt-mid .left .evt-mid-top strong::after {
        width: 4.8rem; height: 4.8rem;
        background-size: contain;
        right: -.5rem; top: -6rem;

    }
    .evt-benefit.evt-02-benefit .evt-mid .right .evt-ticket-01{
        background-size: 100% 100%;
    }
    .evt-benefit.evt-03-benefit .evt-mid .left .evt-mid-top p {
        max-width: 80%;
    }
    .evt-benefit.evt-02-benefit .evt-mid .right::after {
        height: 21.4rem;
        max-width: 100vw;
        background-size: contain;
        top: -13.5rem; right: -11rem;
    }
     
    .evt-benefit .evt-mid .evt-mid-bottom {
        width: 100%;
        max-width: 36rem; 
        margin: 0 auto;
        order: 3;
        gap: .5rem;
        grid-row: auto;
        padding: 0 2.4rem;
    }
    .evt-benefit.evt-01-benefit .evt-mid .evt-mid-bottom .evt-right,
    .evt-benefit.evt-01-benefit .evt-mid .evt-mid-bottom .evt-left {
        width: 50%;
    }
    .evt-benefit.evt-02-benefit .evt-mid .evt-mid-bottom {
        display: block;
    }
    .evt-benefit.evt-02-benefit .evt-mid .evt-mid-bottom .evt-left {
        width:100%;
    }
    .evt-benefit.evt-02-benefit .evt-mid .evt-mid-bottom .evt-left button {
        width:100%; height: 4rem; 
        border: 0;
        background: #0F1249; color: var(--white);
        font-size: 1.4rem;
        
    }
    .evt-benefit.evt-01-benefit .evt-mid .evt-mid-bottom .evt-right button,
    .evt-benefit.evt-01-benefit .evt-mid .evt-mid-bottom .evt-left button {
        width: 100%; height: 4rem;
        font-size: var(--fs-1-6);
        font-weight: 700;
    }
    .evt-benefit.evt-01-benefit .evt-mid .evt-mid-bottom .evt-right span, 
    .evt-benefit.evt-01-benefit .evt-mid .evt-mid-bottom .evt-left span {
        font-size: 1.2rem;
    }
    .evt-benefit .evt-mid .right h3.logo {
        width: 6.4rem; height: 1.4rem;
        top: 2.2rem; left: 2rem;
      
    }
    .evt-benefit.evt-01-benefit .evt-mid .right .mid {
        left: 2rem; top: 5.5rem;
        padding: 0 0 0 0rem;
    }
    .evt-benefit .evt-mid .right .mid img {
        width: 10.5rem; height: 3.6rem;
      
    }

    .evt-benefit.evt-02-benefit .evt-mid .left {
        min-height: auto;
    }

    .evt-benefit.evt-02-benefit .evt-mid .right .mid img {
        width: 10.3rem; height: 3.8rem;
    }
    .evt-benefit .evt-mid .right .mid span {
        margin-left: 0;
    }
    .evt-benefit .evt-mid .right p.sub-txt {
        max-width: 26.4rem;
        left: 2rem; bottom: 1.6rem;
        font-size: 1.2rem;
        line-height: var(--line-1-4);
    }
    .evt-benefit.evt-02-benefit .evt-mid {
        grid-template-columns: 1fr;
    }
    .evt-benefit.evt-02-benefit .evt-mid .right .mid {
        left: 2rem; top: 5rem;
        padding: 0 0 0 0rem;
    }
    .evt-benefit.evt-03-benefit .evt-mid .right .mid {
        left: 2rem; top: 5rem;
        padding: 0 0 0 0rem;
    }
    .evt-benefit.evt-02-benefit .evt-mid .left .evt-mid-top p {
        max-width: 100%;
    }
    .evt-benefit.evt-02-benefit .evt-mid .left .evt-mid-top p + p{
        max-width: 76%;
    }
    .evt-benefit.evt-02-benefit .evt-mid .left .evt-mid-top strong::after {
        width: 4.8rem; height: 4.8rem;
        background-size: contain;
        top: -2rem; right: -1rem;
    }
    .evt-benefit.evt-03-benefit .evt-sub-01 {
        width: 16.8rem; height: 13.4rem;
     
        background-size: contain;
        top: -5.9rem;
    }
    .evt-benefit.evt-03-benefit .evt-sub-01 span {
        font-size: 1.2rem;
        left: 4.8rem; top: 2.5rem;
        padding-left: .4rem;
        font-weight: 600;   
    }
    .evt-benefit.evt-03-benefit .evt-sub-02 {
        width: 12rem; height: 5.2rem;
        background: url(../images/event/2024-01/evt_40.svg?20240502) no-repeat 0 0;
        background-size: contain;
        bottom: -2.5rem;
    }
    .evt-benefit.evt-03-benefit .evt-sub-02 span {
        font-size: 1.2rem;
        left: 1.5rem; top: 2.6rem;
        padding-left: 0rem;
        font-weight: 400;   
    }
    .evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom {
        max-width: 31.2rem;
        padding: 0;
      
    }
    .evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code span {
        font-size: 1.2rem;
        margin-right: .8rem;
    }
    .evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code em {
        width: 8rem; height: 2.4rem; 
        font-size: 1.2rem;
    }
    .evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code button {
        width: 8rem; height: 2.4rem;
        font-size: 1.2rem;
    }
    .evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-copy-code {
        height: 5.6rem;
    }
    .evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-left button {
        min-width: 31.2rem;
        width: 100%; height: 4rem;
        font-size: var(--fs-1-4);
    }
    .evt-benefit.evt-03-benefit .evt-mid .evt-mid-bottom .evt-left button::after {
        width: 2.4rem; height: 2.4rem; 
        margin-left: .6rem;
        background-size: contain;
    }
    .evt-benefit.evt-03-benefit .evt-mid {
        grid-template-columns: 1fr;
    }

    .evt-01-bottom .tit {
        font-size: 2.7rem;
        line-height: 120%;
        max-width: 63%;
        margin: 0 auto;
        font-weight: 700;
        
    }

    .evt-01-bottom .evt-table {
        width: 90%;
        overflow-x: auto;
        border-radius: 8px 8px 0px 0px;
        
    }
    .evt-01-bottom .evt-table.null table {
        width: 100%;
    }
    .evt-01-bottom .evt-table table {
        width: 50rem;
    }
    .evt-01-bottom .evt-table thead tr th:nth-child(1) {
        border-radius: 0;
    }
    .evt-01-bottom .evt-table thead tr th:nth-child(5) {
        border-radius: 0;
    }
    .evt-01-bottom .evt-table thead tr th,
    .evt-01-bottom .evt-table thead tr td {
        font-size: 1.2rem;
    }

    .evt-01 .evt-btn-01 button {
        width: 31.2rem;
        font-size: 2rem ;
    }
    .evt-01-pop .evt-pop-mid {
        padding-top: 3.4rem;
    }

    .evt-01-pop .evt-pop-mid .evt-resulte-01 {
        width: 29.6rem; height: 4.2rem;
        grid-template-columns: 18rem 1fr;
        background-size: 100%;
    }
    .evt-01-pop .evt-pop-mid .evt-resulte-01 span {
        font-size: var(--fs-1-4);
        font-weight: 500;
    }
    .evt-01-pop .evt-pop-mid .evt-resulte-01 em {
        font-size: var(--fs-1-8);
    }
    
    .evt-01-pop .evt-pop-mid .evt-resulte-02 .bg-0 {
        height: 30rem;
        overflow: hidden;
        position: relative;
    }
    .evt-01-pop .evt-pop-mid .evt-resulte-02 .bg-0 img {
        width: 100%; height: 100%;   
        transform: translateX(0%) scale(1.1);
    }
    .evt-01-pop .evt-pop-mid .evt-resulte-02 .bg-1 {
        width: 20rem;
        top: 9rem;                   
       
    }
    .evt-01-pop .evt-pop-mid .evt-resulte-02 .bg-1 img {
        width: 100%;
    }
    .evt-01-pop .evt-pop-mid .evt-resulte-02 .card {
        width: 20rem;
        top: 9rem;
    }

    .evt-01-002 .evt-tab-triger-list li a {
        flex-direction: column;
    }
    .evt-01-002 .evt-tab-triger-list {
        width: 95%;
        margin: 0 auto;
    }
    .evt-01-002 .tabTarget {
        border-radius: 0;
    }
    .evt-01-002 .evt-group-box-01{
        width:90% !important;
        margin: 4rem auto 0;
    }
    .evt-group-box-01 .top {
        width: 9.5rem; height: 4.7rem;
        line-height:5.5rem;
        top: -4.7rem;
        margin: 0 0 0 -4.75rem;
    }
    .evt-01-pop .evt-icon-mark {
        width: 10rem; height: 3.2rem;
        top: 63%;
        background-size: cover;
        font-size: var(--fs-1-6);
        line-height: 3.2rem;
        
    }
    .evt-01-pop .evt-icon-mark em{
        font-size: 1.2rem;
    }
    .evt-01-pop .evt-btn-wrap {
        flex-direction: column;
        align-items: center;
        margin-top: -15rem;
    }

    
    .evt-01-pop .evt-pop-mid .evt-resulte-02 {
       top: -6rem;
    } 



    .evt-01-002 .evt-group-box-01 .detail {
        width: 100%;
    }
    .evt-01-001::after,
    .evt-01-001::before {
        display: none;
    }
    .evt-01-002 .evt-tab-triger-list li {
        width: 50%;
        padding: .8rem 0;
    }
    .evt-01-002 .evt-tab-triger-list li a {
        text-align: center;
        font-size: var(--fs-1-6);
        gap: 0.25rem;
    }
    .evt-01-002 .evt-tab-triger-list li a::before {
        width: 2rem !important; height: 2rem !important;
        background-size: 2rem 2rem !important;
    }
    .evt-01-002 .evt-group-box-01 .detail {
        margin: 1.2rem 0 0;
    }
    .evt-01-002 .evt-group-box-01 .top-tit {
        height: auto;
        padding: 0.8rem 1.2rem;
    }
    .evt-01-002 .evt-group-box-01 .top-cont {
        width: 100%;
        flex-direction: column;
        gap: 2.4rem;
       
    }
    .evt-01-002 .evt-group-box-01 .top-cont.top-type-01 {
        margin-bottom: 21rem;
    }
    .evt-01-002 .evt-group-box-01 .top-cont.top-type-02 {
        margin-bottom: 15rem;
    }
    .evt-01-002 .evt-group-box-01 .top-cont .right {
        max-inline-size: 100%;
        text-align: center;
    }
    .evt-01-002 .evt-group-box-01 .top-cont .right::before {
        display: none;
    }
    .evt-01-002 .evt-group-box-01 {
        background-image: none;
        margin-bottom: 7rem;
    }
    .evt-01-002 .evt-group-box-01 .bottom-cont {
        flex-direction: column;
    }
    .evt-roulette {
        width: 100%; height: 100%;
        min-height: 54rem;
    }
    .evt-roulette img {
        width: 100%;
        
    }
    .btn.btn-green {
        width: 90%;
    }
    .notice-pop {
        width: 90%;
        left: 5% !important;
        margin-left: 0 !important;
    }
    .notice-pop .top {
        padding: .8rem 1.2rem;
        font-size: var(--fs-2-2);
    }
    .notice-pop .contents {
        padding: 2rem 2rem 1.2rem;
        font-size:var(--fs-1-6);
    }
    .notice-pop .bottom {
        padding: 0 0 2.8rem;
    }
    .evt-01-pop {
        width: 100%; max-width: 90%;
        left: 5%;
        transform:translateX(-50%);
        /* transform:translate3d(-50%, -50%, 0); */
    }
    .evt-01-pop .evt-pop-top {
        height: 4.8rem;
    }
    .evt-01-pop .evt-pop-top h2 {
        font-size: var(--fs-1-8);
    }
    .evt-01-pop .content-area .roulette .circle img {
        width: 100%;
    }
    .evt-01-pop .content-area .roulette {
        width: 90%; height: auto;
    }
    .evt-01-pop .content-area .roulette button {
        width: 5rem; height: 5rem;
        background-size: 5rem 5rem;
        margin: -3.5rem 0 0 -2.5rem;
        position: absolute;
        left: 50%; top: 50%;
    }
    .evt-01-001 .ticket-box.blue,
    .evt-01-pop .ticket-box, .evt-01-001 .ticket-box {
        width: 28rem; height: 4rem;
        background-size:28rem 4rem;
        grid-template-columns: 18rem 1fr;
    }
    .evt-01-pop .ticket-box, .evt-01-001 .ticket-box span {
        font-size: var(--fs-1-4);
        line-height: var(--line-1-4);
    }
    .evt-01-pop .ticket-area {
        width: 28rem;
    }
    .evt-01-pop .ticket-box, .evt-01-001 .ticket-box strong {
        font-size: var(--fs-1-8);
        line-height: var(--line-1-8);
    }
    .evt-01-pop .top-area h3 {
        font-size: var(--fs-1-8);
    }
    .evt-01-pop .content-area .roulette::after {
        width: 3.55rem; height: 3.55rem;
        background-size: 3.55rem 3.55rem;
        margin-left: -1.775rem; top: 1.5rem;
    }
    .evt-01-pop .resulte-box .btn-area {
        flex-direction: column;
        gap: 1rem;
    }
    .evt-01-pop .resulte-box .won-image {
        display: none;
    }
    .evt-01-pop .resulte-box .btn-area button {
        font-size: var(--fs-1-6);
        line-height: var(--line-1-8);
    }
    .evt-01-002 .tabTarget.tab-detail-view{ 
        border-radius: 0;
    }
    .detail-tab {
        max-width: 90%;
        padding: 4rem 0;
    }
    .detail-tab .top {
        gap:1rem;
        flex-direction: column;

    }
    .detail-tab h3 {
        font-size: var(--fs-2-4);
        margin: 2.4rem 0 1.2rem;
    }
    .evt-01-002 .tabTarget.tab-detail-view {
        margin-bottom: 0;
    }
    .text-box-01  {
        min-width: auto;
        max-width: 90%;
        left: 5%; top: 5rem;
        transform:translateX(0%);
        font-size: var(--fs-2-4);
        padding: .4rem 2rem;
        line-height: var(--line-3-2);
        text-align: center;
    }
    .text-box-02 {
        width: 100%;
        left:0;
        transform: translateX(0%);
        text-align: center;
    }
    .evt-01-top .evt-mid {
        height: 24rem;
        margin: 11rem 0 7rem; 
        position: relative;
        flex-wrap: nowrap;
       
        justify-content: space-between;
    }
    .evt-01-top .evt-interation-wrap {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-start;
    }
    .evt-01-top .evt-interation-wrap.item2 {
        display: none;
        /* transform: translateX(0px); */
    }

    .evt-benefit .evt-mid .right .mid span {
        font-size: var(--fs-1-8);
    }
 
    /* .evt-01-top .evt-interation-wrap ul li:nth-child(1) {
        scale: 70%;
        left: 0; top: 0;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(2) {
        left: 10rem; top: .5rem;
        scale: 80%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(3) {
        left: 20rem; top: 1.5rem;
        scale: 70%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(4) {
        left: 30rem; top: 1rem;
        scale: 80%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(5) {
        left: 40rem; top: 0rem;
        scale: 70%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(6) {
        left: 50rem; top: 0;
        scale: 80%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(7) {
        left: 60rem; top: 1rem;
        scale: 70%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(8) {
        left: 70rem; top: -1rem;
        scale: 80%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(9) {
        left: 80rem; top: -2rem;
        scale: 70%;
    }
    .evt-01-top .evt-interation-wrap ul li:nth-child(10) {
        left: 90rem; top: -1rem;
        scale: 80%;
    } */
}    
/* E:EVENT 01 */




/*  */
@keyframes rotation1{
    0%{
        
        transform: rotate(-4deg);
    }
    50%{
        transform: rotate(12deg);
    }

    100%{
        transform: rotate(-4deg);
    }
}
@keyframes rotation2{
    0%{
        
        transform: rotate(4deg);
    }
    50%{
        transform: rotate(-2deg);
    }
    100%{
        transform: rotate(4deg);
    }
}
@keyframes rotation3{
    0%{
        transform: rotate(-7deg);
    }
    50%{
        transform: rotate(14deg);
    }
    100%{
        transform: rotate(-7deg);
    }
}
@keyframes rotation4{
    0%{
        transform: rotate(15deg);
    }
    50%{
        transform: rotate(-15deg);
    }
    100%{
        transform: rotate(15deg);
    }
}
@keyframes rotation5{
    0%{
        
        transform: rotate(-3deg);
    }
    50%{
        transform: rotate(12deg);
    }
    100%{
        transform: rotate(-3deg);
    }
}
@keyframes rotation6{
    0%{
        transform: rotate(4deg);
    }
    50%{
        transform: rotate(-4deg);
    }
    100%{
        transform: rotate(4deg);
    }
}
@keyframes rotation7{
    0%{
        transform: rotate(-10deg);
    }
    50%{
        transform: rotate(12deg);
    }

    100%{
        transform: rotate(-10deg);
    }
}
@keyframes rotation8{
    0%{
        transform: rotate(12deg);
    }
    50%{
        transform: rotate(-12deg);
    }
    100%{
        transform: rotate(12deg);
    }
}
@keyframes rotation9{
    0%{
        transform: rotate(-14deg);
    }
    50%{
        transform: rotate(14deg);
    }
    100%{
        transform: rotate(-14deg);
    }
}
@keyframes rotation10{
    0%{
        transform: rotate(2deg);
    }
    50%{
        transform: rotate(-12deg);
    }
    100%{
        transform: rotate(2deg);
    }
}