<svg width="600" height="302" viewBox="0 0 600 302" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M449.998 67.0253C449.998 51.8802 463.854 40.5212 478.705 43.4914L580.705 63.8914C591.923 66.135 599.998 75.985 599.998 87.4253V213.075C599.998 224.515 591.923 234.365 580.705 236.609L478.705 257.009C463.854 259.979 449.998 248.62 449.998 233.475V67.0253Z" fill="url(#paint0_linear_57_933)"/>
<path d="M150 67.0253C150 51.8802 136.144 40.5212 121.293 43.4914L19.2932 63.8914C8.07503 66.135 0 75.985 0 87.4253V213.075C0 224.515 8.07503 234.365 19.2932 236.609L121.293 257.009C136.144 259.979 150 248.62 150 233.475V67.0253Z" fill="url(#paint1_linear_57_933)"/>
<g style="mix-blend-mode:screen" opacity="0.5" filter="url(#filter0_f_57_933)">
<rect x="181" y="2" width="238" height="298" rx="39" stroke="url(#paint2_linear_57_933)" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_f_57_933" x="179" y="0" width="242" height="302" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_57_933"/>
</filter>
<linearGradient id="paint0_linear_57_933" x1="461.248" y1="149.5" x2="574.498" y2="149.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#ACD6FF"/>
<stop offset="1" stop-color="#75BBFF"/>
</linearGradient>
<linearGradient id="paint1_linear_57_933" x1="138.75" y1="149.5" x2="25.5" y2="149.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#ACD6FF"/>
<stop offset="1" stop-color="#75BBFF"/>
</linearGradient>
<linearGradient id="paint2_linear_57_933" x1="381" y1="14.5" x2="208.5" y2="284.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFF8DE"/>
<stop offset="0.234375" stop-color="#F7FFA0"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
</defs>
</svg>
