<svg width="368" height="428" viewBox="0 0 368 428" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g filter="url(#filter0_di_1_94)">
<rect x="64" y="64" width="240" height="300" rx="40" fill="#275EEE"/>
</g>
<path d="M154.048 291.968H162.136V294.992H154.048V291.968ZM154.336 298.88H163.48V302H150.472V285.2H163.168V288.32H154.336V298.88ZM166.574 302V285.2H173.846C175.35 285.2 176.646 285.448 177.734 285.944C178.822 286.424 179.662 287.12 180.254 288.032C180.846 288.944 181.142 290.032 181.142 291.296C181.142 292.544 180.846 293.624 180.254 294.536C179.662 295.432 178.822 296.12 177.734 296.6C176.646 297.08 175.35 297.32 173.846 297.32H168.734L170.462 295.616V302H166.574ZM177.254 302L173.054 295.904H177.206L181.454 302H177.254ZM170.462 296.048L168.734 294.224H173.63C174.83 294.224 175.726 293.968 176.318 293.456C176.91 292.928 177.206 292.208 177.206 291.296C177.206 290.368 176.91 289.648 176.318 289.136C175.726 288.624 174.83 288.368 173.63 288.368H168.734L170.462 286.52V296.048ZM182.014 302L189.502 285.2H193.342L200.854 302H196.774L190.63 287.168H192.166L185.998 302H182.014ZM185.758 298.4L186.79 295.448H195.43L196.486 298.4H185.758ZM200.929 302L208.417 285.2H212.257L219.769 302H215.689L209.545 287.168H211.081L204.913 302H200.929ZM204.673 298.4L205.705 295.448H214.345L215.401 298.4H204.673Z" fill="white"/>
<circle cx="184" cy="184" r="71.5" fill="url(#paint0_linear_1_94)" stroke="url(#paint1_linear_1_94)"/>
<rect x="139" y="136" width="90" height="90" fill="url(#pattern0_1_94)"/>
<defs>
<filter id="filter0_di_1_94" x="60" y="64" width="248" height="308" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_94"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_94" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1_94"/>
</filter>
<pattern id="pattern0_1_94" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_1_94" transform="scale(0.0021978)"/>
</pattern>
<linearGradient id="paint0_linear_1_94" x1="178.534" y1="184.012" x2="287.173" y2="266.582" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.35"/>
<stop offset="0.21" stop-color="white" stop-opacity="0.34"/>
<stop offset="0.4" stop-color="white" stop-opacity="0.29"/>
<stop offset="0.59" stop-color="white" stop-opacity="0.22"/>
<stop offset="0.77" stop-color="white" stop-opacity="0.12"/>
<stop offset="0.93" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_1_94" x1="150.424" y1="121.752" x2="234.73" y2="239.818" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.45"/>
<stop offset="0.02" stop-color="white" stop-opacity="0.42"/>
<stop offset="0.09" stop-color="white" stop-opacity="0.31"/>
<stop offset="0.17" stop-color="white" stop-opacity="0.23"/>
<stop offset="0.26" stop-color="white" stop-opacity="0.16"/>
<stop offset="0.38" stop-color="white" stop-opacity="0.11"/>
<stop offset="0.54" stop-color="white" stop-opacity="0.09"/>
<stop offset="1" stop-color="white" stop-opacity="0.08"/>
</linearGradient>
<image id="image0_1_94" width="455" height="455" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
