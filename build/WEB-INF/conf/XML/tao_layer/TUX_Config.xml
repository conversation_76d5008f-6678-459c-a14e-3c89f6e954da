<?xml version="1.0" encoding="UTF-8"?>
<xml>

<!-- 
Global Unique ID 정의 : 각 필드는 채우지 못한 여백은 모두 언더바(‘_’)를 채운다
시스템 구분
매체구분
사용자 ID
화면구분
Object 구분
일시분초
밀리초
공백

채널 사용 필드 정의
매체 구분값
 -->

    <tran-header target="tuxedo">		
        <field name="svc_id" 		len="11" 	type="L" 	id="" 	en="svc_id" 		/>
        <field name="gvl_uniq_id" 	len="40" 	type="L" 	id="" 	en="gvl_uniq_id" 	/>
        <field name="user_id" 		len="8" 	type="L" 	id="" 	en="user_id" 		/>
        <field name="dept_code" 	len="3" 	type="L" 	id="" 	en="dept_code" 		/>
        <field name="user_gubn" 	len="1" 	type="L" 	id=""	en="user_gubn" 		/>
        <field name="comm_gubn" 	len="2" 	type="L" 	id="" 	en="comm_gubn" 		/>
        <field name="term_id" 		len="8" 	type="L" 	id="" 	en="term_id" 		/>
        <field name="map_id" 		len="10" 	type="L" 	id="" 	en="map_id" 		/>
        <field name="local_ip" 		len="15" 	type="L" 	id="" 	en="local_ip" 		/>
        <field name="public_ip" 	len="15" 	type="L" 	id="" 	en="public_ip" 		/>
		<field name="umgb" 			len="1" 	type="L" 	id=""	en="umgb"			/>
		<field name="mmdd" 			len="4" 	type="L" 	id=""	en="mmdd"			/>
		<field name="itrm" 			len="8" 	type="L" 	id=""	en="itrm"			/>
		<field name="apf1" 			len="3" 	type="L" 	id=""	en="apf1"			/>
		<field name="apf2" 			len="3" 	type="L" 	id=""	en="apf2"			/>
		<field name="jidx" 			len="3" 	type="L" 	id=""	en="jidx"			/>
		<field name="mdgb" 			len="2" 	type="L" 	id=""	en="mdgb"			/>
		<field name="fld"  			len="1" 	type="L" 	id=""	en="fld"			/>
        <field name="rms_gubn" 		len="1" 	type="L" 	id="" 	en="rms_gubn" 		/>
        <field name="cont_gubn" 	len="1" 	type="L" 	id="" 	en="cont_gubn" 		/>
        <field name="next_data" 	len="60" 	type="L" 	id="" 	en="next_data" 		/>
        <field name="macaddr" 		len="12" 	type="L" 	id=""	en="macaddr"		/>
        <field name="lang_type" 	len="1" 	type="L" 	id="" 	en="lang_type" 		/>
        <field name="filler" 		len="5" 	type="L" 	id="" 	en="filler" 		/>
        <field name="svr_rvc_time" 	len="9" 	type="L" 	id="" 	en="svr_rvc_time" 	/> 
        <field name="retr_cnt" 		len="3" 	type="RO" 	id="" 	en="retr_cnt" 		/>
    </tran-header>
    
    <tran-header-out target="tuxedo">
        <field name="svc_id" 		len="11" 	type="L" 	id="" 	en="svc_id" 		/>
        <field name="gvl_uniq_id" 	len="40" 	type="L" 	id="" 	en="gvl_uniq_id"	/>
        <field name="user_id" 		len="8" 	type="L" 	id="" 	en="user_id" 		/>
        <field name="dept_code" 	len="3" 	type="L" 	id="" 	en="dept_code" 		/>
        <field name="user_gubn" 	len="1" 	type="L" 	id="" 	en="user_gubn" 		/>
        <field name="comm_gubn" 	len="2" 	type="L" 	id="" 	en="comm_gubn" 		/>
        <field name="term_id" 		len="8" 	type="L" 	id="" 	en="term_id" 		/>
        <field name="map_id" 		len="10" 	type="L" 	id="" 	en="map_id" 		/>
        <field name="local_ip" 		len="15" 	type="L" 	id="" 	en="local_ip" 		/>
        <field name="public_ip" 	len="15" 	type="L" 	id="" 	en="public_ip" 		/>
        <field name="ch_fld" 		len="25" 	type="L" 	id="" 	en="ch_fld" 		/>
        <field name="rms_gubn" 		len="1" 	type="L" 	id="" 	en="rms_gubn" 		/>
        <field name="cont_gubn" 	len="1" 	type="L" 	id="" 	en="cont_gubn" 		/>
        <field name="next_data" 	len="60" 	type="L" 	id="" 	en="next_data" 		/>
        <field name="macaddr" 		len="12" 	type="L" 	id=""	en="macaddr"		/>
        <field name="filler" 		len="11" 	type="L" 	id="" 	en="filler" 		/>
        <field name="focus_fld" 	len="20" 	type="L" 	id="" 	en="focus_fld" 		/>
        <field name="resp_gubn" 	len="1" 	type="RO" 	id="" 	en="resp_gubn" 		/>
        <field name="resp_code" 	len="6" 	type="RO" 	id="" 	en="resp_code" 		/>
        <field name="resp_mesg" 	len="100" 	type="L" 	id="" 	en="resp_mesg" 		/>
    </tran-header-out>
	
    <!-- Register Trial ID -->
	<transaction target="tuxedo" tr="TLO1050_U01" key="en" timeout="60">
		<request>		
	 		<field name="이름" len="50" type="L" id="" en="name"/>
	    	<field name="E-mail" len="60" type="L" id="" en="email"/>
	    	<field name="전화번호" len="20" type="L" id="" en="hp_no"/>
	    	<field name="SMS수신여부" len="1" type="L" id="" en="sms_agree_yn"/>
	  	</request>
	  	<response>		
	    	<field name="생성ID" len="8" type="L" id="" en="login_id"/>
	    	<field name="비밀번호" len="10" type="L" id="" en="dcrp_scrt"/>
	  	</response>
	</transaction>
	
	<!-- Inquiry Trial ID -->
	<transaction target="tuxedo" tr="TLO1050_Q02" key="en" timeout="60">
		<request>		
	    	<field name="생성ID" len="8" type="L" id="" en="login_id"/>   
	  	</request>
	  	<response>	  
	    	<field name="이름" len="50" type="L" id="" en="name"/>
	    	<field name="E-mail" len="60" type="L" id="" en="email"/>
	    	<field name="전화번호" len="20" type="L" id="" en="hp_no"/>
	    	<field name="SMS수신여부" len="1" type="L" id="" en="sms_agree_yn"/>
	  	</response>
	</transaction>
    
    <!-- THis is a TR that looks up information about events. -->
	<transaction target="tuxedo" tr="TCF9002_Q02" key="en" timeout="60">
	  <request>
	    <field name="EVENTID" len="10" type="L" id="" en="event_id" />
	    <field name="EVENTNAME" len="40" type="L" id="" en="event_nm" />
	  </request>
	  <response>
	    <field name="OUTCOUNT" len="4" type="RO" id="" en="out_count" />
	    <repeat count="out_count">
	      <field name="EVENTID" len="10" type="L" id="" en="event_id" />
	      <field name="EVENTNAME" len="40" type="L" id="" en="event_nm" />
	      <field name="QUALIFYVALUE" len="15" type="RO" id="" en="qualify_value" />
	      <field name="STARTDATE" len="8" type="L" id="" en="s_dt" />
	      <field name="ENDDATE" len="8" type="L" id="" en="e_dt" />
	      <field name="EVENTDAYS" len="10" type="RO" id="" en="event_days" />
	      <field name="TARGET" len="20" type="L" id="" en="target" /><!-- 0:ALL, 1:NEW -->
	      <field name="STATUS" len="20" type="L" id="" en="status" /><!-- Y:OPEN, N:CLOSE -->
	      <field name="DESCRIPTION" len="2000" type="L" id="" en="memo" />
	    </repeat>
	  </response>
	</transaction>
	
	<!-- This is a TR that allows you to check whether you can participate in the event. -->
	<!-- This is a TR that looks up the recommender code and the number of roulette available. -->
	<transaction target="tuxedo" tr="TCF9002_Q06" key="en" timeout="60">
	  <request>
	    <field name="EVENTID" len="10" type="L" id="" en="event_id" />
	    <field name="ACCT_NO" len="8" type="L" id="" en="acct_no" />
	    <field name="LOGIN_ID" len="8" type="L" id="" en="login_id" />
	  </request>
	  <response>
	    <field name="대상구분" len="1" type="L" id="" en="target_tp" /><!-- 0:available, 1:customer already participating, X :unable to participate -->
	    <field name="기타정보1" len="20" type="L" id="" en="etc_info1" /><!-- recommender code -->
	    <field name="기타정보2" len="20" type="L" id="" en="etc_info2" /><!-- the number of roulette available -->
	    <field name="기타정보3" len="20" type="L" id="" en="etc_info3" /><!-- the number of roulette has been used -->
	  </response>
	</transaction>
	
	<!-- This is a TR for applying to participate in the event. -->
	<transaction target="tuxedo" tr="TCF9002_U03" key="en" timeout="60">
	  <request>
	    <field name="EVENTID" len="10" type="L" id="" en="event_id" />
	    <field name="LOGIN_ID" len="8" type="L" id="" en="login_id" />
	    <field name="ACCT_NO" len="8" type="L" id="" en="acct_no" />
	    <field name="MARKET_INFO_YN" len="1" type="L" id="" en="market_info_yn" />
	  </request>
	  <response>
	    <field name="FILLER" len="1" type="L" id="" en="filler" />
	  </response>
	</transaction>
	
	<!-- This is TR for roulette handling. -->
	<transaction target="tuxedo" tr="TCF9002_U04" key="en" timeout="60">
	  <request>
	    <field name="EVENTID" len="10" type="L" id="" en="event_id" />
	    <field name="ACCT_NO" len="8" type="L" id="" en="acct_no" />
	    <field name="LOGINID" len="8" type="L" id="" en="login_id" />
	    <field name="종목코드" len="25" type="L" id="" en="stock_code" />
	  </request>
	  <response>
	    <field name="룰렛가능횟수" len="5" type="L" id="" en="able_cnt" />
	  </response>
	</transaction>
	
	<!-- This is the TR to look up the history of roulette results. -->
	<transaction target="tuxedo" tr="TCF9002_Q07" key="en" timeout="60">
	  <request>
	    <field name="EVENTID" len="10" type="L" id="" en="event_id" />
	    <field name="ACCT_NO" len="8" type="L" id="" en="acct_no" />
	    <field name="LOGIN_ID" len="8" type="L" id="" en="login_id" />
	    <field name="STOCK_CODE" len="25" type="L" id="" en="stock_code" />
	  </request>
	  <response>
	    <field name="고객식별ID" len="6" type="L" id="" en="acct_info" />
	    <field name="룰렛가능횟수" len="5" type="RO" id="" en="able_cnt" />
	    <field name="사용한룰렛횟수" len="5" type="RO" id="" en="used_cnt" />
	    <field name="조회건수" len="4" type="RO" id="" en="out_count" />
	    <repeat count="out_count">
	      <field name="일련번호" len="5" type="RO" id="" en="seq" />
	      <field name="종목코드" len="25" type="L" id="" en="stock_code" />
	      <field name="종목명" len="50" type="L" id="" en="stock_nm" />
	      <field name="추첨일" len="8" type="L" id="" en="rgst_dt" />
	      <field name="지급일" len="8" type="L" id="" en="proc_dt" />
	      <field name="추천계좌번호" len="8" type="L" id="" en="acct_no" />
	      <field name="추천계좌번호등록일" len="8" type="L" id="" en="acct_dt" />
	      <field name="종목수량" len="10" type="RO" id="" en="stock_qty" />
	      <field name="ticketinfo" len="50" type="L" id="" en="ticket_info" />
	    </repeat>
	  </response>
	</transaction>
	
	<!-- Search for Login ID -->
	<transaction target="tuxedo" tr="TLO1001_Q06" key="en" timeout="60">
	  <request>
	    <field name="이메일" len="60" type="L" id="" en="email" />
	    <field name="PHONE_NO" len="20" type="L" id="" en="phone_no" />
	    <field name="엄마이름" len="50" type="L" id="" en="mother_nm" />
	  </request>
	  <response>
	    <field name="로그인ID" len="8" type="L" id="" en="login_id" />
	    <field name="존재여부" len="1" type="L" id="" en="id_yn" />
	  </response>
	</transaction>
	
	<!-- Login Password Pre Request -->
	<transaction target="tuxedo" tr="TCF1012_U01" key="en" timeout="60">
	  <request>
	    <field name="로그인ID" len="8" type="L" id="" en="login_id" />
	    <field name="EMAIL" len="60" type="L" id="" en="email" />
	    <field name="ID_NO" len="30" type="L" id="" en="id_no" />
	  </request>
	  <response>
	    <field name="OK_YN" len="1" type="L" id="" en="ok_yn" />
	    <field name="MSG" len="100" type="L" id="" en="ok_msg" />
	    <field name="KEY" len="20" type="L" id="" en="ok_key" />
	    <field name="고객명" len="50" type="L" id="" en="acnt_nm" />
	  </response>
	</transaction>
	
	<!-- Login Password Reset Proc -->
	<transaction target="tuxedo" tr="TCF1012_U02" key="en" timeout="60">
	  <request>
	    <field name="고객ID" len="8" type="L" id="" en="login_id" />
	    <field name="KEY" len="20" type="L" id="" en="key" />
	    <field name="새비밀번호" len="50" type="L" id="" en="new_pw" />
	    <field name="새비밀번호확인" len="50" type="L" id="" en="confirm_pw" />
	  </request>
	  <response>
	    <field name="FILLER" len="1" type="L" id="" en="filler" />
	  </response>
	</transaction>
	
	<!-- Login Password Key Check -->
	<transaction target="tuxedo" tr="TCF1012_U03" key="en" timeout="60">
	  <request>
	    <field name="로그인ID" len="8" type="L" id="" en="login_id" />
	    <field name="KEY" len="20" type="L" id="" en="key" />
	  </request>
	  <response>
	    <field name="OK_YN" len="1" type="L" id="" en="ok_yn" />
	  </response>
	</transaction>
	
	<transaction target="tuxedo" tr="TCF1013_U01" key="en" timeout="60">
	  <request>
	    <field name="로그인ID" len="8" type="L" id="" en="login_id" />
	    <field name="EMAIL" len="60" type="L" id="" en="email" />
	    <field name="ID_NO" len="30" type="L" id="" en="id_no" />
	  </request>
	  <response>
	    <field name="OK_YN" len="1" type="L" id="" en="ok_yn" />
	    <field name="MSG" len="100" type="L" id="" en="ok_msg" />
	    <field name="KEY" len="20" type="L" id="" en="ok_key" />
	    <field name="고객명" len="50" type="L" id="" en="acnt_nm" />
	  </response>
	</transaction>
	
	<!-- Login Pin Reset Proc -->
	<transaction target="tuxedo" tr="TCF1013_U02" key="en" timeout="60">
	  <request>
	    <field name="고객ID" len="8" type="L" en="login_id" />
	    <field name="KEY" len="20" type="L" en="key" />
	    <field name="새비밀번호" len="50" type="L" en="new_pw" />
	    <field name="새비밀번호확인" len="50" type="L" en="confirm_pw" />
	  </request>
	  <response>
	    <field name="FILLER" len="1" type="L" en="filler" />
	  </response>
	</transaction>
	<transaction target="tuxedo" tr="TCF1013_U03" key="en" timeout="60">
	  <request>
	    <field name="로그인ID" len="8" type="L" id="" en="login_id" />
	    <field name="KEY" len="20" type="L" id="" en="key" />
	  </request>
	  <response>
	    <field name="OK_YN" len="1" type="L" id="" en="ok_yn" />
	  </response>
	</transaction>
	
</xml>