<?xml version="1.0" encoding="UTF-8"?>
<xml>
 <tran-header target="apcall">
        <field name="TRCODE" len="8" type="L" id=""/>
        <field name="CLIENTIP" len="16" type="L" id=""/>
        <field name="USERID" len="8" type="L" id=""/>
        <field name="FILLER" len="2" type="L" id=""/>
        <field name="RTCD" len="1" type="L" id=""/>
 </tran-header>

 <transaction target="apcall" tr="enca0101" key="name" timeout="120">
   <request>
          <field name="신원정보size" len="4" type="L" id=""/>
          <field name="신원정보" len="신원정보size" type="M" id=""/>
          <field name="전자서명size" len="4" type="L" id=""/>
          <field name="전자서명" len="전자서명size" type="M" id=""/>
   </request>
   <response>
          <field name="상태코드" len="5" type="L" id=""/>
          <field name="메세지" len="80" type="L" id=""/>
   </response>
 </transaction>
 
 <!-- enca2950 = enca2951, enca2955, enca2950, enca2952, enca2953 -->
 <transaction target="apcall" tr="enca2950" key="name" timeout="120">
   <request>
          <field name="처리구분" len="1" type="L" id="" comment="0:개인, 1:법인"/>
          <field name="주민번호" len="13" type="L" id=""/>
          <field name="계좌명" len="40" type="L" id=""/>
          <field name="계좌번호" len="8" type="L" id=""/>
          <field name="계좌비밀번호" len="64" type="L" id=""/>
          <field name="통신ID" len="8" type="L" id=""/>
          <field name="비밀번호" len="8" type="L" id=""/>
          <field name="OTP처리구분" len="1" type="L" id=""/>
          <field name="연계은행코드" len="3" type="L" id=""/>
          <field name="연계은행계좌번호" len="20" type="L" id=""/>
          <field name="초등학교이름" len="30" type="L" id=""/>
          <field name="친한친구이름" len="30" type="L" id=""/>
   </request>
   <response>
          <field name="결과코드" len="5" type="L" id=""/>
          <field name="참조번호" len="30" type="L" id=""/>
          <field name="인가코드" len="30" type="L" id=""/>
          <field name="DN" len="256" type="L" id=""/>
          <field name="인증기관결과코드" len="5" type="L" id=""/>
          <field name="메세지" len="80" type="L" id=""/>
   </response>
 </transaction>
 
  <!-- enca2956 -->
 <transaction target="apcall" tr="enca2956" key="name" timeout="120">
   <request>
          <field name="통신ID" len="8" type="L" id=""/>
          <field name="주민번호" len="13" type="L" id=""/>
          <field name="통신ID비밀번호" len="8" type="L" id=""/>
   </request>
   <response>
          <field name="결과코드" len="5" type="L" id=""/>
            <repeat count="80">
              <field name="계좌목록" len="8" type="L" id=""/>
            </repeat>
            <repeat count="80">
              <field name="계좌명목록" len="40" type="L" id=""/>
            </repeat>
          <field name="계좌건수" len="2" type="L" id=""/>
          <field name="단말기지정유무" len="1" type="L" id=""/>
          <field name="추가인증지정유무" len="1" type="L" id=""/>
          <field name="메세지" len="80" type="L" id=""/>
   </response>
 </transaction>
 
</xml>