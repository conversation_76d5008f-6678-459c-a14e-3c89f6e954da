<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE tiles-definitions PUBLIC
		"-//Apache Software Foundation//DTD Tiles Configuration 3.0//EN"
		"http://tiles.apache.org/dtds/tiles-config_3_0.dtd">
<tiles-definitions>
	
	<!-- frame layout (background : white) -->
	<definition name="frame-layout" template="/WEB-INF/views/common/layout/frame-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/header.jsp"></put-attribute>
		<put-attribute name="top" value="/WEB-INF/views/common/layout/top.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
		<put-attribute name="quick" value="/WEB-INF/views/common/layout/quick.jsp"></put-attribute>
		<put-attribute name="footer" value="/WEB-INF/views/common/layout/footer.jsp"></put-attribute>
	</definition>
	<definition name="frame:*" extends="frame-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="frame:*/*" extends="frame-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="frame:*/*/*" extends="frame-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="frame:*/*/*/*" extends="frame-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="frame:*/*/*/*/*" extends="frame-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- frame layout (background : gray) -->
	<definition name="frame-layout-gray" template="/WEB-INF/views/common/layout/frame-layout-gray.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/header.jsp"></put-attribute>
		<put-attribute name="top" value="/WEB-INF/views/common/layout/top.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
		<put-attribute name="quick" value="/WEB-INF/views/common/layout/quick.jsp"></put-attribute>
		<put-attribute name="footer" value="/WEB-INF/views/common/layout/footer.jsp"></put-attribute>
	</definition>
	<definition name="frame-gray:*" extends="frame-layout-gray">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="frame-gray:*/*" extends="frame-layout-gray">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="frame-gray:*/*/*" extends="frame-layout-gray">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="frame-gray:*/*/*/*" extends="frame-layout-gray">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="frame-gray:*/*/*/*/*" extends="frame-layout-gray">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- 'main in homepage' layout -->
	<definition name="main-layout" template="/WEB-INF/views/common/layout/frame-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/main-header.jsp"></put-attribute>
		<put-attribute name="top" value="/WEB-INF/views/common/layout/top.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
		<put-attribute name="quick" value="/WEB-INF/views/common/layout/quick.jsp"></put-attribute>
		<put-attribute name="footer" value="/WEB-INF/views/common/layout/footer.jsp"></put-attribute>
	</definition>
	<definition name="main:*" extends="main-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="main:*/*" extends="main-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="main:*/*/*" extends="main-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="main:*/*/*/*" extends="main-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="main:*/*/*/*/*" extends="main-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- event layout -->
	<definition name="event-layout" template="/WEB-INF/views/common/layout/frame-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/event-header.jsp"></put-attribute>
		<put-attribute name="top" value="/WEB-INF/views/common/layout/top.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
		<put-attribute name="quick" value="/WEB-INF/views/common/layout/quick.jsp"></put-attribute>
		<put-attribute name="footer" value="/WEB-INF/views/common/layout/footer.jsp"></put-attribute>
	</definition>
	<definition name="event:*" extends="event-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="event:*/*" extends="event-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="event:*/*/*" extends="event-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="event:*/*/*/*" extends="event-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="event:*/*/*/*/*" extends="event-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- popup layout -->
	<definition name="popup-layout" template="/WEB-INF/views/common/layout/popup-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/popup-header.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
	</definition>
	<definition name="popup:*" extends="popup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="popup:*/*" extends="popup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="popup:*/*/*" extends="popup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="popup:*/*/*/*" extends="popup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="popup:*/*/*/*/*" extends="popup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- 'screen in hts' layout -->
	<definition name="hts-layout" template="/WEB-INF/views/common/layout/hts-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/hts-header.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
	</definition>
	<definition name="hts:*" extends="hts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="hts:*/*" extends="hts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="hts:*/*/*" extends="hts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="hts:*/*/*/*" extends="hts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="hts:*/*/*/*/*" extends="hts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- 'screen in hts banner (frontpage)' layout -->
	<definition name="htsbanner-layout" template="/WEB-INF/views/common/layout/htsbanner-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/hts-header.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
	</definition>
	<definition name="htsbanner:*" extends="htsbanner-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="htsbanner:*/*" extends="htsbanner-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="htsbanner:*/*/*" extends="htsbanner-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="htsbanner:*/*/*/*" extends="htsbanner-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="htsbanner:*/*/*/*/*" extends="htsbanner-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- 'screen in hts news (frontpage)' layout -->
	<definition name="htsnews-layout" template="/WEB-INF/views/common/layout/htsnews-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/hts-header.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
	</definition>
	<definition name="htsnews:*" extends="htsnews-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="htsnews:*/*" extends="htsnews-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="htsnews:*/*/*" extends="htsnews-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="htsnews:*/*/*/*" extends="htsnews-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="htsnews:*/*/*/*/*" extends="htsnews-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- 'screen in hts banner (frontpage)' layout -->
	<definition name="htspopup-layout" template="/WEB-INF/views/common/layout/htspopup-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/hts-header.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
	</definition>
	<definition name="htspopup:*" extends="htspopup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="htspopup:*/*" extends="htspopup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="htspopup:*/*/*" extends="htspopup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="htspopup:*/*/*/*" extends="htspopup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="htspopup:*/*/*/*/*" extends="htspopup-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- 'screen in mts' layout -->
	<definition name="mts-layout" template="/WEB-INF/views/common/layout/mts-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/mts-header.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
	</definition>
	<definition name="mts:*" extends="mts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="mts:*/*" extends="mts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="mts:*/*/*" extends="mts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="mts:*/*/*/*" extends="mts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="mts:*/*/*/*/*" extends="mts-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
	<!-- etc layout -->
	<definition name="etc-layout" template="/WEB-INF/views/common/layout/etc-layout.jsp">
		<put-attribute name="header" value="/WEB-INF/views/common/layout/etc-header.jsp"></put-attribute>
		<put-attribute name="content-body" value="/WEB-INF/common/views/layout/content-body.jsp"></put-attribute>
	</definition>
	<definition name="etc:*" extends="etc-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}.jsp"></put-attribute>
	</definition>
	<definition name="etc:*/*" extends="etc-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}.jsp"></put-attribute>
	</definition>
	<definition name="etc:*/*/*" extends="etc-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}.jsp"></put-attribute>
	</definition>
	<definition name="etc:*/*/*/*" extends="etc-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}.jsp"></put-attribute>
	</definition>
	<definition name="etc:*/*/*/*/*" extends="etc-layout">
		<put-attribute name="content-body" value="/WEB-INF/views/{1}/{2}/{3}/{4}/{5}.jsp"></put-attribute>
	</definition>
	
</tiles-definitions>