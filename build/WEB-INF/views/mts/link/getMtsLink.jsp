<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<style type="text/css">
	a, button {cursor:pointer; text-decoration:none !important;display:inline-block;}
	.bottom_btn { position: relative; }
	.bottom_btn p { position: fixed; bottom: 5px; width: 35px; height: 34px; z-index: 10000; }
	.bottom_btn p a { position: relative; width: 100%; height: 100%; }
	.bottom_btn .btn_scroll_top { right: 5px; }
	.bottom_btn .btn_scroll_top { right: 5px; }
	.icon { background-size: cover; background-position: center; background-repeat: no-repeat; }
	.ic_btn_top { background-image: url('../images/ic_btn_top.png'); }
	#dimmLoader { width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 10001; display: none; opacity: 0.3; background: #000;}
</style>
<script type="text/javascript">
$(document).ready(function() {
	let menu = '${menu}';
	if(!menu || isNaN(menu) ) menu = '295';
	
	goToMobile(menu); 
});

$(document).on('click',"#clickImg",function(e){
	e = e || window.event;
	e.preventDefault();
	
	let menu = '${menu}';
	if(!menu || isNaN(menu) ) menu = '295';
	
	goToMobile(menu);
});

const goToMobile = (menu) => {
	gfnBlockUI();
	const visitTm = (new Date()).getTime();
	
	if(global.device == '0'){	// device : PC
		window.open("/Main", "_blank");	
	}else{	// device : Mobile
		let code ="${code}";
		let from ="${from}";
		let url = "intent://kiwoomheroshost?menu="+menu+"&code="+code+"&from="+from+"#Intent;scheme=kiwoomheros;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;launchFlags=0x30000000;package=com.linkzen.app;end";
		
		if(global.os == 'I') {
			url = "heromts://applink?code="+code+"&menu="+menu+"&from="+from;
			setTimeout(function(){
				var compareTm = (new Date()).getTime() - visitTm;
				if( compareTm > 3500 && compareTm < 4500 ){
					location.href="itms-apps://itunes.apple.com/kr/app/id1570370057?mt=8";
					return false;
				}
			}, 3500);
			
		} else if(global.os == 'A') {
			url = "intent://heromtshost?menu="+menu+"&code="+code+"&from="+from+"#Intent;scheme=heromts;action=android.intent.action.VIEW;category=android.intent.category.BROWSABLE;launchFlags=0x30000000;package=com.kiwoom.heromts;end";
		} else {
			url = "/Main";
		}
		
		location.href = url;
	}
	
	setTimeout(gfnUbBlockUI, 3000);
}

const gfnBlockUI = () => {
	$('#dimmLoader').css("display", "block");
	$('body').append('<article id="artWait" style="z-index:10002;height: auto; position: fixed; left: 45%; top: 40%; z-index: 1000;"><img src="/inv/images/ajax-loader.gif" /></article>');	
}

const gfnUbBlockUI = () => {
	$('#artWait').remove();
	$('#dimmLoader').css("display", "none");
}

</script>
<body style=" margin: 0px;" >
	<div id="dimmLoader" style="width:100%; height:100%; display:none;"></div>
	<div class="all_wrap">
		<div></div>
    	<div style="width:100%">
       		<div style="position:relative;">
       			<div id="clickImg" style="cursor: pointer;">
       				<img id="imgAdv" src="/inv/images/ad/slink.png" style="position: absolute; top:0; left:0; right:0; bottom:0; width: 100%; height: auto; vertical-align: middle; background-position: center center !important; ">
       			</div>
       		</div>
    	</div>
		<div class="bottom_btn">
       		<p class="icon btn_scroll_top ic_btn_top"><a onclick="$('html, body').animate({ scrollTop: 0 }, 300); return false;"></a></p>
		</div>
	</div>
</body>