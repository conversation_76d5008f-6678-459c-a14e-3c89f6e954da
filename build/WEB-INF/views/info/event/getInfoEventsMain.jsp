<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<script type="text/javascript">
    let tblSearch;
    let currentOffset = 3;
    let searching = false;

    $(document).ready(function () {
        init();
    });

    function init() {
        initButton();
        // $("#lang-select button").prop("disabled", true);
        // $("#lang-select ul button").prop("disabled", true);
        $('#lang-select ul li button').each(function() {
            if ($(this).text().trim() === 'EN') {
                $(this).closest('li').remove();
            }
        });
    }

    function initButton() {
        $("#btnViewMore").on('click', function () {
            onSearch(currentOffset, 3, true);
        });
    }

    function onSearch(offset, limit, isViewMore) {
        searching = true;
        var jsonObj = {
            isShowLoading: false,
            method: 'POST',
            url: "/info/event/getInfoEventsList",
            contentType: 'application/json; charset=utf-8',
            cache: false,
            data: JSON.stringify({
                limit: limit,
                offset: offset
            }),
            async: true,
            success: function (list) {
                if (!isViewMore) {
                    if ($("#divNoData")) {
                        $("#divNoData").remove();
                    }
                    $("#listInfoEvents").empty();
                }

                const isDeviceMobile = global.mod === 'mo';

                if (list.data && list.data.length > 0) {
                    for (let i = 0; i < list.data.length; i++) {
                        const infoEvents = list.data[i];

                        // Determine image and link based on the device type
                        const image = isDeviceMobile ? infoEvents.MOBILE_IMG_URL : infoEvents.IMG_URL;
                        const link = isDeviceMobile ? infoEvents.MOBILE_LINK_URL : infoEvents.PC_LINK_URL;
                        const linkMethod = infoEvents.LINK_METHOD || "_self";

                        let li = "<li>";

                        // Top section with image and link
                        li += '<div class="top">';
                        if (image) {
                            li += '<a class="info-event-container" href="' + (link || '#') + '" target="' + linkMethod + '">';
                            li += '<img class="info-event-image" src="${pageContext.request.contextPath}/kid/upload/banner/' + image + '" alt="Event Image">';
                            li += '</a>';
                        }
                        li += '</div>';

                        // Mid section with title and link
                        li += '<div class="mid">';
                        if (infoEvents.TITL) {
                            const title = infoEvents.TITL;
                            li += '<a href="' + (link || '#') + '" target="' + linkMethod + '">';
                            li += title;
                            li += '</a>';
                        }
                        li += '</div>';

                        // Bottom section with description
                        li += '<div class="">';
                        if (infoEvents.INFO_DESC) {
                            const description = infoEvents.INFO_DESC;
                            li += description;
                        }
                        li += '</div>';

                        li += '</li>';
                        $("#listInfoEvents").append(li);
                    }
                } else {
                    let div = '<div class="info-event-list-empty report-list report-list-event "><div id="divNoData" class="nodata-area">';
                    div += '<h3>No data to display</h3>';
                    div += '<div>';
                    div += '<img src="${pageContext.request.contextPath}/kid/images/common/bg_no_data.png" alt="No Data">';
                    div += '</div>';
                    div += '</div></div>';
                    $("#listInfoEvents").before(div);
                }

                currentOffset = offset + limit;
                searching = false;

                if (list.canFetchData) {
                    $("#btnViewMore").attr("style", "");
                } else {
                    $("#btnViewMore").attr("style", "display:none;");
                }
            },
            error: function (request, status, error) {
                searching = false;
                kidCommon.showErrorMessage(request, status, error);
            }
        }
        kidCommon.ajax(jsonObj);
    }
</script>
<main>
    <div class="grand-banner info-event">
        <h2>Event</h2>
    </div>
    <!-- S:CONTENTS AREA -->
    <div class="contents-wrap">
        <!-- S:LIST -->

        <c:choose>
            <c:when test="${infoEventsList.data != null && fn:length(infoEventsList.data) > 0}">
                <div class="info-event-list report-list report-list-event ">
                    <ul id="listInfoEvents">
                        <c:forEach items="${infoEventsList.data}" var="infoEvents">
                            <li>
                                <div class="top">
                                    <c:set value="${isDevice != 'PC' ? infoEvents.MOBILE_IMG_URL : infoEvents.IMG_URL}"
                                           var="image"/>
                                    <c:set value="${isDevice != 'PC' ? infoEvents.MOBILE_LINK_URL : infoEvents.PC_LINK_URL}"
                                           var="link"/>
                                    <a href="${link}" class="info-event-container"
                                       target="${infoEvents.LINK_METHOD}"><img class="info-event-image"
                                                                               src="${pageContext.request.contextPath}/kid/upload/banner/${image}"
                                                                               alt="Event image"/></a>
                                </div>
                                <div class="mid">
                                    <a href="${link}" target="${infoEvents.LINK_METHOD}">${infoEvents.TITL}</a>
                                </div>
                                <div class="">
                                        ${infoEvents.INFO_DESC}
                                </div>
                            </li>
                        </c:forEach>
                    </ul>
                </div>
            </c:when>
            <c:otherwise>
                <div class="info-event-list-empty report-list report-list-event ">
                    <div id="divNoData" class="nodata-area">
                        <h3>No data to display</h3>
                        <div>
                            <img src="${pageContext.request.contextPath}/kid/images/common/bg_no_data.png"
                                 alt="No Data">
                        </div>
                    </div>
                </div>
            </c:otherwise>
        </c:choose>

        <!-- E:LIST -->
        <!-- S:MORE -->
        <c:set var="canFetchData" value="${infoEventsList.canFetchData == true ? '' : 'display: none' }"/>
        <div class="btn-viewmore" style="${canFetchData}">
            <button type="button" id="btnViewMore">View more</button>
        </div>
        <!-- E:MORE -->
    </div>
    <!-- E:CONTENTS AREA -->
</main>
