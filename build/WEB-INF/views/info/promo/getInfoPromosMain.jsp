<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<script type="text/javascript">
    let tblSearch;
    let currentOffset = 2;
    let searching = false;

    $(document).ready(function () {
        init();
    });

    function init() {
        initButton();
    }

    function initButton() {
        $("#btnViewMore").on('click', function () {
            onSearch(currentOffset, 2, true);
        });
    }

    function onSearch(offset, limit, isViewMore) {
        searching = true;
        var jsonObj = {
            isShowLoading: false,
            method: 'POST',
            url: "/info/promo/getInfoPromosList",
            contentType: 'application/json; charset=utf-8',
            cache: false,
            data: JSON.stringify({
                limit: limit,
                offset: offset
            }),
            async: true,
            success: function (list) {
                if (!isViewMore) {
                    if ($("#divNoData")) {
                        $("#divNoData").remove();
                    }
                    $("#listInfoEvents").empty();
                }
                let language = getLanguage() || '0';
                const langId = language === '1' ? 'active' : '';
                const langEn = language === '0' ? 'active' : '';
                const isDeviceMobile = global.mod === 'mo';

                if (list.data && list.data.length > 0) {
                    for (let i = 0; i < list.data.length; i++) {
                        const infoEvents = list.data[i];

                        // Determine image and link based on the device type
                        const image = isDeviceMobile ? infoEvents.MOBILE_IMG_URL : infoEvents.IMG_URL;
                        const link = ${pageContext.request.contextPath}'/info/promo/detailInfoPromoMain?id=+'+infoEvents.SEQ_NO;
                        const linkMethod = infoEvents.LINK_METHOD || "_self";

                        let li = "<li>";

                        // Top section with image and link
                        li += '<div class="top">';
                        if (image) {
                            li += '<a href="'+link+'" target="'+linkMethod+'" class="info-event-container">';
                            li += '<img class="info-promo-image" src="${pageContext.request.contextPath}/kid/upload/banner/' + image + '" alt="Event Image">';
                            li += '</a>';
                        }
                        li += '</div>';
                        li += '<div class="mid">';
                        li += '<a href="'+link+'" target="'+linkMethod+'">'+infoEvents.TITL+'</a>';
                        li += '</div>';
                        // Mid section with title and link
                        li += '<div class="promo-bottom">';
                        li += '   <div class="left">';
                        li += '     <a class="info-promo-container" href="'+link+'" target="' + linkMethod + '">';
                        li += '         <span class="underline lang-en '+langEn+'">Terms & Conditions</span>';
                        li += '         <span class="underline lang-id '+langId+'">Syarat & Ketentuan</span>';
                        li += '         <span class="arrow">→</span>';
                    li += '         </a>';
                        li += '   </div>';
                        li += '   <div class="right">';
                        li += '       <span>By ' + (infoEvents.RGST_ID ? infoEvents.RGST_ID : 'Admin') + '</span>';
                        li += '       <span class="divider">|</span>';
                        li += '       <span>' + (infoEvents.RGST_TIME ? infoEvents.RGST_TIME : '') + '</span>';
                        li += '   </div>';
                        li += '</div>';
                        li += '</li>';
                        $("#listInfoEvents").append(li);
                    }
                } else {
                    let div = '<div class="info-event-list-empty report-list report-list-event "><div id="divNoData" class="nodata-area">';
                    div += '<h3>No data to display</h3>';
                    div += '<div>';
                    div += '<img src="${pageContext.request.contextPath}/kid/images/common/bg_no_data.png" alt="No Data">';
                    div += '</div>';
                    div += '</div></div>';
                    $("#listInfoEvents").before(div);
                }

                currentOffset = offset + limit;
                searching = false;

                if (list.canFetchData) {
                    $("#btnViewMore").attr("style", "");
                } else {
                    $("#btnViewMore").attr("style", "display:none;");
                }
            },
            error: function (request, status, error) {
                searching = false;
                kidCommon.showErrorMessage(request, status, error);
            }
        }
        kidCommon.ajax(jsonObj);
    }
</script>
<main>
    <div class="grand-banner info-event">
        <h2>WHAT'S ON KIWOOM</h2>
    </div>
    <!-- S:CONTENTS AREA -->
    <div class="contents-wrap">
        <!-- S:LIST -->

        <c:choose>
            <c:when test="${infoPromosList.data != null && fn:length(infoPromosList.data) > 0}">
                <div class="info-promo-list report-list report-list-event promo-report-list">
                    <ul id="listInfoEvents">
                        <c:forEach items="${infoPromosList.data}" var="infoEvents">
                            <li>
                                <div class="top">
                                    <c:set value="${isDevice != 'PC' ? infoEvents.MOBILE_IMG_URL : infoEvents.IMG_URL}"
                                           var="image"/>
                                    <c:set value="/info/promo/detailInfoPromoMain?id=${infoEvents.SEQ_NO}"
                                           var="link"/>
                                    <a href="${link}" target="_self" class="info-event-container"><img class="info-promo-image"
                                                                               src="${pageContext.request.contextPath}/kid/upload/banner/${image}"
                                                                               alt="Event image"/></a>
                                </div>
                                <div class="mid">
                                       <a href="${link}">${infoEvents.TITL}</a>
                                </div>
                                <div class="promo-bottom">
                                    <div class="left">
                                        <a href="${link}" class="info-promo-container"
                                           target="${infoEvents.LINK_METHOD}">
                                            <span class="underline lang-en active">Terms & Conditions</span>
                                            <span class="underline lang-id">Syarat & Ketentuan</span>
                                            <span class="arrow">→</span>
                                        </a>
                                    </div>
                                    <div class="right">
                                        <span>By ${infoEvents.RGST_ID}</span>
                                        <span class="divider">|</span>
                                        <span>${infoEvents.RGST_TIME}</span>
                                    </div>
                                </div>
                            </li>
                        </c:forEach>
                    </ul>
                </div>
            </c:when>
            <c:otherwise>
                <div class="info-promo-list-empty report-list report-list-event ">
                    <div id="divNoData" class="nodata-area">
                        <h3>No data to display</h3>
                        <div>
                            <img src="${pageContext.request.contextPath}/kid/images/common/bg_no_data.png"
                                 alt="No Data">
                        </div>
                    </div>
                </div>
            </c:otherwise>
        </c:choose>

        <!-- E:LIST -->
        <!-- S:MORE -->
        <c:set var="canFetchData" value="${infoPromosList.canFetchData == true ? '' : 'display: none' }"/>
        <div class="btn-viewmore" style="${canFetchData}">
            <button type="button" id="btnViewMore">View more</button>
        </div>
        <!-- E:MORE -->
    </div>
    <!-- E:CONTENTS AREA -->
</main>
