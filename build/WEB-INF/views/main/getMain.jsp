<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<style>
	/* Product Header */
	.product-header {
		height: 50px;
	}

	.switch-button-active {
		float: right;
		width: 100px;
		height: 50px;
		cursor: pointer;
	}
	/* For WebKit browsers (Chrome, Edge, Safari) */
	.scrollable-table::-webkit-scrollbar {
		width: 8px; /* Width of the scrollbar */
	}

	.scrollable-table::-webkit-scrollbar-track {
		background: #f1f1f1; /* Background of the scrollbar track */
	}

	.scrollable-table::-webkit-scrollbar-thumb {
		background: #888; /* Color of the scrollbar handle */
		border-radius: 10px; /* Rounded corners for the scrollbar handle */
	}

	.scrollable-table::-webkit-scrollbar-thumb:hover {
		background: #555; /* Darker color on hover */
	}
	.scrollable-table {
		max-height: 420px; /* Set the maximum height of the scrollable area */
		overflow-y: auto; /* Enable vertical scrolling */
		overflow-x: hidden; /* Disable horizontal scrolling if not needed */
		margin: 0 auto; /* Center the table container */
		width: 99%; /* Default width when no scrollbar */
		scrollbar-color: rgba(230, 230, 232, 1) #f1f1f1; /* Handle color and track color */
		scrollbar-width: thin; /* Thin scrollbar */
		transition: width 0.3s ease; /* Smooth transition for width change */
	}
	/* General Table Styling */
	#productTable {
		width: 99%;
		border-collapse: collapse;
		background-color: #ffffff;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		border-radius: 8px;
		overflow: hidden;
		overflow-x: auto; /* Optional Scrollbar for Overflow */
	}
	/* Styling for the Table Header */
	#productTable thead tr {
		position: sticky; /* Stick the header to the top */
		top: 0; /* Ensure it sticks to the top of the scrollable container */
	}

	/* Styling for the Table Header */
	#productTable thead th {
		background-color: rgba(216, 225, 250, 1); /* Light background color for the header */
		text-align: left; /* Align text to the left */
		padding: 12px 15px; /* Padding inside the cells */
		font-size: 14px; /* Font size */
		font-weight: bold; /* Make the header text bold */
		border-right: 2px solid #fff; /* Add right border */
		color: rgba(24, 25, 73, 1); /* Set text color */
		font-family: 'Fantasize', sans-serif; /* Font family */
		position: sticky; /* Stick the header to the top */
		top: 0; /* Ensure it sticks to the top of the scrollable container */
	}

	/* Remove the Right Border from the Last Header Cell */
	#productTable thead th:last-child {
		border-right: none;
	}

	/* First Header Column Styling (No) */
	#productTable thead th:first-child {
		text-align: center;
		width: 5%;
	}

	/* Centered Columns (Subscription and Redemption Forms) */
	#productTable thead th:last-child,
	#productTable thead th:nth-last-child(2) {
		text-align: center;
	}

	/* Body Row Styling */
	#productTable tbody tr {
		border-bottom: 1px solid #e5e5e5;
	}

	#productTable tbody tr:last-child {
		border-bottom: none;
	}

	/* Body Cell Styling */
	#productTable tbody td {
		padding: 12px 15px;
		font-size: 14px;
		vertical-align: middle;
		text-align: center; /* Center align text for all table cells */
		color: rgba(24, 25, 73, 1); /* Set text color */
		font-family: 'Fantasize', sans-serif; /* Font family */
	}

	/* Left align text for the "Product" column (second column) */
	#productTable td:nth-child(2) {
		text-align: left;
		font-weight: bold;
		color: #2c3e50;
	}

	#productTable tbody td:nth-child(2) small {
		display: block;
		font-size: 12px;
		color: #7f8c8d;
	}

	/* Center Align Specific Columns */
	#productTable tbody td:first-child,
	#productTable tbody td:last-child,
	#productTable tbody td:nth-last-child(2) {
		text-align: center;
	}

	/* Highlight on Hover */
	#productTable tbody tr:hover {
		background-color: #f9f9f9;
	}

	/* Icon Styling (Download Buttons) */
	#productTable tbody td a img {
		width: 16px;
		height: 16px;
		cursor: pointer;
		transition: transform 0.2s ease;
	}

	#productTable tbody td a img:hover {
		transform: scale(1.2);
	}

	/* Default Button Style */
	.download-btn {
		display: inline-block;
		width: 48px;
		height: 48px;
		background-image: url('${pageContext.request.contextPath}/kid/images/btn/btn_download.png');
		background-size: contain;
		background-repeat: no-repeat;
		transition: background-image 0.2s ease;
	}

	/* Hover Effect for Buttons */
	.download-btn:hover {
		background-image: url('${pageContext.request.contextPath}/kid/images/btn/btn_download_hover.png');
	}

	/* Sticky Columns for Mobile Only */
	@media screen and (max-width: 768px) {
		#productTable {
			margin-left: 10px;
			margin-right: 10px;
		}
		/* Sticky Columns */
		#productTable thead th:first-child,
		#productTable thead th:nth-child(2){
			position: sticky;
			z-index: 2; /* Ensure the sticky columns stay above other content */
			background-color: rgba(216, 225, 250, 1);
		}

		#productTable tbody td:first-child,
		#productTable tbody td:nth-child(2) {
			position: sticky;
			z-index: 2; /* Ensure the sticky columns stay above other content */
			background-color: white;
		}

		/* First Column - Sticky at Left */
		#productTable thead th:first-child,
		#productTable tbody td:first-child {
			left: 0; /* Fix the first column at the leftmost position */
		}

		/* Second Column - Sticky to the Right of the First */
		#productTable thead th:nth-child(2),
		#productTable tbody td:nth-child(2) {
			left: 48px; /* Adjust based on the width of the first column */
		}

		/* Ensure Vertical Scrolling on Mobile */
		.scrollable-table {
			overflow-y: scroll; /* Enable vertical scroll */
			max-height: 58vh; /* Adjust the height for mobile view */
		}
	}
</style>
<script type="text/javascript">

	// img lazy loading
	document.addEventListener("DOMContentLoaded", function() {
		var lazyloadImages;
		// Browsers that support IntersectionObserver
		if ("IntersectionObserver" in window) {
			lazyloadImages = document.querySelectorAll(".lazy");
		  	var imageObserver = new IntersectionObserver(function(entries, observer) {
		    	entries.forEach(function(entry) {
		      		if (entry.isIntersecting) {
		        		var image = entry.target;
		        		image.src = image.dataset.src;
		        		image.classList.remove("lazy");
		        		imageObserver.unobserve(image);
		      		}
		    	});
		  	});

		  	lazyloadImages.forEach(function(image) {
		    	imageObserver.observe(image);
		  	});

		} else {  // not support
		  	var lazyloadThrottleTimeout;
		  	lazyloadImages = document.querySelectorAll(".lazy");

		  	function lazyload () {
		    	if(lazyloadThrottleTimeout) {
		      		clearTimeout(lazyloadThrottleTimeout);
		    	}

		    	lazyloadThrottleTimeout = setTimeout(function() {
		      		var scrollTop = window.pageYOffset;
		      		lazyloadImages.forEach(function(img) {
		          		if(img.offsetTop < (window.innerHeight + scrollTop)) {
		            		img.src = img.dataset.src;
		            		img.classList.remove('lazy');
		          		}
		      		});
			      	if(lazyloadImages.length == 0) {
			        	document.removeEventListener("scroll", lazyload);
			        	window.removeEventListener("resize", lazyload);
			        	window.removeEventListener("orientationChange", lazyload);
			      	}
		    	}, 20);
			}

		  	document.addEventListener("scroll", lazyload);
		  	window.addEventListener("resize", lazyload);
		  	window.addEventListener("orientationChange", lazyload);
		}
	});

	//PRODUCT
	let productSwiper = undefined;
	//MAIN
	var _mainSwiperPC = undefined, _mainSwiperMO = undefined;
	$(document).ready(function() {
		init();
		initTopBanner("#topBanners","#mobileTopBanner");
		initResearchBanner("#researchBanners");
		initProductLeft("#mainProduct");
		initProductRight("#products");
		initMarketReport("#marketReport");
		initDailyNews("#dailyNews");
		initIpoNews("#ipoNews");
		initInternationalNews("#internationalNews");
		initCallButton();
		initNoticePopup();
		loadTableData();
	});

	function init() {
		toggle.init('.aboutUs-main', 'mo');
		tab.init('.tab-triger-list', '.tab-target-list');
		productSwich();
		initButton();
	}
	// Function to toggle the switch button image
	function toggleSwitchButton() {

		const imgElement = document.getElementById('switchButtonImg');
		const productCards = document.getElementById('productCards');
		const productTable = document.getElementById('productTable');

		const activeSrc = `${pageContext.request.contextPath}/kid/images/common/btn_switch_active.svg`;
		const inactiveSrc = `${pageContext.request.contextPath}/kid/images/common/btn_switch_inactive.svg`;

		if (imgElement.src.includes('btn_switch_active.svg')) {
			// Switch to Table View
			imgElement.src = inactiveSrc;
			productCards.style.display = 'none';
			productTable.style.display = 'block';
		} else {
			// Switch to Product Cards View
			imgElement.src = activeSrc;
			productCards.style.display = '';
			productTable.style.display = 'none';
		}
	}
	function initTopBanner(idPc, idMobile) {
		const url = `${pageContext.request.contextPath}/kid/upload/banner/json/main_banner01.json?${ver}`;
		$.getJSON(url, function(res) {
			$(idPc).empty();
			$(idMobile).empty();
			for (let i = 0; i < res.length; i++) {
				const banner = res[i];
				const bannerPcImgUrl = `${pageContext.request.contextPath}/kid/upload/banner/`+banner.imgFileUrl;
				let li = '<li class="swiper-slide">';
				if(banner.pcLinkUrl){
					li += '<a href="'+banner.pcLinkUrl+'" target="'+banner.linkMethod+'" class="banner-01" style="background-image: url('+bannerPcImgUrl+');" data-gtm-grp="Main" data-gtm-id="TopBannerBtn'+ (i+1) + '"></a>';
				}else {
					li += '<a class="banner-01" style="background-image: url('+bannerPcImgUrl+');"></a>';
				}
				$(idPc).append(li);
				const bannerMobileImgUrl = `${pageContext.request.contextPath}/kid/upload/banner/`+banner.mobileImgUrl;
				let mobileLi = '<li class="swiper-slide">';
				if(banner.mobileLinkUrl){
					mobileLi += '<a href="'+banner.mobileLinkUrl+'" target="'+banner.linkMethod+'" class="banner-01" data-gtm-grp="Main" data-gtm-id="TopBannerBtn'+ (i+1) + '">';
				}else {
					mobileLi += '<a class="banner-01">';
				}
				mobileLi += '<img src="'+bannerMobileImgUrl+'" alt="'+banner.alt+'"></img>';
				mobileLi += '</a>';
				$(idMobile).append(mobileLi);
			}
			//Total Banner Text Set!
			$('.current-num-pc').text(1);
			$('.all-num').text(res.length);
			initSwiperMain();
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getTopBanners`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(idPc).empty();
					$(idMobile).empty();
					for (let i = 0; i < res.length; i++) {
						const banner = res[i];
						const bannerPcImgUrl = `${pageContext.request.contextPath}/kid/upload/banner/`+banner.IMG_URL;
						let li = '<li class="swiper-slide">';
						if(banner.PC_LINK_URL){
							li += '<a href="'+banner.PC_LINK_URL+'" target="'+banner.LINK_METHOD+'" class="banner-01" style="background-image: url('+bannerPcImgUrl+');" data-gtm-grp="Main" data-gtm-id="TopBannerBtn'+ (i+1) + '"></a>';
						}else {
							li += '<a class="banner-01" style="background-image: url('+bannerPcImgUrl+');"></a>';
						}
						$(idPc).append(li);
						const bannerMobileImgUrl = `${pageContext.request.contextPath}/kid/upload/banner/`+banner.MOBILE_IMG_URL;
						let mobileLi = '<li class="swiper-slide">';
						if(banner.MOBILE_LINK_URL){
							mobileLi += '<a href="'+banner.MOBILE_LINK_URL+'" target="'+banner.LINK_METHOD+'" class="banner-01" data-gtm-grp="Main" data-gtm-id="TopBannerBtn'+ (i+1) + '">';
						}else {
							mobileLi += '<a class="banner-01">';
						}
						mobileLi += '<img src="'+bannerMobileImgUrl+'" alt="'+banner.IMG_DESC+'"></img>';
						mobileLi += '</a>';
						$(idMobile).append(mobileLi);
					}
					//Total Banner Text Set!
					$('.current-num-pc').text(1);
					$('.all-num').text(res.length);
					initSwiperMain();
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}
	function initResearchBanner(id) {
		const url = `${pageContext.request.contextPath}/kid/upload/banner/json/main_banner02.json?${ver}`;
		$.getJSON(url, function(res) {
			$(id).empty();
			for (let i = 0; i < res.length; i++) {
				const isMobile = global.mod == 'mo';
				const banner = res[i];
				const imgName = banner.imgFileUrl;
				const bannerImgUrl = `${pageContext.request.contextPath}/kid/upload/banner/`+imgName;
				const linkUrl = isMobile ? banner.mobileLinkUrl : banner.pcLinkUrl;
				let li = '<li class="swiper-slide">';
				if(linkUrl){
					li += '<a href="'+linkUrl+'" target="'+banner.linkMethod+'" class="banner-01" data-gtm-grp="Main" data-gtm-id="MiddleBannerBtn'+ (i+1) + '">';
				}else {
					li += '<a class="banner-01">';
				}
				li += '<img src="'+bannerImgUrl+'" alt="'+banner.alt+'"></img>';
				li += '</a>';
				$(id).append(li);
			}
			initSwiperResearch();
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getResearchBanners`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(id).empty();
					for (let i = 0; i < res.length; i++) {
						const isMobile = global.mod == 'mo';
						const banner = res[i];
						const imgName = banner.IMG_URL;
						const bannerImgUrl = `${pageContext.request.contextPath}/kid/upload/banner/`+imgName;
						const linkUrl = isMobile ? banner.MOBILE_LINK_URL : banner.PC_LINK_URL;
						let li = '<li class="swiper-slide">';
						if(linkUrl){
							li += '<a href="'+linkUrl+'" target="'+banner.LINK_METHOD+'" class="banner-01" data-gtm-grp="Main" data-gtm-id="MiddleBannerBtn'+ (i+1) + '">';
						}else {
							li += '<a class="banner-01">';
						}
						li += '<img src="'+bannerImgUrl+'" alt="'+banner.IMG_DESC+'"></img>';
						li += '</a>';
						$(id).append(li);
					}
					initSwiperResearch();
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}
	function loadTableData() {
		const url = `${pageContext.request.contextPath}/kid/upload/product/json/AllProductsMain.json?${ver}`;
		$.getJSON(url, function(res) {
			const tbody = $('#productTable tbody');
			tbody.empty(); // Clear existing rows

			// Loop through the JSON response and populate table rows
			res.forEach((item, index) => {
				const subscriptionFormUrl = item.FILENM_SUB
						? `${pageContext.request.contextPath}` + '/kid/upload/product/' + item.APNDNM_SUB
						: '#';

				const redemptionFormUrl = item.FILENM_RED
						? `${pageContext.request.contextPath}` + '/kid/upload/product/' + item.APNDNM_RED
						: '#';

				const subscriptionDisabled = !item.FILENM_SUB ? 'disabled' : '';
				const redemptionDisabled = !item.FILENM_RED ? 'disabled' : '';

				const row =
						'<tr>' +
						'<td>' + (index + 1) + '</td>' +
						'<td>' + item.NM + '</td>' +
						'<td>' + (item.RTN_RT_12 + '%' || '-') + '</td>' +
						'<td>' + (item.RTN_RT_36 + '%' || '-') + '</td>' +
						'<td>' + (item.RTN_RT_6 + '%' || '-') + '</td>' +
						'<td>Rp ' + item.AUM.toLocaleString() + '</td>' +
						'<td>' +
						'<a href="' + subscriptionFormUrl + '" class="download-btn ' + subscriptionDisabled + '" download="' + item.FILENM_SUB + '"></a>' +
						'</td>' +
						'<td>' +
						'<a href="' + redemptionFormUrl + '" class="download-btn ' + redemptionDisabled + '" download="' + item.FILENM_RED + '"></a>' +
						'</td>' +
						'</tr>';
				tbody.append(row); // Append the row to the table body
			});

			// Apply styles for disabled buttons
			$('.download-btn.disabled').css({
				pointerEvents: 'none',
				opacity: '0.5'
			});

			setTimeout(()=>{
				adjustScrollableTableWidth();
			},1000);
		}).fail(function() {

		});
	}

	function initProductLeft(id) {
		const url = `${pageContext.request.contextPath}/kid/upload/product/json/PRDMain.json?${ver}`;

		$.getJSON(url, function(res) {
			$(id).empty();
			var mainProduct = res[0];
			if(mainProduct){
				 var logoUrl = mainProduct.LOGO_IMG_URL
			        ? "${pageContext.request.contextPath}/kid/upload/product/" + mainProduct.LOGO_IMG_URL
			        : "${pageContext.request.contextPath}/kid/images/common/logo_default.png";
			    var mainProductNm = mainProduct.NM;
			    var mainRepTitl = mainProductNm.length > 25
			          ? mainProductNm.substring(0, 25) + "<br/>" + mainProductNm.substring(25)
			          : mainProductNm;
			    var rtnRt12 = mainProduct.RTN_RT_12 == 0 ? "-" : mainProduct.RTN_RT_12 + "%";
		    	var aum = mainProduct.AUM == 0? "-" : mainProduct.AUM;
		    	if(aum != "-"){
		    		aum = aum.toLocaleString();
		    	}
			    var score = mainProduct.SCORE * 20;
			    var html = '<div class="product-item-01">'
			       	html += '<div class="top">'
			        html += '<div class="logo">'
			        html += '<img src="'+logoUrl+'" alt="LOGO">'
			        html += '</div>'
			        html += '<h4>'+mainRepTitl+'</h4>'
			        html += '<br/>'
			        html += '</div>'
			        html += '<div class="mid">'
			        html += '<ul>'
			        html += '<li>'
			        html += '<p>1 Tahun terakhir*</p>'
			        html += '<div>'+rtnRt12+'</div>'
			        html += '</li>'
			        html += '<li>'
			        html += '<p>AUM (Miliyar)</p>'
			        html += '<div>'
			        html += '<em>Rp</em>'
			        html += aum
			        html += '</div>'
			        html += ' </li>'
			        html += '</ul>'
			        html += '</div>'
			        html += '<div class="bottom">'
			        html += '<div class="star-aria">'
			        html += '<div class="star-range">'
			        html += ' <span style="width: '+score+'%;"></span>'
			        html += '</div>'
			        html += '<p>Rating bintang di dapat dari ulasan pembeli.</p>'
			        html += '</div>'
			        html += '<p class="sub-txt">*Kinerja masa lalu tidak mencerminkan kinerja masa depan</p>'
			        html += '</div>'
			        html += '</div>';
			    $(id).append(html);

			    $('#div_product').css('display', 'block');	// div area show
			}
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getProducts`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(id).empty();
					if(res && res.length > 0){
						var mainProduct = res[0];
					    var logoUrl = mainProduct.LOGO_IMG_URL
					        ? "${pageContext.request.contextPath}/kid/upload/product/" + mainProduct.LOGO_IMG_URL
					        : "${pageContext.request.contextPath}/kid/images/common/logo_default.png";
					    var mainProductNm = mainProduct.NM;
					    var mainRepTitl = mainProductNm.length > 25
					          ? mainProductNm.substring(0, 25) + "<br/>" + mainProductNm.substring(25)
					          : mainProductNm;
					    var rtnRt12 = mainProduct.RTN_RT_12 == 0 ? "-" : mainProduct.RTN_RT_12 + "%";
				    	var aum = mainProduct.AUM == 0? "-" : mainProduct.AUM;
				    	if(aum != "-"){
				    		aum = aum.toLocaleString();
				    	}
					    var score = mainProduct.SCORE * 20;
					    var html = '<div class="product-item-01">'
					       	html += '<div class="top">'
					        html += '<div class="logo">'
					        html += '<img src="'+logoUrl+'" alt="LOGO">'
					        html += '</div>'
					        html += '<h4>'+mainRepTitl+'</h4>'
					        html += '<br/>'
					        html += '</div>'
					        html += '<div class="mid">'
					        html += '<ul>'
					        html += '<li>'
					        html += '<p>1 Tahun terakhir*</p>'
					        html += '<div>'+rtnRt12+'</div>'
					        html += '</li>'
					        html += '<li>'
					        html += '<p>AUM (Miliyar)</p>'
					        html += '<div>'
					        html += '<em>Rp</em>'
					        html += aum
					        html += '</div>'
					        html += ' </li>'
					        html += '</ul>'
					        html += '</div>'
					        html += '<div class="bottom">'
					        html += '<div class="star-aria">'
					        html += '<div class="star-range">'
					        html += ' <span style="width: '+score+'%;"></span>'
					        html += '</div>'
					        html += '<p>Rating bintang di dapat dari ulasan pembeli.</p>'
					        html += '</div>'
					        html += '<p class="sub-txt">*Kinerja masa lalu tidak mencerminkan kinerja masa depan</p>'
					        html += '</div>'
					        html += '</div>';
					    $(id).append(html);

					    $('#div_product').css('display', 'block');	// div area show
					}
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}

	function initProductRight(id) {
		const url = `${pageContext.request.contextPath}/kid/upload/product/json/PRDMain.json?${ver}`;
		$.getJSON(url, function(res) {
			$(id).empty();
			for (let i = 1; i < res.length; i++){
		    	var product = res[i];
		    	var logoUrl = product.LOGO_IMG_URL
		        ? "${pageContext.request.contextPath}/kid/upload/product/" + product.LOGO_IMG_URL
		        : "${pageContext.request.contextPath}/kid/images/common/logo_default.png";
		    	var productNm = product.NM;
		    	var mainRepTitl = productNm.length > 25
		          ? productNm.substring(0, 25) + "<br/>" + productNm.substring(25)
		          : productNm;
		    	var rtnRt12 = product.RTN_RT_12 == 0 ? "-" : product.RTN_RT_12 + "%";
		    	var aum = product.AUM == 0? "-" : product.AUM;
		    	if(aum != "-"){
		    		aum = aum.toLocaleString();
		    	}
		    	var score = product.SCORE * 20;
		    	let li = '<li class="item swiper-slide">'
		    		li += '<div class="top">'
		    		li += '<div class="logo">'
		    		li += '<img src="'+logoUrl+'" alt="LOGO">'
		    		li += '</div>'
		    		li += '<h4>'+mainRepTitl+'</h4>'
		    		li += '<br/>'
		    		li += '</div>'
		    		li += '<div class="mid">'
		    		li += '<ul>'
		    		li += '<li>'
		    		li += '<p>1 Tahun terakhir*</p>'
		    		li += '<div>'+rtnRt12+'</div>'
		    		li += '</li>'
		    		li += '<li>'
		    		li += '<p>AUM (Miliyar)</p>'
		    		li += '<div>'
		    		li += '<em>Rp</em>'
		    		li += aum
		    		li += '</div>'
		    		li += ' </li>'
		    		li += '</ul>'
		    		li += '</div>'
		    		li += '<div class="bottom">'
		    		li += '<div class="star-aria">'
		    		li += '<div class="star-range">'
					li += ' <span style="width: '+score+'%;"></span>'
					li += '</div>'
					li += '<p>Rating bintang di dapat dari ulasan pembeli.</p>'
					li += '</div>'
					li += '<p class="sub-txt">*Kinerja masa lalu tidak mencerminkan kinerja masa depan</p>'
					li += '</div>'
					li += '</li>'
				$(id).append(li);
		    }
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getProducts`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(id).empty();
					for (let i = 1; i < res.length; i++){
				    	var product = res[i];
				    	var logoUrl = product.LOGO_IMG_URL
				        ? "${pageContext.request.contextPath}/kid/upload/product/" + product.LOGO_IMG_URL
				        : "${pageContext.request.contextPath}/kid/images/common/logo_default.png";
				    	var productNm = product.NM;
				    	var mainRepTitl = productNm.length > 25
				          ? productNm.substring(0, 25) + "<br/>" + productNm.substring(25)
				          : productNm;
				    	var rtnRt12 = product.RTN_RT_12 == 0 ? "-" : product.RTN_RT_12 + "%";
				    	var aum = product.AUM == 0? "-" : product.AUM;
				    	if(aum != "-"){
				    		aum = aum.toLocaleString();
				    	}
				    	var score = product.SCORE * 20;
				    	let li = '<li class="item swiper-slide">'
				    		li += '<div class="top">'
				    		li += '<div class="logo">'
				    		li += '<img src="'+logoUrl+'" alt="LOGO">'
				    		li += '</div>'
				    		li += '<h4>'+mainRepTitl+'</h4>'
				    		li += '<br/>'
				    		li += '</div>'
				    		li += '<div class="mid">'
				    		li += '<ul>'
				    		li += '<li>'
				    		li += '<p>1 Tahun terakhir*</p>'
				    		li += '<div>'+rtnRt12+'</div>'
				    		li += '</li>'
				    		li += '<li>'
				    		li += '<p>AUM (Miliyar)</p>'
				    		li += '<div>'
				    		li += '<em>Rp</em>'
				    		li += aum
				    		li += '</div>'
				    		li += ' </li>'
				    		li += '</ul>'
				    		li += '</div>'
				    		li += '<div class="bottom">'
				    		li += '<div class="star-aria">'
				    		li += '<div class="star-range">'
							li += ' <span style="width: '+score+'%;"></span>'
							li += '</div>'
							li += '<p>Rating bintang di dapat dari ulasan pembeli.</p>'
							li += '</div>'
							li += '<p class="sub-txt">*Kinerja masa lalu tidak mencerminkan kinerja masa depan</p>'
							li += '</div>'
							li += '</li>'
						$(id).append(li);
				    }
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}

	function initMarketReport(id) {
		const url = `${pageContext.request.contextPath}/kid/upload/marketreport/json/MarketReportJSON.json?${ver}`;
		$.getJSON(url, function(res) {
			$(id).empty();
			var listLength = res.length >= 3 ? 3 : res.length;
			for (let i = 0; i < listLength; i++){
		    	var mr = res[i];
		    	var title = mr.TITL;
		    	var makeDate = mr.MAKEDATE5;
		    	var seqNo = mr.SEQNO;
		    	const isNew = !isMoreThanOneDayAhead(mr.MAKEDATE3);
		    	let li = '<li>'
		    		li += '<a href="${pageContext.request.contextPath}/market/detailMarketReportMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="MarketReportBtn'+ (i+1) + '">'
		    		li += '<div class="frist">'
		    		li += '<p>'+title+'</p>'
		    	if(isNew == true){
		    		li += '<span>NEW</span>'
		    	}
		    		li += '</div>'
		    		li += '<div class="second">'
		    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
		    		li += '</div>'
		    		li += '</a>'
		    		li += '</li>'
				$(id).append(li);
		    }
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getMarketReportTop3`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(id).empty();
					for (let i = 0; i < res.length; i++){
				    	var mr = res[i];
				    	var title = mr.TITL;
				    	var makeDate = mr.MAKEDATE5;
				    	var seqNo = mr.SEQNO;
				    	const isNew = mr.IS_NEW;
				    	let li = '<li>'
				    		li += '<a href="${pageContext.request.contextPath}/market/detailMarketReportMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="MarketReportBtn'+ (i+1) + '">'
				    		li += '<div class="frist">'
				    		li += '<p>'+title+'</p>'
				    	if(isNew == 1){
				    		li += '<span>NEW</span>'
				    	}
				    		li += '</div>'
				    		li += '<div class="second">'
				    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
				    		li += '</div>'
				    		li += '</a>'
				    		li += '</li>'
						$(id).append(li);
				    }
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}

	function initDailyNews(id) {
		const url = `${pageContext.request.contextPath}/kid/upload/dailynews/json/DailyNewsJSON.json?${ver}`;
		$.getJSON(url, function(res) {
			$(id).empty();
			var listLength = res.length >= 3 ? 3 : res.length;
			for (let i = 0; i < listLength; i++){
		    	var dn = res[i];
		    	var title = dn.TITL;
		    	var makeDate = dn.MAKEDATE5;
		    	var seqNo = dn.SEQNO;
		    	const isNew = !isMoreThanOneDayAhead(dn.MAKEDATE3);
		    	let li = '<li>'
		    		li += '<a href="${pageContext.request.contextPath}/dailynews/detailDailyNewsMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="DailyNewsBtn'+ (i+1) + '">'
		    		li += '<div class="frist">'
		    		li += '<p>'+title+'</p>'
		    	if(isNew == true){
		    		li += '<span>NEW</span>'
		    	}
		    		li += '</div>'
		    		li += '<div class="second">'
		    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
		    		li += '</div>'
		    		li += '</a>'
		    		li += '</li>'
				$(id).append(li);
		    }
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getDailyNewsTop3`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(id).empty();
					for (let i = 0; i < res.length; i++){
				    	var dn = res[i];
				    	var title = dn.TITL;
				    	var makeDate = dn.MAKEDATE5;
				    	var seqNo = dn.SEQNO;
				    	const isNew = dn.IS_NEW;
				    	let li = '<li>'
				    		li += '<a href="${pageContext.request.contextPath}/dailynews/detailDailyNewsMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="DailyNewsBtn'+ (i+1) + '">'
				    		li += '<div class="frist">'
				    		li += '<p>'+title+'</p>'
				    	if(isNew == 1){
				    		li += '<span>NEW</span>'
				    	}
				    		li += '</div>'
				    		li += '<div class="second">'
				    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
				    		li += '</div>'
				    		li += '</a>'
				    		li += '</li>'
						$(id).append(li);
				    }
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}

	function initIpoNews(id) {
		const url = `${pageContext.request.contextPath}/kid/upload/iponews/json/IPONewsJSON.json?${ver}`;
		$.getJSON(url, function(res) {
			$(id).empty();
			var listLength = res.length >= 3 ? 3 : res.length;
			for (let i = 0; i < listLength; i++){
		    	var ipon = res[i];
		    	var title = ipon.TITL;
		    	var makeDate = ipon.MAKEDATE5;
		    	var seqNo = ipon.SEQNO;
		    	const isNew = !isMoreThanOneDayAhead(ipon.MAKEDATE3);
		    	let li = '<li>'
		    		li += '<a href="${pageContext.request.contextPath}/iponews/detailIpoNewsMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="IpoNewsBtn'+ (i+1) + '">'
		    		li += '<div class="frist">'
		    		li += '<p>'+title+'</p>'
		    	if(isNew == true){
		    		li += '<span>NEW</span>'
		    	}
		    		li += '</div>'
		    		li += '<div class="second">'
		    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
		    		li += '</div>'
		    		li += '</a>'
		    		li += '</li>'
				$(id).append(li);
		    }
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getIpoNewsTop3`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(id).empty();
					for (let i = 0; i < res.length; i++){
				    	var ipon = res[i];
				    	var title = ipon.TITL;
				    	var makeDate = ipon.MAKEDATE5;
				    	var seqNo = ipon.SEQNO;
				    	const isNew = ipon.IS_NEW;
				    	let li = '<li>'
				    		li += '<a href="${pageContext.request.contextPath}/iponews/detailIpoNewsMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="IpoNewsBtn'+ (i+1) + '">'
				    		li += '<div class="frist">'
				    		li += '<p>'+title+'</p>'
				    	if(isNew == 1){
				    		li += '<span>NEW</span>'
				    	}
				    		li += '</div>'
				    		li += '<div class="second">'
				    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
				    		li += '</div>'
				    		li += '</a>'
				    		li += '</li>'
						$(id).append(li);
				    }
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}

	function initInternationalNews(id) {
		const url = `${pageContext.request.contextPath}/kid/upload/internationalnews/json/InternationalNewsJSON.json?${ver}`;
		$.getJSON(url, function(res) {
			$(id).empty();
			var listLength = res.length >= 3 ? 3 : res.length;
			for (let i = 0; i < listLength; i++){
		    	var intern = res[i];
		    	var title = intern.TITL;
		    	var makeDate = intern.MAKEDATE5;
		    	var seqNo = intern.SEQNO;
		    	const isNew = !isMoreThanOneDayAhead(intern.MAKEDATE3);
		    	let li = '<li>'
		    		li += '<a href="${pageContext.request.contextPath}/internationalnews/detailInternationalNewsMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="InternationalNewsBtn'+ (i+1) + '">'
		    		li += '<div class="frist">'
		    		li += '<p>'+title+'</p>'
		    	if(isNew == true){
		    		li += '<span>NEW</span>'
		    	}
		    		li += '</div>'
		    		li += '<div class="second">'
		    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
		    		li += '</div>'
		    		li += '</a>'
		    		li += '</li>'
				$(id).append(li);
		    }
		})
		.fail(function(){
			// Called only if json file cannnot be read
			const url = `${pageContext.request.contextPath}/main/getInternationalNewsTop3`;
			var jsonObj = {
				type : 'POST',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					$(id).empty();
					for (let i = 0; i < res.length; i++){
				    	var intern = res[i];
				    	var title = intern.TITL;
				    	var makeDate = intern.MAKEDATE5;
				    	var seqNo = intern.SEQNO;
				    	const isNew = intern.IS_NEW;
				    	let li = '<li>'
				    		li += '<a href="${pageContext.request.contextPath}/internationalnews/detailInternationalNewsMain?id='+seqNo+'" data-gtm-grp="Main" data-gtm-id="InternationalNewsBtn'+ (i+1) + '">'
				    		li += '<div class="frist">'
				    		li += '<p>'+title+'</p>'
				    	if(isNew == 1){
				    		li += '<span>NEW</span>'
				    	}
				    		li += '</div>'
				    		li += '<div class="second">'
				    		li += '<span>'+makeDate+'</span> <em>No. '+seqNo+'</em>'
				    		li += '</div>'
				    		li += '</a>'
				    		li += '</li>'
						$(id).append(li);
				    }
				},
				error: function(request, status, error) {
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		});
	}
	function initCallButton() {
		let _btnCall1 = $("button[name='btn_call1']");
		let _btnCall2 = $("button[name='btn_call2']");

		if(global.device == '1'){	// mobile
			_btnCall1.attr('disabled', false);
			_btnCall2.attr('disabled', false);

			_btnCall1.on('click', ()=> goTel('02127085695'));
			_btnCall2.on('click', ()=> goTel('02127085877'));
		}else {
			_btnCall1.attr('disabled', true);
			_btnCall2.attr('disabled', true);
		}
	}
	function initNoticePopup() {
		let popupYn = '${popupYn}';
		if(popupYn == 'Y'){
			if(kidCommon.getCookie("todayCloseYn") != 'Y'){
				$("#div_popup").addClass("block");
			}
		}
	}
	function isMoreThanOneDayAhead(targetDateStr){
		// Parse the target date using moment
		const targetDate = moment(targetDateStr, 'DD/MM/YYYY HH:mm:ss');
		// Set the desired UTC offset to UTC+7 (Bangkok, Thailand)
		const desiredUtcOffset = 7;
		// Get the current date in the local timezone
		const currentDateLocal = moment();
		// Get the UTC offset in hours for the local timezone
		const currentUtcOffset = currentDateLocal.utcOffset() / 60;
		// Calculate the difference in hours between local timezone and UTC+7
		const offsetDifference = desiredUtcOffset - currentUtcOffset;
		// Convert the current date to the UTC+7 timezone
		const currentDateUtcPlus7 = currentDateLocal.add(offsetDifference, 'hours');
		// Calculate the difference in days
		const differenceInDays = currentDateUtcPlus7.diff(targetDate, 'days');
		// Check if the difference is greater than 1
		return differenceInDays >= 1;
	}
	function initSwiperMain(){
		swiperMain();
		var cachedWidth = $(window).width();
		window.addEventListener('resize', function(){
           toggle.init('.aboutUs-main','mo');
           var newWidth = $(window).width();
           if(newWidth !== cachedWidth){
               cachedWidth = $(window).width();
               swiperMain();
           }
       });
	}
	function initSwiperResearch(){
		swiperResearch();
		var cachedWidth = $(window).width();
		window.addEventListener('resize', function() {
			var newWidth = $(window).width();
           	if(newWidth !== cachedWidth){
            	cachedWidth = $(window).width();
               	productSwich();
           	}
		});
	}
	function initButton(){
		let _btnOpenAccount = $("button[name='btn_open_account']");
		_btnOpenAccount.on('click', ()=> kidCommon.goOpenAccount());

		let _btnPopClose = $("button[id='btnPopClose']");
		_btnPopClose.on('click', ()=> closePopup());
	}
	function productSwich() {
		if (global.mod == 'mo' && productSwiper == undefined) {
			productSwiper = new Swiper(".product-swiper", {
				spaceBetween : 0,
				pagination : {
					el : '.product-swiper-pagination',
					type : 'bullets',
				},
			});
		} else if (global.mod == 'pc' && productSwiper != undefined) {
			productSwiper.destroy();
			productSwiper = undefined;
		}
	}
	//RESEARCH
	function swiperResearch() {
		var researchSwiper = new Swiper(".research-swiper", {
			slidesPerView : 1,

			delay : 4000,
			speed : 300,
			autoplay : true,
			breakpoints : {
				360 : {
					spaceBetween : 50,
				},
				720 : {
					spaceBetween : 10,
				},
			},
			pagination : {
				el : '.research-swiper-pagination',
				type : 'bullets',
			},
		});
	}

	function swiperMain() {
		let _pcSwiper = $('.main-banner-pc'), _moSwiper = $('.main-banner-mo');

        _totalPc = $('.main-banner-pc').find('li').length;
        _totalMo = $('.main-banner-mo').find('li').length;
        _totalPc <= 1 ?   $('.swiper-main-sub-pc').find('.count').hide():$('.current-num').text(1);
        _totalMo <= 1 ?   $('.swiper-main-sub-mo').find('.count').hide():$('.current-num').text(1);

		if (global.mod == 'mo') {
			_pcSwiper.css('display','none');
            _moSwiper.css('display','block');
			if (_mainSwiperMO != undefined) {
			     _mainSwiperMO.destroy();
			     _mainSwiperMO =undefined;
			}


			_mainSwiperMO = new Swiper(".main-banner-mo", {
				slidesPerView: 1,
                spaceBetween: 0,

                observer: true,
                observeParents: true,
                autoplayDisableOnInteraction: false,
                touchMoveStopPropagation:true,
                touchRatio: 1,
                loop: true,
                autoplay: {
                    delay: 4000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: "#main-swiper-pagination-mo",
                    type: "progressbar",
                },
                on: {
                    activeIndexChange: function() {
                        const cIdx = this.realIndex + 1;
                        $('.current-num').text(cIdx);
                    },
                },
			});
		} else if (global.mod == 'pc') {
			_pcSwiper.css('display', 'block');
			_moSwiper.css('display', 'none');
			cIdx_pc = 0;
            if (_mainSwiperPC != undefined) {
                _mainSwiperPC.destroy();
                _mainSwiperPC =undefined;
            }

			_mainSwiperPC = new Swiper(".main-banner-pc", {
				slidesPerView: 1,
                spaceBetween: 10,
                touchRatio: 0,
                loop: true,

                autoplayDisableOnInteraction: false,
                touchMoveStopPropagation:true,
                autoplay: {
                    delay: 4000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: "#main-swiper-pagination-pc",
                    type: "progressbar",
                },
                navigation: {
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                },
                on: {
                    activeIndexChange: function() {
                        const cIdx_pc = this.realIndex + 1;
                        $('.current-num-pc').text(cIdx_pc);
                    },
                },
			});
		}
	}

	const goTel = (telno) => location.href = 'tel:' + telno;

	function closePopup() {
		if($("#todayCloseYn").prop("checked")){
			kidCommon.setCookie('todayCloseYn', 'Y', 1);
		}

		$("#div_popup").removeClass("block");
	}

	function adjustScrollableTableWidth() {
		const scrollableTable = document.querySelector('.scrollable-table');
		const productTable = document.querySelector('#productTable');
		if (scrollableTable) {
			// Check if there is a vertical scrollbar
			const hasVerticalScrollbar = scrollableTable.scrollHeight > scrollableTable.clientHeight;
			// Adjust width based on scrollbar presence
			scrollableTable.style.width = hasVerticalScrollbar ? '98%' : '100%';
			productTable.style.width = hasVerticalScrollbar ? '100%' : '100%';
		}
	}
	// Optionally call it on window resize or dynamically added content
	window.addEventListener('resize', adjustScrollableTableWidth);


</script>
<main>
	<div class="main-banner">
		<div class="main-banner-pc" style="<c:if test="${isDevice != 'PC'}">display: none</c:if>">
			<ul class="swiper-wrapper" id="topBanners">
				<c:if test="${mainTopFirstImgUrl != ''}">
					<li class="swiper-slide">
	                    <a class="banner-01" style="background-image: url('${pageContext.request.contextPath}/kid/upload/banner/${mainTopFirstImgUrl}');"></a>
	                </li>
				</c:if>
			</ul>
			<div class="swiper-main-sub-pc">
				<div class="swiper-pagination" id="main-swiper-pagination-pc"></div>
				<div class="count" id="main-swiper-bullets-pc">
					<span class="current-num-pc"></span>
                    <span class="all-num"></span>
				</div>
				<div class="swiper-move-btn-area">
					<div class="swiper-button-prev" id="main-swiper-prev"></div>
					<div class="swiper-button-next" id="main-swiper-next"></div>
				</div>
			</div>
		</div>
		<div class="main-banner-mo" style="<c:if test="${isDevice != 'MOBI'}">display: none; -webkit-overflow-scrolling: touch;</c:if>">
			<ul class="swiper-wrapper" id="mobileTopBanner">
				<c:if test="${mainTopFirstMobileImgUrl != ''}">
					<li class="swiper-slide">
	                    <a><img src="${pageContext.request.contextPath}/kid/upload/banner/${mainTopFirstMobileImgUrl}"></a>
	                </li>
	            </c:if>
			</ul>
			<div class="swiper-main-sub-mo">
                <div class="swiper-pagination " id="main-swiper-pagination-mo"></div>
                <div class="count">
                	<span class="current-num"></span>
                	<span class="all-num"></span>
                </div>
			</div>
		</div>
		<div class="world-index">
        	<div class="wrap">
            	<p class="title">World Index</p>
                <ul>
                	<c:forEach items="${worldIndexs}" var="worldIndexs">
                		<c:choose>
                			<c:when test="${worldIndexs.FID_25 eq '2'}">
                				<c:set var="symbol" value="up" />
                			</c:when>
                			<c:when test="${worldIndexs.FID_25 eq '5'}">
                				<c:set var="symbol" value="down" />
                			</c:when>
                			<c:when test="${worldIndexs.FID_25 eq '+'}">
                				<c:set var="symbol" value="up" />
                			</c:when>
                			<c:when test="${worldIndexs.FID_25 eq '-'}">
                				<c:set var="symbol" value="down" />
                			</c:when>
                			<c:when test="${worldIndexs.FID_25 eq '3'}">
                				<c:set var="symbol" value="" />
                			</c:when>
                			<c:otherwise>
                				<c:set var="symbol" value="" />
                			</c:otherwise>
                		</c:choose>
                		<li class="${symbol}">
                			<span>${worldIndexs.FID_302}</span>
	                        <span>${worldIndexs.FID_10}</span>
	                        <span>${worldIndexs.FID_12}</span>
                		</li>
	                </c:forEach>
                </ul>
            </div>
        </div>
	</div>
	<div class="contents-wrap lang-en <c:if test="${language != '1'}">active</c:if>">
		<div class="content-tit">
			Opening Account
			<p>Online opening account is easy, fast and secure</p>
		</div>
		<div class="OpenAccount-main">
			<span class="arrow-01 lazy"></span>
			<span class="arrow-02 lazy"></span>
			<span class="arrow-03 lazy"></span>
			<ol class="openAccountStep">
				<li>
					<div class="top">
						<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_01.svg" alt="">
					</div>
					<div class="bottom">
						<span>1</span>
						<h4>Fill in forms</h4>
						<p>Provide sufficient information in your documents</p>
					</div>
				</li>
				<li>
					<div class="top">
						<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_02.svg" alt="">
					</div>
					<div class="bottom">
						<span>2</span>
						<h4>Complete documents</h4>
						<p>Complete required supporting documents</p>
					</div>
				</li>
				<li>
					<div class="top">
						<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_03.svg" alt="">
					</div>
					<div class="bottom">
						<span>3</span>
						<h4>Documents verification</h4>
						<p>Review profiles and verify your account</p>
					</div>
				</li>
				<li>
					<div class="top">
						<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_04.svg" alt="">
					</div>
					<div class="bottom">
						<span>4</span>
						<h4>Confirmed email</h4>
						<p>Receive an account details information email</p>
					</div>
				</li>
			</ol>
		</div>
		<div class="openAccount-bottom">
			<button name="btn_open_account" class="btn btn-blue" data-gtm-grp="Main" data-gtm-id="OpenAccountBtn">Open Account Online</button>
			<div class="openAccount-bottom-info">
				<p>To open an account offline, institutional client, and margin account, please contact:</p>
				<ul class="information-card">
					<li class="wrap lazy">
						<h5>Customer Service</h5>
						<ol>
							<li>
								<strong>Mail:</strong> <span><a href="mailto:<EMAIL>"><EMAIL></a></span>
							</li>
							<li>
								<strong>Tel:</strong> <span><button name="btn_call1">(021) 2708 5695 - 5696</button></span>
							</li>
						</ol>
					</li>
					<li class="wrap lazy">
						<h5>Korean Desk</h5>
						<ol>
							<li>
								<strong>Mail:</strong> <span><a href="mailto:<EMAIL>"><EMAIL></a></span>
							</li>
							<li>
								<strong>Tel:</strong> <span><button name="btn_call2">(021) 2708 5877</button></span>
							</li>
						</ol>
					</li>
				</ul>
			</div>
		</div>
	</div>
	<div class="contents-wrap lang-id <c:if test="${language == '1'}">active</c:if>">
	    <div class="content-tit">
	        Pembukaan Rekening
	        <p>Pembukaan rekening online itu mudah, cepat dan aman</p>
	    </div>
	    <div class="OpenAccount-main">
	        <span class="arrow-01 lazy"></span>
	        <span class="arrow-02 lazy"></span>
	        <span class="arrow-03 lazy"></span>
	        <ol class="openAccountStep">
	            <li>
	                <div class="top">
	                    <img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_01.svg" alt="">
	                </div>
	                <div class="bottom">
	                    <span>1</span>
	                    <h4>Pengisian formulir</h4>
	                    <p>Berikan informasi yang cukup dalam dokumen Anda</p>
	                </div>
	            </li>
	            <li>
	                <div class="top">
	                    <img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_02.svg" alt="">
	                </div>
	                <div class="bottom">
	                    <span>2</span>
	                    <h4>Melengkapi dokumen pendukung</h4>
	                    <p>Lengkapi dokumen pendukung yang diperlukan</p>
	                </div>
	            </li>
	            <li>
	                <div class="top">
	                    <img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_03.svg" alt="">
	                </div>
	                <div class="bottom">
	                    <span>3</span>
	                    <h4>Verifikasi dokumen</h4>
	                    <p>Tinjau profil dan verifikasi akun Anda</p>
	                </div>
	            </li>
	            <li>
	                <div class="top">
	                    <img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_openAccount_main_04.svg" alt="">
	                </div>
	                <div class="bottom">
	                    <span>4</span>
	                    <h4>Menerima E-mail informasi akun</h4>
	                    <p>Tinjau profil dan verifikasi akun Anda</p>
	                </div>
	            </li>
	        </ol>
	    </div>
	    <div class="openAccount-bottom">
	        <button name="btn_open_account" class="btn btn-blue" data-gtm-grp="Main" data-gtm-id="OpenAccountBtn">Buka Akun Daring</button>
	        <div class="openAccount-bottom-info">
	            <p>Pembukaan rekening secara offline, nasabah institusi dan akun margin dapat menghubungi</p>
	            <ul class="information-card">
	                <li class="wrap lazy">
	                    <h5>Pelayanan Pelanggan</h5>
	                    <ol>
	                        <li>
	                            <strong>Surat:</strong>
	                            <span><a href="mailto:<EMAIL>"><EMAIL></a></span>
	                        </li>
	                        <li>
	                            <strong>Tel:</strong>
	                            <span><button name="btn_call1">(021) 2708 5695 - 5696</button></span>
	                        </li>
	                    </ol>
	                </li>
	                <li class="wrap lazy">
	                    <h5>Meja Korea</h5>
	                    <ol>
	                        <li>
	                            <strong>Surat:</strong>
	                            <span><a href="mailto:<EMAIL>"><EMAIL></a></span>
	                        </li>
	                        <li>
	                            <strong>Tel:</strong>
	                            <span><button name="btn_call2">(021) 2708 5877</button></span>
	                        </li>
	                    </ol>
	                </li>
	            </ul>
	        </div>
	    </div>
	</div>

	<div class="contents-blue">
		<div class="contents-wrap reseach-main">
			<div class="content-tit reseach-tit">Research</div>
			<section class="top-area">
				<section class="research-main-contents tab-target-list">
					<div class="tabTarget on" id="tab1">
						<a href="${pageContext.request.contextPath}/market/getMarketReportMain" class="title lazy" data-gtm-grp="Main" data-gtm-id="MarketReportBtn">
							<h4>MARKET REPORT</h4>
							<span></span>
						</a>
						<div class="research-main-list">
							<ul id="marketReport">
							</ul>
						</div>
					</div>
					<div class="tabTarget" id="tab2">
						<a href="${pageContext.request.contextPath}/dailynews/getDailyNewsMain" class="title lazy" data-gtm-grp="Main" data-gtm-id="DailyNewsBtn">
							<h4>DAILY NEWS</h4>
							<span></span>
						</a>
						<div class="research-main-list">
							<ul id="dailyNews">
							</ul>
						</div>
					</div>
					<div class="tabTarget" id="tab3">
						<a href="${pageContext.request.contextPath}/iponews/getIpoNewsMain" class="title lazy" data-gtm-grp="Main" data-gtm-id="IpoNewsBtn">
							<h4>IPO NEWS</h4>
							<span></span>
						</a>
						<div class="research-main-list">
							<ul id="ipoNews">
							</ul>
						</div>
					</div>
					<div class="tabTarget" id="tab4">
						<a href="${pageContext.request.contextPath}/internationalnews/getInternationalNewsMain" class="title lazy" data-gtm-grp="Main" data-gtm-id="InternationalNewsBtn">
							<h4>INTERNATIONAL NEWS</h4>
							<span></span>
						</a>
						<div class="research-main-list">
							<ul id="internationalNews">
							</ul>
						</div>
					</div>
				</section>
				<div class="research-swiper">
					<ul class="swiper-wrapper" id="researchBanners">
					</ul>
					<div class="swiper-pagination research-swiper-pagination"></div>
				</div>
			</section>
			<section class="bottom-area">
				<ul class="tab-triger-list">
					<li class="on">
						<a class="lazy" href="javascript:void(0)" data-tab="0" data-gtm-grp="Main" data-gtm-id="MarketReportTab"> Market Report </a>
					</li>
					<li>
						<a class="lazy" href="javascript:void(0)" data-tab="1" data-gtm-grp="Main" data-gtm-id="DailyNewsTab"> Daily News </a>
					</li>
					<li>
						<a class="lazy" href="javascript:void(0)" data-tab="2" data-gtm-grp="Main" data-gtm-id="IpoNewsTab"> IPO News </a>
					</li>
					<li>
						<a class="lazy" href="javascript:void(0)" data-tab="3" data-gtm-grp="Main" data-gtm-id="InternationalNewsTab"> International News </a>
					</li>
				</ul>
			</section>
		</div>
	</div>
	<div id="div_product" class="contents-gray" style="display:none">
		<div class="contents-wrap">
			<div class="content-tit product-tit">
				<span class="lang-en <c:if test="${language != '1'}">active</c:if>">Mutual Fund</span>
				<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">Reksa Dana</span>
				</div>

			<div class="product-header">
				<div class="switch-button-active" onclick="toggleSwitchButton()">
					<img id="switchButtonImg" src="${pageContext.request.contextPath}/kid/images/common/btn_switch_active.svg"
						 alt="Switch Button"
						 style="width: 100%; height: 100%; object-fit: contain;">
				</div>
			</div>
			<div id="productCards" class="product-wrap-main">
				<div class="left" id="mainProduct">
				</div>
				<div class="right product-swiper swiper">
					<ul class="items-wrap swiper-wrapper" id="products">
					</ul>
				</div>
				<div class="swiper-pagination product-swiper-pagination"></div>
			</div>
			<div id="tableWrapper" class="scrollable-table">
				<div id="productTable" style="display:none">
					<table>
						<thead>
						<tr>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">No</span>
								<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">No</span>
							</th>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">Product</span>
								<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">Produk Reksadana</span>
							</th>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">1 Year Return (%)</span>
								<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">1 Year Return (%)</span>
							</th>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">3 Year Return (%)</span>
								<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">3 Year Return (%)</span>
							</th>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">5 Year Return (%)</span>
								<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">5 Year Return (%)</span>
							</th>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">AUM (Miliar)</span>
								<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">AUM (Miliar)</span>
							</th>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">Subscription Form</span>
								<span class="lang-id  <c:if test="${language == '1'}">active</c:if>">Formulir Pembelian</span>
							</th>
							<th>
								<span class="lang-en <c:if test="${language != '1'}">active</c:if>">Redemption Form</span>
								<span class="lang-id <c:if test="${language == '1'}">active</c:if>">Formulir Penjualan</span>
							</th>
						</tr>
						</thead>
						<tbody>

						</tbody>
					</table>
				</div>
			</div>

		</div>
	</div>
	<div class="contents-wrap lang-en <c:if test="${language != '1'}">active</c:if>">
		<div class="content-tit aboutUs-top">
			About Us
			<p>The most trusted investment partner in Indonesia</p>
		</div>
		<div class="aboutUs-main">
			<div class="aboutUs-main-bg">
				<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/common/bg_aboutUs_main.png" alt="">
			</div>
			<ul>
				<li>
					<div class="top icon1 lazy"></div>
					<div class="bottom">
						<h4>Customer-centric</h4>
					</div>
					<p class="sub-txt">Prioritizing customer in our decision making</p>
				</li>
				<li>
					<div class="top icon2 lazy"></div>
					<div class="bottom">
						<h4>Professional</h4>
					</div>
					<p class="sub-txt">Constantly striving to improve our competencies</p>
				</li>
				<li>
					<div class="top icon3 lazy"></div>
					<div class="bottom">
						<h4>Trust & Integrity</h4>
					</div>
					<p class="sub-txt">Adhere to the highest standards of ethical conduct, acting in good faith and with integrity</p>
				</li>
				<li>
					<div class="top icon4 lazy"></div>
					<div class="bottom">
						<h4>Mutual Growth</h4>
					</div>
					<p class="sub-txt">Contribute to the development of society by growing together with Indonesia capital market</p>
				</li>
			</ul>
		</div>
	</div>
	<div class="contents-wrap lang-id <c:if test="${language == '1'}">active</c:if>">
	    <div class="content-tit aboutUs-top">
	        Tentang Kami
	        <p>Mitra investasi paling terpercaya di Indonesia</p>
	    </div>
	    <div class="aboutUs-main">
	        <div class="aboutUs-main-bg">
	        	<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/common/bg_aboutUs_main.png" alt="">
	        </div>
	        <ul>
	            <li>
	                <div class="top icon1 lazy"></div>
	                <div class="bottom">
	                    <h4>Terpusat pada pelanggan</h4>

	                </div>
	                <p class="sub-txt">
	                    Memprioritaskan pelanggan dalam pengambilan keputusan kami
	                </p>
	            </li>
	            <li>
	                <div class="top icon2 lazy"></div>
	                <div class="bottom">
	                    <h4>Profesional</h4>

	                </div>
	                <p class="sub-txt">
	                    Terus berusaha untuk meningkatkan kompetensi kita
	                </p>
	            </li>
	            <li>
	                <div class="top icon3 lazy"></div>
	                <div class="bottom">
	                    <h4>Kepercayaan & Integritas</h4>

	                </div>
	                <p class="sub-txt">
	                    Patuhi standar perilaku etis tertinggi, bertindak dengan
	                    itikad baik dan dengan integritas
	                </p>
	            </li>
	            <li>
	                <div class="top icon4 lazy"></div>
	                <div class="bottom">
	                    <h4>Pertumbuhan Bersama</h4>
	                </div>
	                <p class="sub-txt">
	                    Berkontribusi pada perkembangan masyarakat dengan
	                    tumbuh bersama pasar modal Indonesia
	                </p>
	            </li>
	        </ul>
	    </div>
	</div>
	<div class="main-affiliated">
		<ul>
			<li>
				<a href="https://www.ojk.go.id/id/Default.aspx" target="_blank">
					<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_affiliated_01.svg" alt="ojk">
				</a>
			</li>
			<li>
				<a href="https://www.idx.co.id/id" target="_blank">
					<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_affiliated_02.svg" alt="idx">
				</a>
			</li>
			<li>
				<a href="https://www.idclear.co.id/id" target="_blank">
					<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_affiliated_03.svg" alt="idclear">
				</a>
			</li>
			<li>
				<a href="https://www.ksei.co.id/" target="_blank">
					<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_affiliated_04.svg" alt="ksei">
				</a>
			</li>
			<li>
				<a href="https://www.indonesiasipf.co.id/" target="_blank">
					<img class="lazy" data-src="${pageContext.request.contextPath}/kid/images/icon/icon_affiliated_05.svg" alt="indonesiasipf">
				</a>
			</li>
		</ul>
	</div>
</main>
<div id="div_popup" class="img-pop">
    <div class="img-pop-con">
        <c:if test="${not empty popupImgLink}"><a href="${popupImgLink}" target="_blank"></c:if><c:if test="${not empty popupImgUrl}"><img src="${pageContext.request.contextPath}/kid/upload/popup/${popupImgUrl}" alt="" /></c:if><c:if test="${not empty popupImgLink}"></a></c:if>
    </div>
    <div class="img-pop-bottom">
        <div>
            <span class="inp-check">
                <input type="checkbox" id="todayCloseYn">
                <label for="todayCloseYn">
                Do not open<br>
                this window for a day
                </label>
            </span>
        </div>
        <div class="close">
            <button id="btnPopClose">Close</button>
        </div>
    </div>
</div>
