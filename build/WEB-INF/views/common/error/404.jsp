<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<main class="pt0">
	<!-- S:CONTENTS AREA -->
    <section class="err-page-msg">
	<div class="left-wrap">
		<h1>
	        We are sorry for the inconvenience.<br>
	        The screen you requested could not be found.
		</h1>
		<p class="err-sub-txt">
             The page you are looking for cannot be found.
             The page address you are looking for is either entered incorrectly or is currently unavailable due to a change or deletion of the page address.
             Please confirm again if the page address you entered is correct.
		</p>
		<p class="err-call">Customer Service (021)2708 5695 ~ 5696</p>
		<ul class="btn-area">
			<li>
				<button id="btnPrevious" class="btn btn-gray-01">Previous page</button>
			</li>
			<li>
				<button id="btnHome" class="btn btn-blue">Home</button>
			</li>
		</ul>
	</div>
	<div class="err-msg-img ">
		<img src="${pageContext.request.contextPath}/kid/images/icon/icon_err_02.svg" alt="404 ERROR">
	</div>
    </section>
    <!-- E:CONTENTS AREA -->
</main>
<script type="text/javascript">
	$(document).ready(function() {
		init();
	});
	
	const init = () => {
		initButton();
	}
	
	const initButton = () => {
		$("#btnPrevious").on('click', function() {
			history.back(-1);
		});
		
		$("#btnHome").on('click', function() {
			location.href = "/";
		});
	}
</script>