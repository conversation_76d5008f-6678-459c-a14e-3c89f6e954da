<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" errorPage = "/WEB-INF/views/kws/common/error/error.jsp"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"%>
<%@ page import="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver"%>
<%@ page import="org.slf4j.Logger"%>
<%@ page import="org.slf4j.LoggerFactory"%>
<%
	String contextPath	= request.getContextPath();
	pageContext.setAttribute("contextPath",contextPath);
	pageContext.setAttribute("referer",request.getHeader("referer"));
	
	Throwable e	= (Throwable)request.getAttribute("exception");
	Logger logger = LoggerFactory.getLogger(SimpleMappingExceptionResolver.class);
	
	try{ 
		if(e!=null){
			if(e.getCause()!=null){
				pageContext.setAttribute("errorMessage",e.getCause().getMessage());
			}else if(pageContext.getAttribute("errorMessage")==null && request.getAttribute("errorMessage")==null){
				pageContext.setAttribute("errorMessage","Invalid Request.");
			}
			
			if(logger.isErrorEnabled())
				logger.error("",e);
		}else if(request.getAttribute("errorMessage")!=null){	
			pageContext.setAttribute("errorMessage",request.getAttribute("errorMessage"));	
		}else{
			pageContext.setAttribute("errorMessage","An error occurred during processing.");
		}
	}catch(NullPointerException npe) {	
		logger.error(npe.getMessage());
	}catch(Exception ex){
		logger.error(ex.getMessage());
	}
%>
<main class="pt0">
    <!-- S:CONTENTS AREA -->
	<section class="err-page-msg">
	<div class="left-wrap">
		<h1>
		We are sorry for the inconvenience.<br>
		A problem occurred during processing.
		</h1>
		<p class="err-point"><c:out value="${errorMessage}"/></p>
		<p class="err-call">Customer Service (021)2708 5695 ~ 5696</p>
		<ul class="btn-area">
			<li>
				<button id="btnPrevious" class="btn btn-gray-01">Previous page</button>
			</li>
			<li>
				<button id="btnHome" class="btn btn-blue">Home</button>
			</li>
		</ul>
	</div>
	<div class="err-msg-img ">
		<img src="${pageContext.request.contextPath}/kid/images/icon/icon_err_01.svg" alt="500 ERROR">
	</div>
     </section>
     <!-- E:CONTENTS AREA -->
</main>
<textarea id="referer" style="display:none;"><c:out value="${referer}"/></textarea>
<script type="text/javascript">
$(function(){
	if(!$("#referer").val())
		$("#referer").val($(location).attr('origin')+"${putil.escapeXml(contextPath)}");
});

$(document).ready(function() {
	init();
});

const init = () => {
	initButton();
}

const initButton = () => {
	$("#btnPrevious").on('click', function() {
		const refererVal = $("#referer").val();
		location.href = refererVal;
	});
	
	$("#btnHome").on('click', function() {
		location.href = "/";
	});
}
</script>	