<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<link href="${pageContext.request.contextPath}/kid/css/old/common.css" rel="stylesheet" type="text/css" media="all" />
<link href="${pageContext.request.contextPath}/kid/css/old/sub.css" rel="stylesheet" type="text/css" media="all" />
<link href="${pageContext.request.contextPath}/kid/css/old/board.css" rel="stylesheet" type="text/css" media="all" />
<script type="text/javascript" src="${pageContext.request.contextPath}/kid/js/libs/jquery/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/kid/js/common/stockSearchPopup.js"></script>
<script type="text/javascript">
var sendStockInfo = function(stockCode, stockNm){
	if(opener) {
		opener.callBackStockInfo(stockCode, stockNm);
		window.open('','_self','');
		window.close();
	} else {
		alert(stockCode+"="+stockName);
	}
};
</script>
<!-- s:cb -->
<div class="cb bd pop">
	<!-- s:srchBox -->
	<div class="srchBox floatClear">
		<ul class="srch_total">
			<li class="srch_l">
				<!-- s:stkSrch_Form -->
				<div class="stkSrch_Form">
					<fieldset>
						<legend>Stock Search</legend>
						<label for=""><img class="tit" src="${pageContext.request.contextPath}/kid/img/old/sub/sub4/txtImg1_1.gif" width="91" height="21" alt="Stock Search" /></label>
						<input type="text" id="searchStockInput" name="searchStockInput" class="inputTxt_srch" placeholder="Insert name" maxlength="15" />
					</fieldset>
				 </div>	
			</li>
		</ul>
	</div>
	<!-- e:srchBox -->
	<table class="tbWeb1" width="100%" summary="Stock Search Result">
		<colgroup>
		<col width="30%" />
		<col width="70%" />
		</colgroup>
		<thead>
			<tr>
				<th scope="col">Code</th>
				<th scope="col">Name</th>
			</tr>
		</thead>
		<tbody id="stockList">
			<tr>
				<td colspan="2" class="center">Please enter stock code or stock name</td>
			</tr>
		</tbody>
	</table>
	<!-- s:tb_pageNavi -->
	<div class="tb_pageNavi">
	</div>
	<!-- e:tb_pageNavi -->
</div>