<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<script type="text/javascript">
$(document).ready(function() {
	if(global.device == '0'){	// PC Device
		$('#quickPc').css('display', 'block');
		$('#quickAndroid').css('display', 'block');
		$('#quickIos').css('display', 'block');
	}else {	// Moblie Device
		$('#quickPc').css('display', 'none');
		
		switch (global.os) {	// Moblie OS
		case 'A':
			$('#quickIos').css('display', 'none');
			$('#quickAndroid').css('display', 'block');				
			break;
		case 'I':
			$('#quickAndroid').css('display', 'none');
			$('#quickIos').css('display', 'block');			
			break;
		default:
			$('#quickAndroid').css('display', 'block');
			$('#quickIos').css('display', 'block');
			break;
		}
	}
});
</script>
<div id="quick-link">
    <ul class="up">
        <li id="quickPc" class="icon-pc" style="display:none"><a href="javascript:kidCommon.downloadHts();" data-gtm-grp="QuickBar" data-gtm-id="HtsDownloadBtn" data-gtm-vid="HtsDownloadCount"></a></li>
        <li id="quickAndroid" class="icon-adroid" style="display:none"><a href="javascript:kidCommon.downloadMtsAndroid();" data-gtm-grp="QuickBar" data-gtm-id="GooglePlayBtn"></a></li>
        <li id="quickIos" class="icon-apple" style="display:none"><a href="javascript:kidCommon.downloadMtsIphone();" data-gtm-grp="QuickBar" data-gtm-id="AppStoreBtn"></a></li>
    </ul>
    <div class="icon-whatsapp">
        <a href="javascript:kidCommon.openWhatsApp();" data-gtm-grp="QuickBar" data-gtm-id="WhatsAppBtn"></a>
    </div>
</div>