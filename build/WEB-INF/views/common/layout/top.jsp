<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="kw" uri="/WEB-INF/tld/KwisTaglib.tld"%>
<spring:eval expression="@kwisCommonUtil.getEnvironment()" var="env" scope="request"/>
<script type="text/javascript">

	// global variable
	const currentMenus = ${currentMenus};
 	const currentMenu = ${currentMenu}[0];
 	const isMulti = '${isMulti}';

 	// menu information
	if(currentMenus){
		if(currentMenus.length == 2){
			if(setCurrentMenu){
				let firstDepth = currentMenus[0];
				let secondDepth = currentMenus[1];

				if(firstDepth)	setCurrentMenu[0] = firstDepth.ucd;
				if(secondDepth)	setCurrentMenu[1] = secondDepth.ucd;
			}
		}else if(currentMenus.length == 3){
			if(setCurrentMenu){
				let firstDepth = currentMenus[0];
				let secondDepth = currentMenus[1];
				let thirdDepth = currentMenus[2];

				if(firstDepth)	setCurrentMenu[0] = firstDepth.ucd;
				if(secondDepth)	setCurrentMenu[1] = secondDepth.ucd;
				if(thirdDepth)	setCurrentMenu[2] = thirdDepth.ucd;
			}
		}
	}

 	// set to document title
 	if(currentMenu)	kidCommon.setDocumentTitle(currentMenu.name);

 	// function : get to language value
 	const getLanguage = () => {
 		let languageVal = kidCommon.getCookie('language');
 		return languageVal == undefined ? null : languageVal;
 	}

 	// function : set to language value
 	const setLanguage = (val) => {
 		kidCommon.setCookie('language', val, 30);
 	}

	// function : screen language changes based on language setting
	const changeLanguage = (isMulti) => {
		if(isMulti === 'Y'){
			let language = getLanguage();	// get cookie
			if(language == '0'){	// EN
				$(".lang-id").removeClass('active');
				$(".lang-en").addClass('active');
			}else if(language == '1'){	// ID
				$(".lang-en").removeClass('active');
				$(".lang-id").addClass('active');
			}else {	// EN
				$(".lang-id").removeClass('active');
				$(".lang-en").addClass('active');
			}
		}
	}

	$(document).ready(function() {

		if(getLanguage() == null){	// if null in cookie
			setLanguage('0');
		}

		let _select_lang = $('#lang-select');
		_select_lang[0].addEventListener('language', (e) => {
			let _target = $(e.target);
			let _target_value = _target.attr('data-value');
			if(_target_value){
				setLanguage(_target_value);
			}

			changeLanguage(isMulti);
		});

		if(currentMenu){
			// The main screen must be processed before it is rendered.
			if(currentMenu.ucd != "00000000" && currentMenu.name != "Main"){
				changeLanguage(isMulti);
			}
		}
	});

</script>
<c:if test="${'dev' eq env}"><article id="dev-mode" class="on">dev mode</article></c:if>
<header id="header">
    <section id="header-wrap">
        <h1>
            <a id="header-icon-logo" href="/" class="logo-pc" data-gtm-grp="Top" data-gtm-id="LogoBtn"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_logo.svg" alt="KIWOOM SEKURITAS INDONESIA"></a>
            <a id="header-icon-back" href="javascript:history.back();" class="history-back" data-gtm-grp="Top" data-gtm-id="BackBtn"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_arrowback.svg" alt="Back"></a>
            <a id="header-icon-home" href="/" class="move-home" data-gtm-grp="Top" data-gtm-id="HomeBtn"><img src="${pageContext.request.contextPath}/kid/images/icon/icon-HouseLine.svg" alt="Go to Main"></a>
        </h1>
        <kw:pc_menu />
        <div id="sub-menu">
        	<a href="javascript:void(0);" onclick="javascript:kidCommon.goOpenAccount();" title="new window open" class="btn-openaccount" data-gtm-grp="Top" data-gtm-id="OpenAccountBtn">
                Open Account
            </a>
            <em></em>
            <a href="javascript:void(0);" onclick="javascript:kidCommon.goForgotId();" class="btn-forgot" data-gtm-grp="Top" data-gtm-id="ForgotIDBtn">
               Forgot ID/Password?
           	</a>
		</div>
        <div class="lang-select select-item" id="lang-select">
            <button class="target">
            	<c:choose>
            		<c:when test="${isMulti eq 'Y'}">
            			<c:choose>
              		<c:when test="${cookie.language.value eq '0'}">
              			EN
              		</c:when>
              		<c:when test="${cookie.language.value eq '1'}">
              			ID
              		</c:when>
              		<c:otherwise>
              			EN
              		</c:otherwise>
              	</c:choose>
            		</c:when>
            		<c:otherwise>
             		<c:choose>
             			<c:when test="${isMulti eq 'N'}">
             				EN
             			</c:when>
             			<c:otherwise>
             				ID
             			</c:otherwise>
             		</c:choose>
            		</c:otherwise>
            	</c:choose>
            </button>
            <ul>
            	<c:choose>
             	<c:when test="${isMulti eq 'Y'}">
             		<li<c:if test="${cookie.language.value eq null || cookie.language.value eq '0'}"> class="on"</c:if>><button>EN</button></li>
                		<li<c:if test="${cookie.language.value eq '1'}"> class="on"</c:if>><button>ID</button></li>
             	</c:when>
             	<c:otherwise>
             		<c:choose>
             			<c:when test="${isMulti eq 'N'}">
             				<li class="on"><button>EN</button></li>
             			</c:when>
             			<c:otherwise>
             				<li class="on"><button>ID</button></li>
             			</c:otherwise>
             		</c:choose>
             	</c:otherwise>
            	</c:choose>
            </ul>
        </div>
        <button id="menu-open-mo">
            <span></span>
            <span></span>
            <span></span>
        </button>
        <div class="mo-menu-bg">
            <div class="mo-menu-wrap">
                <div class="mo-menu-top">
                    <a href="/" class="logo-pc" data-gtm-grp="Menu" data-gtm-id="LogoBtn"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_logo.svg" alt="KIWOOM SEKURITAS INDONESIA"></a>
                    <button class="mo-menu-close">Close</button>
                </div>
                <div class="mo-menu-mid">
                    <ul>
                        <li><a href="javascript:kidCommon.downloadMtsIphone();" data-gtm-grp="Menu" data-gtm-id="AppleStoreBtn"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_apple.svg" alt="APPLE Store"></a></li>
                        <li><a href="javascript:kidCommon.downloadMtsAndroid();" data-gtm-grp="Menu" data-gtm-id="GooglePlayBtn"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_googleplay.svg" alt="GOOGLE PLAY STORE"></a></li>
                        <li><a href="javascript:kidCommon.openWhatsApp();" data-gtm-grp="Menu" data-gtm-id="WhatsAppBtn"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_whatsapp.svg" alt="WHATSAPP"></a></li>
                    </ul>

                    <div class="btn-wrap">
	                   <a href="javascript:void(0);" onclick="javascript:kidCommon.goOpenAccount();" title="new window open" class="btn-openaccount" data-gtm-grp="Menu" data-gtm-id="OpenAccountBtn">
	                       Open Account
	                   </a>
	                   <em></em>
	                   <a href="javascript:void(0);" onclick="javascript:kidCommon.goForgotId();" class="btn-forgot" data-gtm-grp="Menu" data-gtm-id="ForgotIDBtn">
	                       Forgot ID/Password?
	                   </a>
	               </div>
                </div>
                <div class="mo-menu-list">
                    <kw:mobile_menu />
                </div>
            </div>
        </div>
    </section>
</header>
