<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<spring:eval expression="@kwisCommonUtil.getEnvironment()" var="env" scope="request"/>
<c:set var="ver" value="ver=1.${nowDate}" scope="application" />
<head>
<meta charset="UTF-8">
<title>KIWOOM SEKURITAS INDONESIA</title>
<link rel="icon" href="${pageContext.request.contextPath}/kid/images/icon/icon_favicon.svg">
<link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/kid/css/hts.css">
<link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/kid/lib/swiper.2.7.6.css">
<script src="${pageContext.request.contextPath}/kid/lib/jquery-1.2.1.min.js"></script>
<script src="${pageContext.request.contextPath}/kid/lib/swiper.2.7.6.js"></script>
<script src="${pageContext.request.contextPath}/kid/js/kidCommon.js?20240228"></script>
<c:choose>
	<c:when test="${'dev' eq env}">
		<!-- Google Tag Manager -->
		<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-PL7KLSWQ');</script>
		<!-- End Google Tag Manager -->
	</c:when>
	<c:when test="${'prd' eq env}">
		<!-- Google Tag Manager -->
		<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
		new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
		j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
		'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
		})(window,document,'script','dataLayer','GTM-KJXVPHCL');</script>
		<!-- End Google Tag Manager -->
	</c:when>
</c:choose>
<style>
</style>
</head>