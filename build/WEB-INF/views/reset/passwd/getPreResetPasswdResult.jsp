<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<section id="step2" class="complte-pop find-id block" >
    <div class="wrap">
        <div class="form-center-contents">
            <p class=" bold fs24 blue">
            	<c:choose>
           			<c:when test="${resp_gubn eq '0'}">
           				<c:choose>
		           			<c:when test="${ok_yn eq 'Y'}">
		           				Your submitsted request has been completed!<br/>
		           				Please check your e-mail
		           			</c:when>
		           			<c:otherwise>
		           				<c:out value="${ok_msg}"/>
		           			</c:otherwise>
		           		</c:choose>
           			</c:when>
           			<c:otherwise>
           				<c:out value="${resp_mesg}"/>
           			</c:otherwise>
           		</c:choose>
           	</p>
        </div>
    </div>
    <div class="form-center-btn">
        <button id="btnClose" class="btn btn-blue" data-gtm-grp="ResetPassword" data-gtm-id="Close">Close</button>
    </div>
</section>

<script type="text/javascript">
	$(document).ready(function() {
		kidCommon.setDocumentTitle('Result | RESET PASSWORD');
		init2();
	});
	
	var init2 = () => {
		initButton2();
	}
	
	var initButton2 = () => {
		var resp_gubn = '${resp_gubn}';
		var ok_yn = '${ok_yn}';
		$("#btnClose").on('click', function() {
			if(resp_gubn == '0' && ok_yn == 'Y'){	// success
				self.close();
			}else {	// fail
				$('#step2').remove();
				$('#step1').removeClass('complte');	
			}
		});
	}
</script>