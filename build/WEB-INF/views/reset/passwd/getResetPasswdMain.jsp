<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<section class="sub-header-type01">
    <div class="wrap">
        <h2>
            <img src="${pageContext.request.contextPath}/kid/images/icon/icon_logo.svg" alt="KIWOOM SEKURITAS INDONESIA">
        </h2>
        <div class="customer">
            Customer Service<br>
            (021) 2708 5695~5696
        </div>
    </div>
</section>
<form id="frm" name="frm" onSubmit="return false;">
	<section id="step1" class="sub-contents-type01">
	    <div class="contents-bg-area">
	        <img src="${pageContext.request.contextPath}/kid/images/common/bg_etc_02.svg" alt="">
	    </div>
	    <div class="contents-area">
	        <div class="title">
	            <h1>Change your password</h1>
	            <ol>
	                <li>Login Password cannot be the same as your User ID and Account PIN.</li>
	                <li>Login Password must contain 4-8 alphanumeric characters.</li>
	                <li>Capital letters are not permitted.</li>
	            </ol>
	        </div>
	        <dl class="form-list-sub">
	            <dt>Enter new password</dt>
	            <dd id="dd_new_pw">
	                <span id="span_new_pw" class="inp-txt sub-icon sub-icon-05">
	                    <input type="password" id="new_pw" name="new_pw" maxLength="8" placeholder="Enter new password...">
	                </span>
	                <span class="err-inp-txt">Please enter new password</span>
	            </dd>
	            <dt>Repeat new password to</dt>
	            <dd id="dd_confirm_pw">
	                <span id="span_confirm_pw" class="inp-txt sub-icon sub-icon-05">
	                    <input type="password" id="confirm_pw" name="confirm_pw" maxLength="8" placeholder="Confirm password...">
	                </span>
	                <span class="err-inp-txt">Please enter new password</span>
	            </dd>
	        </dl>
	        <div class="sub-contents-btn-area">
	            <button id="btnConfirm" data-gtm-grp="Reset Password" data-gtm-id="Submit" class="btn btn-blue">Submit</button>
	        </div>
	    </div>
	</section>
</form>
<script type="text/javascript">
	
	$(document).ready(function() {
		kidCommon.setDocumentTitle('RESET PASSWORD');
		init();
	});
	
	const init = () => {
		initButton();
	}
	
	const initValidate = () => {
		$("#frm").validate({
			onsubmit: true,
			errorElement: "span",
			errorClass: "err-inp-txt",
			errorPlacement: function(error, element){
				error.insertAfter("#span_" + element.attr("name"));
			},
			highlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).addClass('err');
			},
			unhighlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).removeClass('err');
			},
			rules : {
				new_pw : {
					required : true,
				},
				confirm_pw: {
					required : true,
				}
			},
			messages : {
				new_pw : {
					required : "Please enter new password."
				},
				confirm_pw : {
					required : "Please enter repeat new password."
				}
			},
			submitHandler: function() {
				request();
			}
		});
	}
	
	const initButton = () => {
		$("#btnConfirm").on('click', function() {
			initValidate();
		});
	}
	
	const request = () => {
		const params = {
			langType : "0",	// 0:EN, 1:IN, 2:KR
			newPw : $('#new_pw').val(),
			confirmPw : $('#confirm_pw').val()
		}
			
		const jsonObj = {
			isShowLoading : false,
			method : 'POST',
			url : "/reset/passwd/regResetPasswd",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(result) {
				if(result){
					if(result.resp_gubn == '0'){
						alert(result.resp_mesg);
						self.close();
					}
				}
			},
			error: function(request, status, error) {
				kidCommon.showErrorMessage(request, status, error);
    		}
		}

		kidCommon.ajax(jsonObj);
	}
</script>