<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<section class="sub-header-type01">
    <div class="wrap">
        <h2>
            <img src="${pageContext.request.contextPath}/kid/images/icon/icon_logo.svg" alt="KIWOOM SEKURITAS INDONESIA">
        </h2>
        <div class="customer">
            Customer Service<br>
            (021) 2708 5695~5696
        </div>
    </div>
</section>
<form id="frm" name="frm" onSubmit="return false;">
	<section id="step1" class="sub-contents-type01">
	    <div class="contents-bg-area">
	        <img src="${pageContext.request.contextPath}/kid/images/common/bg_etc_02.svg" alt="">
	    </div>
	    <div class="contents-area">
	        <div class="title">
	            <h1>Reset password </h1>
	        </div>
	        <dl class="form-list-sub">
	            <dt>User ID </dt>
	            <dd id="dd_login_id">
	                <span id="span_login_id" class="inp-txt sub-icon sub-icon-03">
	                    <input type="text" id="login_id" name="login_id">
	                </span>
	            </dd>
	            <dt>E-mail</dt>
	            <dd id="dd_email">
	                <span id="span_email" class="inp-txt sub-icon sub-icon-01">
	                    <input type="text" id="email" name="email">
	                </span>
	            </dd>
	            <dt>KTP/KITAS/Passport</dt>
	            <dd id="dd_id_no">
	                <span id="span_id_no" class="inp-txt sub-icon sub-icon-04">
	                    <input type="text" id="id_no" name="id_no">
	                </span>
	            </dd>
	        </dl>
		    <div class="sub-contents-btn-area">
		        <button id="btnRequest" data-gtm-grp="ResetPassword" data-gtm-id="Request" class="btn btn-blue">Request</button>
		    </div>
	    </div>
	</section>
</form>
<form id="frmMove" name="frmMove">
	<input type="hidden" id="resp_gubn" name="resp_gubn" />
	<input type="hidden" id="resp_mesg" name="resp_mesg" />
	<input type="hidden" id="ok_yn" name="ok_yn" />
	<input type="hidden" id="ok_msg" name="ok_msg" />
</form>
<script type="text/javascript">
	
	$(document).ready(function() {
		kidCommon.setDocumentTitle('RESET PASSWORD');
		init();
	});
	
	const init = () => {
		initButton();
	}
	
	const initValidate = () => {
		$("#frm").validate({
			onsubmit: true,
			errorElement: "span",
			errorClass: "err-inp-txt",
			errorPlacement: function(error, element){
				error.insertAfter("#span_" + element.attr("name"));
			},
			highlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).addClass('err');
			},
			unhighlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).removeClass('err');
			},
			rules : {
				login_id : {
					required : true,
				},
				email: {
					required : true,
					email : true
				},
				id_no : {
					required : true
				}
			},
			messages : {
				login_id : {
					required : "Please enter your Login ID."
				},
				email : {
					required : "Please enter your Email.",
					email : "Please check the email format."
				},
				id_no : {
					required : "Please enter your ID NO."
				}
			},
			submitHandler: function() {
				request();
			}
		});
	}
	
	const initButton = () => {
		$("#btnRequest").on('click', function() {
			initValidate();
		});
	}
	
	const onlyNumChk = (obj) => {
		var tgId = "#"+obj.id;
		$(tgId).numeric();
	}
	
	const request = () => {
		const params = {
			langType : "0",	// 0:EN, 1:IN, 2:KR
			loginId : $('#login_id').val(),
			email : $('#email').val(),
			idNo : $('#id_no').val()
		}
			
		const jsonObj = {
			isShowLoading : false,
			method : 'POST',
			url : "/reset/passwd/regPreResetPasswd",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(result) {
				if(result){
					$('#resp_gubn').val(result.resp_gubn);
					$('#resp_mesg').val(result.resp_mesg);
					$('#ok_yn').val(result.ok_yn);
					$('#ok_msg').val(result.ok_msg);
					
					const moveParams = $("#frmMove").serialize();	
					
					$.post("/reset/passwd/getPreResetPasswdResult", moveParams, function(data) {
						$('html').scrollTop(0);
						$('#step1').addClass('complte');
						$('#frm').after(data);
					});
				}
			},
			error: function(request, status, error) {
				kidCommon.showErrorMessage(request, status, error);
    		}
		}

		kidCommon.ajax(jsonObj);
	}
</script>