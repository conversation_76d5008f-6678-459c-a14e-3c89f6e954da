<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/kid/css/reset-pin.css">
<section class="sub-header-type01">
	<div class="wrap">
		<h2>
			<img src="${pageContext.request.contextPath}/kid/images/icon/icon_logo.svg" alt="KIWOOM SEKURITAS INDONESIA">
		</h2>
		<div class="customer">
			Customer Service<br>
			(021) 2708 5695~5696
		</div>
	</div>
</section>
<form id="frm" name="frm" onSubmit="return false;">
	<section id="step1" class="sub-contents-type01">
		<div class="contents-bg-area">
			<img src="${pageContext.request.contextPath}/kid/images/common/reset_pin_1.svg" alt="">
		</div>
		<div class="contents-area">
			<div class="title">
				<h1>Reset PIN </h1>
			</div>
			<dl class="form-list-sub">
				<dt>User ID </dt>
				<dd id="dd_login_id">
	                <span id="span_login_id" class="inp-txt sub-icon sub-icon-03">
	                    <input type="text" id="login_id" name="login_id">
	                </span>
				</dd>
				<dt>E-mail</dt>
				<dd id="dd_email">
	                <span id="span_email" class="inp-txt sub-icon sub-icon-01">
	                    <input type="text" id="email" name="email">
	                </span>
				</dd>
				<dt>KTP/KITAS/Passport</dt>
				<dd id="dd_id_no">
	                <span id="span_id_no" class="inp-txt sub-icon sub-icon-04">
	                    <input type="text" id="id_no" name="id_no">
	                </span>
				</dd>
			</dl>
			<div class="sub-contents-btn-area">
				<button id="btnRequest" data-gtm-grp="ResetPassword" data-gtm-id="Request" class="btn btn-blue">Request</button>
			</div>
		</div>
	</section>
</form>
<form id="frmMove" name="frmMove">
	<input type="hidden" id="resp_gubn" name="resp_gubn" />
	<input type="hidden" id="resp_mesg" name="resp_mesg" />
	<input type="hidden" id="ok_yn" name="ok_yn" />
	<input type="hidden" id="ok_msg" name="ok_msg" />
</form>

<!-- Alert Box -->
<div class="backdrop" id="alert-backdrop"></div>
<div class="alert-box" id="alert-box">
	<div class="alert-header">
		<span id="alert-title">Error</span> <!-- Dynamic title here -->
		<span class="close-icon" onclick="closeAlert()">&times;</span>
	</div>
	<div class="alert-content">
		<p id="alert-message">No matching information found</p>
	</div>
</div>
<script type="text/javascript">

	$(document).ready(function() {
		kidCommon.setDocumentTitle('RESET PIN');
		init();
	});

	const init = () => {
		initButton();
	}

	const initValidate = () => {
		$("#frm").validate({
			onsubmit: true,
			errorElement: "span",
			errorClass: "err-inp-txt",
			errorPlacement: function(error, element){
				error.insertAfter("#span_" + element.attr("name"));
			},
			highlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).addClass('err');
			},
			unhighlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).removeClass('err');
			},
			rules : {
				login_id : {
					required : true,
				},
				email: {
					required : true,
					email : true
				},
				id_no : {
					required : true
				}
			},
			messages : {
				login_id : {
					required : "Please enter your Login ID."
				},
				email : {
					required : "Please enter your Email.",
					email : "Please check the email format."
				},
				id_no : {
					required : "Please enter your KTP/KITAS/Passport."
				}
			},
			submitHandler: function() {
				$("#btnRequest").prop('disabled', true).addClass('disabled-btn');
				request();
			}
		});
	}

	const initButton = () => {
		$("#btnRequest").on('click', function() {
			initValidate();
		});
	}

	const onlyNumChk = (obj) => {
		var tgId = "#"+obj.id;
		$(tgId).numeric();
	}

	const request = () => {
		const params = {
			langType : "0",	// 0:EN, 1:IN, 2:KR
			loginId : $('#login_id').val(),
			email : $('#email').val(),
			idNo : $('#id_no').val()
		}

		const jsonObj = {
			isShowLoading : false,
			method : 'POST',
			url : "/reset/pin/regPreResetPin",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(result) {
				if(result){
					if(result.ok_yn == 'Y'){
						showAlert("Notice", "Your submitted request has been completed!\nPlease check your e-mail");
					}else{
						let message = result.ok_msg;
						if(message.includes("ID_NO IS NOT VALID")){
							message = "KTP/KITAS/Passport is not matching";
						}else if(message.includes("EMAIL IS NOT VALID")){
							message = "Email is not matching";
						}
						showAlert("Error",message);
						$("#btnRequest").prop('disabled', false).removeClass('disabled-btn');  // Re-enable the button on error and remove class
					}

				}
			},
			error: function(request, status, error) {
				$("#btnRequest").prop('disabled', false).removeClass('disabled-btn');  // Re-enable the button on error and remove class
				showAlert("Error","USER ID Is Not Matching");
			}
		}

		kidCommon.ajax(jsonObj);
	}
	const showAlert = (title, message) => {
		$("#alert-title").text(title);  // Set the title dynamically
		$("#alert-message").html(message);  // Use .html() to render HTML in the message
		$("#alert-backdrop, #alert-box").fadeIn();  // Show backdrop and alert box
	}

	const closeAlert = () => {
		$("#alert-backdrop, #alert-box").fadeOut();  // Hide backdrop and alert box
	}
</script>
