<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/kid/css/reset-pin.css">
<section class="sub-header-type01">
    <div class="wrap">
        <h2>
            <img src="${pageContext.request.contextPath}/kid/images/icon/icon_logo.svg" alt="KIWOOM SEKURITAS INDONESIA">
        </h2>
        <div class="customer">
            Customer Service<br>
            (021) 2708 5695~5696
        </div>
    </div>
</section>
<form id="frm" name="frm" onSubmit="return false;">
	<section id="step1" class="sub-contents-type01">
	    <div class="contents-bg-area">
	        <img src="${pageContext.request.contextPath}/kid/images/common/reset_pin_1.svg" alt="">
	    </div>
	    <div class="contents-area">
	        <div class="title">
	            <h1>Reset your PIN</h1>
	            <ol>
	                <li>Account PIN can not be the same as user ID and Login password.</li>
	                <li>The PIN number must contain 4-8 alphanumeric characters.<br/>
	                	(capital letters are not allowed. Ex: 1234, 1a2b3c)
	                </li>
					<li>Account PIN can not be use symbol or special character.<br/>(Ex: @#$%^&*/)
					</li>
	            </ol>
	        </div>
	        <dl class="form-list-sub">
	            <dt>Enter new PIN</dt>
	            <dd id="dd_new_pin">
	                <span id="span_new_pin" class="inp-txt sub-icon sub-icon-05">
	                    <input type="password" id="new_pin" name="new_pin" maxLength="8" placeholder="Enter new PIN">
	                </span>
	            </dd>
	            <dt>Repeat new PIN to</dt>
	            <dd id="dd_confirm_pin">
	                <span id="span_confirm_pin" class="inp-txt sub-icon sub-icon-05">
	                    <input type="password" id="confirm_pin" name="confirm_pin" maxLength="8" placeholder="Repeat new PIN to...">
	                </span>
	            </dd>
	        </dl>
	        <div class="sub-contents-btn-area">
	            <button id="btnConfirm" data-gtm-grp="Reset Pin" data-gtm-id="Submit" class="btn btn-blue">Submit</button>
	        </div>
	    </div>
	</section>
</form>
<div class="backdrop" id="alert-backdrop"></div>
<div class="alert-box" id="alert-box">
	<div class="alert-header">
		<span id="alert-title">Error</span> <!-- Dynamic title here -->
		<span class="close-icon" onclick="closeAlert()">&times;</span>
	</div>
	<div class="alert-content">
		<p id="alert-message">No matching information found</p>
	</div>
</div>
<script type="text/javascript">

	$(document).ready(function() {
		kidCommon.setDocumentTitle('RESET PIN');
		init();
	});

	const init = () => {
		initButton();
	}

	const initValidate = () => {
		$("#frm").validate({
			onsubmit: true,
			errorElement: "span",
			errorClass: "err-inp-txt",
			errorPlacement: function(error, element){
				error.insertAfter("#span_" + element.attr("name"));
			},
			highlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).addClass('err');
			},
			unhighlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).removeClass('err');
			},
			rules : {
				new_pin : {
					required: true,
				},
				confirm_pin: {
					required: true,
					equalTo: "#new_pin"  // Ensures confirm_pw matches new_pw
				}
			},
			messages : {
				new_pin : {
					required : "Please enter new PIN."
				},
				confirm_pin : {
					required : "Please enter repeat new PIN.",
					equalTo: "Entered Incorrect New PIN."
				}
			},
			submitHandler: function() {
				request();
			}
		});
	}

	const initButton = () => {
		$("#btnConfirm").on('click', function() {
			initValidate();
		});
		$('#new_pin, #confirm_pin').on('keydown', function(e) {
			if (e.which === 32) {
				e.preventDefault();  // Prevent space from being entered
			}
		});
	}
	let isRedirect = false;
	const request = () => {
		const params = {
			langType : "0",	// 0:EN, 1:IN, 2:KR
			newPin : $('#new_pin').val(),
			confirmPin : $('#confirm_pin').val()
		}

		const jsonObj = {
			isShowLoading : false,
			method : 'POST',
			url : "/reset/pin/regResetPin",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(result) {
				if(result){
					if(result.resp_gubn === '0'){
						 showAlert("Notice", "Your PIN has been reset!");
						 isRedirect = true;
						 setTimeout(()=>{
							 window.location.href = "https://kiwoom.co.id/main"
						 },3000);
					}
				}
			},
			error: function(request, status, error) {
				let message = kidCommon.getErrorMessage(request);
				if(message.includes("Password identical with previous password. Unable to change")){
					message = "PIN identical with previous PIN. Unable to change";
				}else if(message.includes("PCF_SCRT_VALID_CHK ERROR [The login password cannot use it as password][9998]")){
					message = "Account PIN can not be the same with Login Password";
				}else if(message.includes("PCF_SCRT_VALID_CHK ERROR [The login id cannot use it as password][9998]")){
					message = "Account PIN can not be the same as user ID";
				}else if(message.includes("Special Character")){
					message = "Special Character can not use";
				}else if(message.includes("Already done")){
					message = "Your PIN has already been reset";
				}
				showAlert("Error",message);
    		}
		}

		kidCommon.ajax(jsonObj);
	}
	const showAlert = (title, message) => {
		$("#alert-title").text(title);  // Set the title dynamically
		$("#alert-message").html(message);  // Use .html() to render HTML in the message
		$("#alert-backdrop, #alert-box").fadeIn();  // Show backdrop and alert box
	}

	const closeAlert = () => {
		$("#alert-backdrop, #alert-box").fadeOut();  // Hide backdrop and alert box
		if(isRedirect){
			window.location.href = "https://kiwoom.co.id/main";
		}

	}
</script>
