<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags"  %>
<script type="text/javascript">
	$(document).ready(function() {
		init();
	});
	
	const init = () => {
		initButton();
		chkShowMsg();
	}
	
	const chkShowMsg = () => {	// check for login error message output
		const loginErrormessage = '${loginErrormessage}';
		if(loginErrormessage){
			showMsgPop();
		}
	}
	
	const initButton = () => {	// initialize button
		$("#btnForgotId").on("click",function() {	// forgot password
			kidCommon.goForgotId();
		});
		
		$("#btnLogin").on("click",function() { // login button
			$("form").submit();
		});
		
		$("#btnPopOk").on("click",function() {	// close popup
			closeMsgPop();
		});
	}
	
	const showMsgPop = () => {	// show error message popup
		$('#divMsg').addClass("on");
		$('.login-area').addClass("pop");
		popUpAdjust.init();
	}
	
	const closeMsgPop = () => {	// hide error message popup
		$('#divMsg').removeClass("on");
		$('.login-area').removeClass("pop");
	}
	
</script>
<main class="pt0">
	<form name="loginFrm" id="loginFrm" action="/login" method="post">	
		<section class="login-area">
            <div class="dimm"></div>
            <div class="login-area-wrap">
                <div class="login-main-img"></div>
                <div class="input-area">
                    <h3>Log in</h3>
                    <ul>
                        <li>
                            <span class="tit">User ID</span>
                            <span class="inp-txt">
                                <input type="text" name="username" id="username">
                            </span>
                        </li>
                        <li>
                            <span class="tit">Password</span>
                            <span class="inp-txt">
                                <input type="password" name="password" id="password">
                            </span>
                        </li>
                    </ul>
                    <a id="btnForgotId" href="javascript: void(0);" class="resetPW">Forgot ID?</a>
                    <button id="btnLogin" class="btn btn-blue">Log on</button>
                </div>
            </div>           
   		</section>
   	</form>
   	<!-- msg popup -->
	<div id="divMsg" class="pop-up-type-01 pop-01 popup">
         <h5><c:out value="${loginErrormessage}"/></h5>
         <button id="btnPopOk" class="btn btn-blue">OK</button>
    </div>
    <!-- msg popup -->
</main>