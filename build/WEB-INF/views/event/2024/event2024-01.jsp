<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://www.springframework.org/tags" prefix="spring"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<spring:eval expression="@kwisCommonUtil.getEnvironment()" var="env" scope="request"/>
<script type="text/javascript">
	var interation = {
		init:()=>{
			wrap = document.querySelector('.evt-01-top');
			wrap = wrap.querySelector('.evt-mid');
			_width = document.querySelector('body').offsetWidth;
			cardWrap =wrap.querySelectorAll('.evt-interation-wrap');
			stopBtn = document.querySelector('.point-01');
			item1 = wrap.querySelector('.item1');
			item2 = wrap.querySelector('.item2');
			cardW = wrap.offsetWidth;
			interation.calcW(cardW);

			_time = 10000;
			//_v = [{x:0}, {x:-cardW}];

			interation.animationEvent(wrap,cardWrap, cardW, _time);

		},
		calcW:(cardW)=>{
			_t =cardWrap.length;
			_t = cardW * _t;

			wrap.style.width = _t +"px";
			cardWrap.forEach((cardWraps, index)=>{
				cardWrap[index].style.width =  cardW+"px";
			});
			setTimeout(()=>{
				cardWrap[1].style.display =  'flex';
			},200);
		},

		animationEvent:(wrap,cardWrap,cardW)=>{
			interation.calcW(cardW);
			_set = 0;
			_set2 = 2500-_width;
			_v = [_set, -_set2];

			let keyframe2 = [
				{transform:'translateX('+_v[0]+'px)'},
				{transform:'translateX('+_v[1]+'px)'},
			];
			let options ={
					duration: _time,
					easing:'linear',
					fill:'both',
					//direction:'alternate',
					iterations: Infinity ,
			}

			cardWrap.forEach((cardWraps, index)=>{
				_set = cardWrap[index];
				_set = window.getComputedStyle(_set).transform;

				if(_set == 'none') {
					_set = 0;
					_set2 = 1250-_width
					_v = [_set, -_set2];
					 keyframe = [
						 {transform:'translateX('+_v[0]+'px)'},
						 {transform:'translateX('+_v[1]+'px)'}
					];
				}else{
					matrix = _set.replace('matrix(', '').replace(')','').split(',')
					_set = matrix[4];
					_set2 = (_set+_set);
					_v = [-_set, -_set2];
					 keyframe = [
							{transform:`translateX(430px)`,},
							{transform:`translateX(-1250px)`},
					];

				}
				//cardWrap[index].animate(keyframe, {...options, delay: index*_time});


				//console.log(cardWrap[index].animate)
			});
			wrap.animate(keyframe2, {...options, delay: 0});
		},

	}

	window.addEventListener('load', function(){
		global.mod == 'mo' ?  interation.init()  :null;
	});

	var dwidth = $(window).width();
	$(window).bind('resize', function(e){
		var wwidth = $(window).width();
		if(dwidth!==wwidth){
			dwidth = $(window).width();

			if (window.RT) clearTimeout(window.RT);
			window.RT = setTimeout(function(){
				this.location.reload(false); /* false to get page from cache */
			}, 1000);
		}
	});

	$(document).ready(function() {
		kidCommon.setDocumentTitle('Renewal Grand Open Event');
		init();
	});
	function init() {
		initButton();
		initTheme();
		initBusiness();
	}
	function initTheme(){
          bgColorSetting();
          bgImageSetting();
	}
	function initButton(){
		// Event Main > Get shares
		let _btnGetShares = $("button[id='btnGetShares']");
		_btnGetShares.on('click', ()=> {
			processRoulette();
		});

		// Event Main > Open Account
		let _btnOpenAccount = $("button[id='btnOpenAccount']");
		_btnOpenAccount.on('click', ()=> {
			eventCommon.goToOpenAccountPage();
		});

		// Event Main > Get ticket
		let _btnApplyEvent = $("button[id='btnGetTicket']");
		_btnApplyEvent.on('click', ()=> {
			const eventId = $('#eventId').val();
			const params = {
				eventId : eventId
			}
			eventCommon.eventApply(params);
		});

		// Event Main > Go to trading
		let _btnGotoTrading = $("button[id='btnGotoTrading']");
		_btnGotoTrading.on('click', ()=> {
			alertPop("#alert-00", 'In ready.');
		});

		// Event Main > Copy Link
		let _btnCopyLink = $("button[id='btnCopyLink']");
		_btnCopyLink.on('click', ()=> {
			let referralCode = $('#referralCode').text();
			writeClipBoard(referralCode);
		});

		// Event Main > Share event with WhatsApp
		let _btnShareWhatsApp = $("button[id='btnShareWhatsApp']");
		_btnShareWhatsApp.on('click', ()=> {
			shareLink();
		});

		// Event Main > Use remaining entry ticket
		let _btnUseRemainingTicket = $("button[id='btnUseRemainingTicket']");
		_btnUseRemainingTicket.on('click', ()=> {
			processRoulette();
		});

		// Reuslt Popup > Recommend to a friend to get entry ticket
		let _btnResultRecommend = $("button[id='btnResultRecommend']");
		_btnResultRecommend.on('click', ()=> {
			closeResultPop();
			goBenefit3();
		});

		// Reuslt Popup > Use remaining entry ticket
		let _btnResultUseRemainingTicket = $("button[id='btnResultUseRemainingTicket']");
		_btnResultUseRemainingTicket.on('click', ()=> {
			closeResultPop();
			processRoulette();
		});

		// Reuslt Popup > Close
		let _btnCloseResultPop = $("button[id='evt-01-pop-close']");
		_btnCloseResultPop.on('click', ()=> {
			closeResultPopAndReload();
		});
	}

	function initBusiness(){
		let loginYn = $('#loginYn').val();
		getRouletteInfo(loginYn);
		getHistory(loginYn);
	}

	function getRouletteInfo(loginYn){
		if(loginYn != 'Y'){
			showRouletteCnt(loginYn);
			showReferralCode();
			return;
		}

		const eventId = $('#eventId').val();
		eventCommon.getRouletteInfo({eventId: eventId}, (data) => {
			if(data){
				$("#etc_info1").val(data.etc_info1);
				$("#etc_info2").val(data.etc_info2);
				$("#etc_info3").val(data.etc_info3);
				showRouletteCnt(loginYn);
				showReferralCode();
			}
		},(error) => {
			alertPop("#alert-00", error);
		});
	}

	function showRouletteCnt(loginYn){
		if(loginYn != 'Y'){
			$('#availCnt').addClass("type-02");
			$('#availCnt').text("?");
			$('#usedCnt').addClass("type-02");
			$('#usedCnt').text("?");
			return;
		}

		let etc_info2 = $("#etc_info2").val();
		let etc_info3 = $("#etc_info3").val();

		if(Number(etc_info2) == 0 && Number(etc_info3) >= 1){
			let ahref = '<a href="javascript:goBenefit3();">' + etc_info2 + '</a>';
			$('#availCnt').addClass("type-02");
			$('#availCnt').html(ahref);
		}else {
			if(Number(etc_info2) > 0){
				$('#availCnt').addClass("type-01");
			}else {
				$('#availCnt').addClass("type-02");
			}
			$('#availCnt').text(etc_info2);
		}

		$('#usedCnt').addClass("type-02");
		$('#usedCnt').text(etc_info3);
	}

	function goBenefit3(){
		var offset = $("#div_benefit3").offset();
		$("html, body").animate({scrollTop: offset.top}, 400);
	}

	function showReferralCode(){
		let etc_info1 = $("#etc_info1").val();
		if(etc_info1){
			$('#referralCode').text(etc_info1);
			$('#div_referral').removeClass("none");
		}
	}

	function getHistory(loginYn){
		const _tblHistory = $("#tblHistory");
 		_tblHistory.empty();

 		$('#div_evt_table').removeClass("null");
		if(loginYn != 'Y'){
			let tr = '<tr><td colspan="5">Dapat dicek setelah login.</td></tr>';
	 		_tblHistory.append(tr);
	 		$('#div_evt_table').addClass("null");
			return;
		}

		const eventId = $('#eventId').val();
		eventCommon.eventRouletteHistory({eventId: eventId}, (data) => {
			if(data){
				var grid = data.g1;
				if(grid){
					for(let i  = 0 ; i < grid.length; i++){
						let tr = '';
						if(grid[i].stock_code){
								tr = '<tr>';
					 			tr += 	'<td>'+toYYYMMDD(grid[i].rgst_dt)+'</td>';
					 			tr += 	'<td><span translate="no">'+grid[i].stock_code+'</span></td>';
					 			tr += 	'<td>'+parseInt(grid[i].stock_qty)+' shares</td>';
					 			tr += 	'<td>'+toYYYMMDD(grid[i].proc_dt)+'</td>';
					 			tr += 	'<td>'+grid[i].ticket_info+'</td>';
					 			tr += '</tr>';
						}else {
								tr = '<tr class="evt-entry-tiket">';
					 			tr += 	'<td colspan="4">';
					 			tr += 		'<button onclick="javascript:processRoulette();">Gunakan sisa tiket</button>';
					 			tr += 	'</td>';
					 			tr += 	'<td>'+grid[i].ticket_info+'</td>';
					 			tr += '</tr>';
						}
						_tblHistory.append(tr);
					}
				}else {
					let tr = '<tr><td colspan="5">Data tidak ada.</td></tr>';
			 		_tblHistory.append(tr);
			 		$('#div_evt_table').addClass("null");
				}
			}
		});
	}

	function toYYYMMDD(inputDate){
	   if(inputDate === "" || inputDate === undefined || inputDate === null){
		   return "";
	   }
	   let year = inputDate.slice(0, 4);
       let month = inputDate.slice(4, 6);
       let day = inputDate.slice(6);
       return year+'.'+month+'.'+day
	}

	function closeResultPop(){
		closePop('.evt-01-pop');
	}

	function closeResultPopAndReload(){
		closePop('.evt-01-pop');
		location.reload();
	}

	function processRoulette(){
		const eventId = $('#eventId').val();

		eventCommon.getRouletteCnt({eventId: eventId}, (data) => {
			if(data){
				if(data.etc_info2 == '0'){
					var toDate = '${toDate}';
					if(toDate <= 20241130){
						alertPop("#alert-00", 'Tidak ada tiket masuk.<br/>Rekomendasikan teman untuk menerima tiket masuk!');	// There are no entry ticket.<br/>Recommend a friend to receive an entry ticket!	
					}else {
						alertPop("#alert-00", 'Tidak ada tiket masuk.');	// There are no entry ticket.
					}
					return;
				}

				eventCommon.eventRoulette({eventId: eventId}, (data) => {
					$("#result_ticket").text(data.ableCnt);
		    		$("#result_stockLogo").attr("src","${pageContext.request.contextPath}/kid/images/event/2024-01/logo/Logo_"+data.prizeCd+".svg");

					showResultPop();
					getRouletteInfo('Y');
					getHistory('Y');

				},(error) => {
					let message = error;
					let lang = getLanguage();
					if(lang === '1' && message.includes("Run Out of Ticket")){
						message = 'Tiket sudah habis';
					}
					alertPop("#alert-00", message);
				});
			}
		},(error) => {
			alertPop("#alert-00", error);
		});
	}

	function showResultPop(){
		let mod = global.mod;

        $('.evt-wrap').addClass('pop');

        var z=fnMaxZIndex();
        popH = $(window).scrollTop();
        popW = $('.evt-01-pop').width()/2;
        $('.dimm').css({
            zIndex:z-1,
            display:'block'

        });

        $('html').addClass('fix');
        if(mod =='pc'){
            $('.evt-01-pop').css({
                top:popH+'px',
                marginLeft:-popW+'px',
                left:50+'%',
                display:'block',
                zIndex:z+1
            });
        }else if(mod=='mo'){
            $('.evt-01-pop').css({
                top: popH+40+'px',
                left:50+'%',
                display:'block',
                zIndex:z+1
            });
    	}
	}

	function shareLink(){
		let env = '${env}';
		let recomm_id = $("#etc_info1").val();
		let url = "";

		// let message = "\nIf your friend opens an account in Kiwoom, you will get another ticket.\n\n100% Winning\nGet an extra 100 shares!\nIf the account is successfully registered, ticket will be automatically received.";
		let message = "\nKamu akan mendapatkan tambahan tiket jika temanmu bergabung dan membuat akun di Kiwoom.\n\nMenang 100%\nDapatkan tambahan 100 lembar saham.\nJika akun berhasil terdaftar, tiket akan otomatis diperoleh.";
		if(recomm_id){
			if(env == 'dev'){
				url = "https://kiwoom.dev.webarq.net?recomm_id=" + recomm_id;	// TEST
			}else {
				url = "https://welcome.kiwoom.co.id?recomm_id=" + recomm_id;
			}
		}else {
			if(env == 'dev'){
				url = "https://kiwoom.dev.webarq.net";	// TEST
			}else {
				url = "https://welcome.kiwoom.co.id";
			}
		}

		const params = {
			url : url,
			message : message
		}
		eventCommon.shareWhatsApp(params);
	}

	function goToLoginPage(){
		const eventId = $('#eventId').val();
		eventCommon.goToLoginPage(eventId);
	}

	function writeClipBoard(str){
		navigator.clipboard.writeText(str).then(
			() => {
				alertPop("#alert-00", 'Salinan telah selesai.');	// The string has been copied.
			}
		);
	}

	function alertPop(obj, content){
         try {
        	if(content !== undefined){
            	$(obj).find('.contents').html(content);
            }

        	let mod = global.mod;
            $('.evt-wrap').addClass('pop');
            winH = $(window).scrollTop() + $(obj).height();
            popW =  $(obj).width()/2;
            popH =  $(obj).height()/2;
            $('html').addClass('fix');
            var z=fnMaxZIndex();
            $('.dimm').css({
                zIndex:z-1,
                display:'block'
            });

            if(mod =='pc'){
                $(obj).css({
                    top:winH+'px',
                    marginLeft:-popW+'px',
                    display:'block',
                    zIndex:z+1
                });
            }else if(mod=='mo'){

                $(obj).css({
                    top:winH+'px',
                    display:'block',
                    zIndex:z+1
                });
            }
         } catch (error) {
             console.log(error);
         }
     }

     function closePop(obj){
         try {
             $('.evt-wrap').removeClass('pop');
             $('html').removeClass('fix');
             $(obj).hide(50);
             $('.dimm').hide();

             if(obj == '#alert-02'){
            	 location.reload();
             }

         } catch (err) {
             console.log(err)
         }
     }

     function decodeHTMLEntities (str) {
         if(str !== undefined && str !== null && str !== '') {
             str = String(str);
             str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
             str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
             var element = document.createElement('div');
             element.innerHTML = str;
             str = element.textContent;
             element.textContent = '';
         }
         return str;
     }

     function bgColorSetting(){
         try {
			_obj = document.querySelectorAll('.setBgColor');
			_obj.forEach((obj,idx) =>{
				_set = _obj[idx].getAttribute('area-backgroundColor');
				_obj[idx].style.backgroundColor =  _set;
             });
         } catch (error) {
             console.log(error);
         }
     }

     function bgImageSetting(){
         try {
			_obj = document.querySelectorAll('.setBgImage');
			_baseURL = "${pageContext.request.contextPath}/kid/images/event";
			_obj.forEach((obj,idx) =>{
				_set = _obj[idx].getAttribute('area-backgroundImage');
				if(_set != null){
                    //decodeHTMLEntities(_set);
                    console.log(_set)
					_obj[idx].style.backgroundImage='url('+_baseURL+_set+')';
				}
             });
         } catch (error) {
             console.log(error);
         }
     }
</script>
<main>
	<input type="hidden" id="eventId" name="eventId" value="${eventId}" />
	<input type="hidden" id="mdgb" name="mdgb" value="${mdgb}" />
	<input type="hidden" id="loginYn" name="loginYn" value="${loginYn}" />

	<input type="hidden" id="etc_info1" name="etc_info1" />
	<input type="hidden" id="etc_info2" name="etc_info2" />
	<input type="hidden" id="etc_info3" name="etc_info3" />

	<section class="evt-wrap evt-01">
		<section class="evt-01-top">
        	<div class="evt-detail">
		        <div class="evt-top">
		        	<h2><img src="${pageContext.request.contextPath}/kid/images/icon/icon_hero.svg" alt="HERO" class="img"></h2>
		            <h1>Peluncuran</h1>
		            <p class="point-01">Sistem Trading Baru</p>
					<div class="evt-symbol">
						<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_33.png?********" alt="100+100+100">
                    </div>
					<p class="point-02"><span>Bergabunglah</span>dan dapatkan<span>saham</span></p>
		            <p class="point-03">${s_dt_str} ~ ${e_dt_str}</p>
		        </div>
		        <div class="evt-mid">
		            <div class="evt-interation-wrap item1">
						<ul>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_55.svg?20240912" alt="UNVR">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_48.svg?20240912" alt="HMSP">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_42.svg?20240912" alt="PWON">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_44.svg?20240912" alt="SIDO">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_54.svg?20240912" alt="TOWR">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_46.svg?20240912" alt="PGAS">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_43.svg?20240912" alt="ACES">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_52.svg?20240912" alt="BBTN">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_53.svg?20240912" alt="MAPI">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_49.svg?20240912" alt="ERAA">
							</li>
						</ul>
					</div>

					<div class="evt-interation-wrap item2">
						<ul>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_55.svg?20240912" alt="UNVR">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_48.svg?20240912" alt="HMSP">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_42.svg?20240912" alt="PWON">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_44.svg?20240912" alt="SIDO">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_54.svg?20240912" alt="TOWR">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_46.svg?20240912" alt="PGAS">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_43.svg?20240912" alt="ACES">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_52.svg?20240912" alt="BBTN">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_53.svg?20240912" alt="MAPI">
							</li>
							<li>
								<img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_49.svg?20240912" alt="ERAA">
							</li>
						</ul>
					</div>
		        </div>
		        <div class="evt-bottom">
		            <button id="btnGetShares" data-gtm-grp="Event" data-gtm-id="Get shares">Dapatkan Saham</button>
		            <div class="evt-count-ticket-wrap">
		                <div class="evt-count-ticket">
		                    <span>Tersedia</span>
		                    <strong id="availCnt"></strong>
		                </div>
		                <div class="evt-count-ticket">
		                    <span>Digunakan</span>
		                    <strong id="usedCnt"></strong>
		                </div>
		            </div>
		            <p>Saham apa saja yang sudah saya terima?</p>
		        </div>
		    </div>
		</section>
		<section class="evt-01-mid">
		    <div class="evt-con-wrap">
		        <div class="evt-01-benefit evt-benefit">
		            <!-- <div class="evt-top">
		                <span>Keuntungan</span>
		                <em>1</em>
		                <strong>Seluruh Nasabah</strong>
		            </div> -->
		            <div class="evt-mid">
		            	<div class="evt-mid-count">1</div>
		                <div class="left">
		                    <div class="evt-mid-top">
		                        100 lembar<br>
								<strong>
									saham untuk
								<i>Nasabah</i> KIWOOM<br>
								</strong>
								<p>
									Dapatkan
									tiketmu sekarang dan dapatkan
								   	tambahan 100 lembar saham
								</p>
		                    </div>
		             	</div>
	                    <div class="evt-mid-bottom">
	                        <div class="evt-left">
	                            <span>Belum punya akun?</span>
	                            <button id="btnOpenAccount" data-gtm-grp="Event" data-gtm-id="Open an Account">Buka Akun</button>
	                        </div>
	                        <div class="evt-right">
	                            <span>Sudah punya akun?</span>
	                            <button id="btnGetTicket" data-gtm-grp="Event" data-gtm-id="Get ticket">Dapatkan tiket</button>
	                        </div>
	                    </div>
		                <div class="right">
		                    <div class="evt-ticket-01">
		                        <h3 class="logo"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_hero.svg" alt="HERO" class="img"></h3>
		                        <div class="mid">
		                            <img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_07.svg?********" alt="100">
		                            <span>lembar</span>
		                        </div>
		                        <p class="sub-txt">saham untuk Nasabah Kiwoom</p>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		    <div class="evt-con-wrap">
		        <div class="evt-benefit evt-02-benefit">
		            <!-- <div class="evt-top">
		                <span>Keuntungan</span>
		                <em>2</em>
		                <strong>Transaksi</strong>
		            </div> -->
		            <div class="evt-mid">
		            	<div class="evt-mid-count">2</div>
		                <div class="left">
		                    <div class="evt-mid-top">
		                        100 lembar<br>
								<strong>
									saham jika<br>
									kamu <i>transaksi!</i><br>
								</strong>
								<p>Dengan transaksi sebesar 10 juta rupiah</p>
								<p>
								Tiket akan otomatis kamu dapat bila kamu melakukan transaksi
								</p>
		                    </div>
		                </div>
		                <!--
		                <div class="evt-mid-bottom">
                            <div class="evt-left">
                            	<button id="btnGotoTrading" data-gtm-grp="Event" data-gtm-id="Go to trading">Lakukan transaksi</button>
                            </div>
                        </div>
                        -->
		                <div class="right">
		                    <div class="evt-ticket-01">
		                        <h3 class="logo"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_hero.svg" alt="HERO" class="img"></h3>
		                        <div class="mid">
		                            <img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_10.svg?********" alt="100">
		                            <span>lembar</span>

		                        </div>
		                        <p class="sub-txt">100 lembar saham lagi jika<br>kamu transaksi</p>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		    <c:if test='${toDate <= 20241130}'>
			    <div id="div_benefit3" class="evt-con-wrap">
			        <div class="evt-03-benefit evt-benefit">
			            <!-- <div class="evt-top">
			                <span>Keuntungan</span>
			                <em>3</em>
			                <strong>Teman</strong>
			            </div> -->
			            <div class="evt-mid">
			            	<div class="evt-mid-count">3</div>
			                <div class="left">
			                    <div class="evt-mid-top">
			                        100 lembar saham <br>setiap kali <br>kamu<br>
									<strong>
									<i>merekomendasikan</i><br>
									Kiwoom ke temanmu
									</strong>
									<p>
										Dapatkan tiket tambahan jika temanmu bergabung dan
										membuat akun di Kiwoom dengan referral link
									</p>
			                    </div>
			                </div>
			                <div class="evt-mid-bottom">
		                    	<div id="div_referral" class="evt-copy-code-wrap none">
			                        <div class="evt-copy-code">
			                            <span>Kode referral</span>
			                            <em id="referralCode"></em>
			                            <button id="btnCopyLink" data-gtm-grp="Event" data-gtm-id="Copy link">Salin tautan</button>
			                        </div>
			                   	</div>
		                        <div class="evt-left">
		                            <button id="btnShareWhatsApp" data-gtm-grp="Event" data-gtm-id="Share event with WhatsApp">Bagikan link referral via WhatsApp</button>
		                        </div>
		                    </div>
			                <div class="right">
			                    <div class="evt-ticket-01">
			                        <h3 class="logo"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_hero.svg" alt="HERO" class="img"></h3>
			                        <div class="mid">
			                            <img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_16.png?********" alt="100">
			                            <span>lembar</span>
			                        </div>
			                        <div class="evt-sub-01"></div>
			                        <!-- <div class="evt-sub-02"><span>FRIEND</span></div> -->
			                        <p class="sub-txt">rekomendasikan<br>Kiwoom ke temanmu</p>
			                    </div>
			                </div>
			            </div>
			        </div>
			    </div>
		    </c:if>
		</section>
		<section class="evt-01-bottom">
		    <div class="evt-con-wrap">
		        <div class="evt-con-detail">
		            <h4 class="tit">Saham apa saja yang sudah saya terima?</h4>
		            <div id="div_evt_table" class="evt-table">
		                <table>
		                    <caption></caption>
		                    <colgroup>
		                        <col width="20%" span="5">
		                    </colgroup>
	                        <thead>
	                            <tr>
	                                <th scope="col">Tanggal</th>
	                                <th scope="col">Saham</th>
	                                <th scope="col">Volume</th>
	                                <th scope="col">Settle(perkiraan)</th>
	                                <th scope="col">Informasi Tiket</th>
	                            </tr>
	                        </thead>
	                        <tbody id="tblHistory"></tbody>
		                </table>
	                </div>
	                <div class="evt-btn-01">
	                    <button id="btnUseRemainingTicket" class="evt-pop-btn" data-gtm-grp="Event" data-gtm-id="Use remaining entry ticket">Gunakan sisa tiket</button>
	                </div>
	            </div>
	        </div>
		</section>
	</section>
	<!-- result pop -->
	<div class="evt-01-pop">
	    <div class="evt-pop-top">
	        <h2>Receive stock CARD</h2>
	        <button id="evt-01-pop-close" class="close">Close Pop</button>
	    </div>
	    <div class="evt-pop-mid">
	        <div class="evt-resulte-01">
	            <span>Tiket belum terpakai</span>
	            <em><i id="result_ticket"></i>ticket</em>
	        </div>
	        <div class="evt-resulte-02">
	            <div class="bg-0">
	                <img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_19.svg?********" alt="">
	            </div>
	            <div class="card">
	                <img id="result_stockLogo" src="" alt="">
	            </div>
	            <div class="bg-1">
	                <img src="${pageContext.request.contextPath}/kid/images/event/2024-01/evt_20.svg?********" alt="">
	            </div>
	            <span class="evt-icon-mark">100<em>shares</em></span>
	        </div>
	        <div class="evt-btn-wrap">
	            <button id="btnResultRecommend" data-gtm-grp="Event" data-gtm-id="Reuslt Popup Recommend to a friend">
	                rekomendasi ke teman<br>
					untuk dapat tambahan tiket
	            </button>
	            <button id="btnResultUseRemainingTicket" data-gtm-grp="Event" data-gtm-id="Result Popup Use remaining entry ticket">Gunakan sisa tiket</button>
	        </div>
	    </div>
	</div>
	<!--// result pop -->
	<div class="notice-pop" id="alert-00">
		<div class="top">Notice</div>
		<div class="contents"></div>
		<div class="bottom">
			<button class="btn btn-blue btn-confirm" onclick="javascript:closePop('#alert-00');">Confirm</button>
		</div>
	</div>
	<div class="notice-pop" id="alert-01">
		<div class="top">Notice</div>
		<div class="contents">
			Kamu harus login.<br/><!-- Login is required. -->
			Apakah kamu ingin meninggalkan halaman ini?<!-- Would you like to move the Login page? -->
		</div>
		<div class="bottom">
			<button class="btn btn-white btn-confirm" onclick="javascript:closePop('#alert-01');">Cancel</button>
			<button class="btn btn-blue btn-confirm" onclick="javascript:goToLoginPage();">Confirm</button>
		</div>
	</div>
	<div class="notice-pop" id="alert-02">
		<div class="top">Notice</div>
		<div class="contents">
			Kamu mendapatkan 1 tiket di keuntungan pertama.<br><!-- You have received a ticket of benefit 1. -->
			 Silakan putar rolet untuk mendapatkan saham!<!-- Please spin the roulette to receive stocks! -->
		</div>
		<div class="bottom">
			<button class="btn btn-blue btn-confirm" onclick="javascript:closePop('#alert-02');">Confirm</button>
		</div>
	</div>
	<div class="dimm"></div>
</main>
