<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<script src="https://www.youtube.com/iframe_api"></script>
<main>
	<div class="grand-banner type09">
	    <h2>TRADING SYSTEM</h2>
	</div>
	
	<div class="contents-wrap rounding-off">
	    
	    <div class="mts-intro-top">
	        <h2 class="lang-en active">Introduction</h2>
            <h2 class="lang-id">Introduksi</h2>
	        <h4 class="img"><img src="${pageContext.request.contextPath}/kid/images/icon/icon_hero.svg" alt="HERO" ></h4>
	        <span>Investasi Saham & Reksadana</span>
			<h3 class="lang-en active">A Kiwoom Sekuritas Indonesia <strong class="pink">Home Trading System</strong></h3>
			<h3 class="lang-id"><strong class="pink">Desktop Trading System </strong>Kiwoom Sekuritas Indonesia</h3>
			<div class="download">
                <a href="#none" onclick="javascript:kidCommon.downloadHts();" data-gtm-grp="TradingSystem HTS" data-gtm-id="Download Now" data-gtm-vid="HtsDownloadCount">
                    <img src="${pageContext.request.contextPath}/kid/images/icon/icon_HTSDownloadBtn.svg" alt="HTS Download" data-gtm-grp="TradingSystem HTS" data-gtm-id="Download Now" data-gtm-vid="HtsDownloadCount">
                </a>
            </div>
	    </div>
	    
	    <div class="mts-intro-mid hts">
	        <div class="bg">
	            <img src="${pageContext.request.contextPath}/kid/images/common/bg_htsIntroTop.svg" alt="chart">
	        </div>
	        <div class="sticker2">
	            <img src="${pageContext.request.contextPath}/kid/images/icon/icon_mtsMark.svg" alt="POWERFUL INVERSTING APP">
	        </div>
	        
	        <ul class="down2">
	            <li>
	                <img src="${pageContext.request.contextPath}/kid/images/icon/icon_MicrosoftBtn.svg" alt="Microsoft">
	            </li>
	        </ul>
	        
	    </div>
	
	   	<div class=" mts-intro-bottom introduction-icon lang-en active">                        
	        <ul>
	            <li>
	                <span>High Speed</span>
	            </li>
	            <li>
	                <span>High Perfromance</span>
	            </li>
	            <li>
	                <span>More Easily Trading</span>
	            </li>
	        </ul>
	    </div>
		
		<div class=" mts-intro-bottom introduction-icon lang-id">
	        <ul>
	            <li>
	                <span>Cepat</span>
	            </li>
	            <li>
	                <span>Memiliki banyak fitur</span>
	            </li>
	            <li>
	                <span>Lebih mudah digunakan trading</span>
	            </li>
	        </ul>
	    </div>
	</div>
	
	<div class="comprehensiveFeature">
	    <div class="contents-wrap">
	        <div class="top">
	            <h4 class="lang-en active">Comprehensive Feature</h4>
				<h4 class="lang-id">Fitur Komprehensif</h4>
	        </div>
	        
	
	        <div class="left">
	            <div class="left-pagenation"></div>
	            
	        </div>
	
	        <div class="right">
	
	            <div class="swiper swiper-comprehensiveFeature hts">
	                <ul class="swiper-wrapper">
	                    <li class="swiper-slide">
	                        <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_comprehensiveFeature_01.svg" alt="">
	                    </li>
	                    <li class="swiper-slide">
	                        <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_comprehensiveFeature_02.svg" alt="">
	                    </li>
	                    <li class="swiper-slide">
	                        <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_comprehensiveFeature_03.svg" alt="">
	                    </li>
	                    <li class="swiper-slide">
	                        <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_comprehensiveFeature_04.svg" alt="">
	                    </li>
	                    <li class="swiper-slide">
	                        <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_comprehensiveFeature_05.svg" alt="">
	                    </li>
	                    <li class="swiper-slide">
	                        <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_comprehensiveFeature_06.svg" alt="">
	                    </li>
	
	                </ul>
	                
	            </div>
	            <div class="comprehensiveFeature-pagination"></div>
	            <ol class="comprehensiveFeature-nav">
	                <li><span class="prev"></span></li>
	                <li><span class="next"></span></li>  
	            </ol>
	        </div>
	
	    </div>
	</div>
	
	
	
	<div class="howToUse-mts">
	    <div class="contents-wrap">
	        <h4 class="lang-en active">How To Use</h4>
            <h4 class="lang-id">Cara Pakai</h4>
	        <div id="div_youtube" class="youtube-area"></div>
	
	        <div class="howToUseMtsStep hts">
	            <span class="arrow-01 arrow"></span>
	            <span class="arrow-02 arrow"></span>
	            
	            <ul class="hts">
	                <li>
	                    <div class="left">
                            <div class=" lang-en active">
                                <h3>STEP 01</h3>
                                <p>Open Account or Request Trial ID Before Do Further</p>
                                <div>
                                    <button name="btn_open_account" class="btn btn-blue " data-gtm-grp="TradingSystem HTS" data-gtm-id="Open Account">Open Account</button>
                                </div>
                            </div>
                            <div class="lang-id">
                                <h3>LANGKAH 1</h3>
                                <p>Buka Rekening atau membuat Trial ID sebelum melanjutkan.</p>
                                <div>
                                    <button name="btn_open_account" class="btn btn-blue " data-gtm-grp="TradingSystem MTS" data-gtm-id="Open Account">Buka Rekening</button>
                                </div>
                            </div>
                            
                        </div>
	                    <div class="right">
	                        <img src="${pageContext.request.contextPath}/kid/images/icon/icon_howTo_10.svg" alt="HOW TO USE">
	                    </div>
	                </li>
	                <li>
	                    <div class="left">
                            <div class=" lang-en active">
                                <h3>STEP 02</h3>
                                <p>Download Application</p>
                                <div>
                                    <button name="btn_hts_download" class="btn btn-blue " data-gtm-grp="TradingSystem HTS" data-gtm-id="Download Click" data-gtm-vid="HtsDownloadCount">Download Click Here</button>
                                </div>
                            </div>
                            <div class="lang-id">
                                <h3>LANGKAH 2</h3>
                                <p>Unduh Aplikasi</p>
                                <div>
                                    <button name="btn_hts_download" class="btn btn-blue " data-gtm-grp="TradingSystem HTS" data-gtm-id="Download Click" data-gtm-vid="HtsDownloadCount">Download Click Here</button>
                                </div>
                            </div>
                        </div>
	                    <div class="right">
	                        <img src="${pageContext.request.contextPath}/kid/images/icon/icon_howTo_08.svg" alt="HOW TO USE">
	                    </div>
	                </li>
	                <li>
	                    <div class="left">
                            <div class=" lang-en active">
                                <h3>STEP 03</h3>
                                <p>Use HERO</p>
                                <div>
                                    <button name="btn_see_introduction" class="btn btn-blue " data-gtm-grp="TradingSystem HTS" data-gtm-id="seeIntroduction">See introduction</button>
                                </div>
                            </div>     
                            <div class="lang-id">
                                <h3>LANGKAH 3</h3>
                                <p>Jalankan HERO</p>
                                <div>
                                    <button name="btn_see_introduction" class="btn btn-blue " data-gtm-grp="TradingSystem HTS" data-gtm-id="seeIntroduction">See introduction</button>
                                </div>
                            </div>
                        </div>
	                    <div class="right">
	                        <img src="${pageContext.request.contextPath}/kid/images/icon/icon_howTo_09.svg" alt="HOW TO USE">
	                    </div>
	                </li>
	            </ul>
	        </div>
	
	    </div>
	    <!--
	    <div class="requestTrialID hts">
	        <div class="contents-wrap">
	            <h3 class="lang-en active">Open Account or Request for Trial ID</h3>
                <h3 class="lang-id">Buka Rekening Atau Membuat ID percobaan</h3>
	            <div class="btn-wrap">
	                <button name="btn_open_account" class="btn btn-large btn-pink lang-en active" data-gtm-grp="TradingSystem HTS" data-gtm-id="Open Account">Open Account</button>
	                <button name="btn_trial_id" class="btn btn-large btn-blue lang-en active" data-gtm-grp="TradingSystem HTS" data-gtm-id="Trial ID">Request for Trial ID</button>
	                <button name="btn_open_account" class="btn btn-large btn-pink lang-id" data-gtm-grp="TradingSystem HTS" data-gtm-id="Open Account">Buka Rekening</button>
	                <button name="btn_trial_id" class="btn btn-large btn-blue lang-id" data-gtm-grp="TradingSystem HTS" data-gtm-id="Trial ID">Membuat ID percobaan</button>
	            </div>
	        </div> 
	    </div>
	    -->
	</div>
	
	
	
	
	<div class="howto-tab-wrap tab-wrap tab-target-list">
	    <div class="tabTarget on howto-tab-01" id="tab1">    
	        <div class="howToApp hts">
	            <div class="howToInstall-txt content-tit hts-tit lang-en active">
	                How to install
	                <h4>
	                    Supported on Windows7 or Above <br>
	                    Windows Update guide, please contact your desktop manufactuner
	                </h4>
	            </div>
	            <div class="howToInstall-txt content-tit hts-tit lang-id">
                    Cara Instalasi
                    <h4>
                        Dapat digunakan untuk Windows 7 <br>
                        Panduan pembaruan windows dapat mengujungi situs resmi perangkat Anda.
                    </h4>
                </div>
	            <div class="contents-wrap swiper swiper-02">
	                <ul class="swiper-wrapper">
	                    <li class="swiper-slide">
	                        <div class="txt">
	                            <strong>01</strong>
	                            <span class="lang-en active">Download File</span>
                                <span class="lang-id">Unduh file</span>
	                        </div>
	                        <div class="img">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_howToGuidePC_01.svg" alt="Run Google Play Store">
	                        </div>
	                    </li>
	                    <li class="swiper-slide">
	                        <div class="txt">
	                            <strong>02</strong>
	                            <span class="lang-en active">Run File Downloaded "KiwoomHeroGSetup.exe"</span>
                                <span class="lang-id">Jalankan file hasil unduhan</span>    
	                        </div>
	                        <div class="img">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_howToGuidePC_02.svg" alt="App Search Keyword Hero Kiwoom">
	                        </div>
	                    </li>
	                    <li class="swiper-slide">
	                        <div class="txt">
	                            <strong>03</strong>
	                            <span class="lang-en active">Follow the step until complete, then find HERO on your desktop</span>
                                <span class="lang-id">Simpan file hasil unduhan Lalu ikuti langkah tersebut hingga selesai.</span>
	                        </div>
	                        <div class="img">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_howToGuidePC_03.svg" alt="Download & InstallWhen it’s complete run Hero">
	                        </div>
	                    </li>
	                </ul>
	                
	            </div>
	            <div class="swiper-02-graybullet-page graybullet-page mo"></div>
	            <div class="howToApp-btnWrap">
	                <span class="swiper-02-prev prev">
	                    <img src="${pageContext.request.contextPath}/kid/images/btn/btn_swiper_howToApp_01_left.svg" alt="">
	                </span>
	                <span class="swiper-02-next next">
	                    <img src="${pageContext.request.contextPath}/kid/images/btn/btn_swiper_howToApp_01_right.svg" alt="">
	                </span>
	            </div>
	        </div>
	    </div>
	</div>
	
	
	<div class="preview-txt content-tit lang-en active">
        Preview
    </div>
    <div class="preview-txt content-tit lang-id">
        Pratinjau
    </div>
	<section class="preview contents-wrap">
	    <section class="screenshot-mts">
	        <div class="screenshot-menu swiper">
	            <ul class="screenshot-tap-triger swiper-wrapper">
	                <li class="on swiper-slide">
	                    <a href="javascript: void(0)" data-tab="3">
	                        <div class="lang-en active">
	                            <h6>Menu structure</h6>
	                            <span>Simple menu structure and expand/collapse</span>
	                        </div>
	                        <div class="lang-id">
                                <h6>Struktur Menu</h6>
                               	<span>Struktur Menu Sederhana ringkas</span>
                            </div>                   
	                    </a>
	                </li>
	                <li class="swiper-slide">
	                    <a href="javascript: void(0)" data-tab="4">
	                        <div class="lang-en active">
	                            <h6 >Real-time Running Trade</h6>
	                            <span>Provide "Real time done transaction"</span>
	                        </div>
	                        <div class="lang-id">
                                <h6>Real-time Running Trade</h6>
                                <span>Menampilkan Real time transaksi di pasar</span>
                            </div>
	                    </a>
	                </li>
	                <li class="swiper-slide">
	                    <a href="javascript: void(0)" data-tab="5">
	                        <div class="lang-en active">
	                            <h6>Easy & Fast Order</h6>
	                            <span>Be the first in line and organized transaction</span>
	                        </div>
	                       <div class="lang-id">
								<h6>Mudah & Order Cepat</h6>
                                <span>Jadilah yang pertama dalam antrian dan transaksi terorganisir</span>
                           </div>
	                    </a>
	                </li>
	                <li class="swiper-slide">
	                    <a href="javascript: void(0)" data-tab="6">
	                        <div class="lang-en active">
	                            <h6>Comprehensive Chart</h6>
	                            <span>Provide variety chart type and chart analysis possible</span>
	                        </div>
	                       <div class="lang-id">
                                <h6>Komprehensif Chart</h6>
								<span>Menyediakan berbagai macam tipe chart dan analisa</span>
                           </div>
	                    </a>
	                </li>
	                <li class="swiper-slide">
	                    <a href="javascript: void(0)" data-tab="7">
	                        <div class="lang-en active">
	                            <h6>Variety Invest Info</h6>
	                            <span>Provide variety Invest Information</span>
	                        </div>
	                       <div class="lang-id">
                           		<h6>Informasi investasi lengkap</h6>
                                <span>Menyediakan berbagai macam informasi investasi</span>
                           </div>
	                    </a>
	                </li>
	                <li class="swiper-slide">
	                    <a href="javascript: void(0)" data-tab="8">
	                        <div class="lang-en active">
	                            <h6>Auto-Sync Favorite Stock </h6>
	                            <span>Auto-Sync Favorite stock HERO MTS & HTS</span>
	                        </div>
	                       	<div class="lang-id">
								<h6>Sinkronisasi otomatis saham favorit </h6>
                                <span>Sinkronisasi otomatis saham favorit di HERO MTS dengan HTS </span>
                            </div>
	                    </a>
	                </li>
	                <li class="swiper-slide">
	                    <a href="javascript: void(0)" data-tab="9">
	                        <div class="lang-en active">
	                            <h6>Market Research Is In Your Hands</h6>
	                            <span>Get reliable research every day</span>
	                        </div>
	                       	<div class="lang-id">
                            	<h6>Market Riset Ada di Tangan Anda</h6>
                                <span>Penelitian yang dapat diandalkan setiap hari</span>
							</div>
	                    </a>
	                </li>
	                <li class="swiper-slide">
	                    <a href="javascript: void(0)" data-tab="10">
	                        <div class="lang-en active">
	                            <h6>Always Get The News First</h6>
	                            <span>We provide news and emiten news</span>
	                        </div>
	                     	<div class="lang-id">
								<h6>Selalu mendapatkan berita eksklusif</h6>
                                <span>Menyediakan berita tentang pasar modal dan emiten</span>
                            </div>
	                    </a>
	                </li>
	            </ul>
	        </div>
	        
	        <span class="screenshot-next"></span>
	        <span class="screenshot-prev"></span>
	        
	    </div>
	  
	    <section class="screenShot-tab screenshot-tap-list">
	        <div id="tab4" class="tabTarget on">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper01">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_01.svg" alt="dummy">
	                        </li>
	                       
	                    </ul>
	                </div>
	                <div class="swiper-pagination screenshot-swiper01-pagination"></div>
	                
	            </div>
	        </div>
	
	        <div id="tab5" class="tabTarget">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper02">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_02.svg" alt="dummy">
	                        </li>
	                    </ul>
	                    <div class="swiper-pagination screenshot-swiper02-pagination"></div>
	                </div>
	               
	            </div>
	        </div>
	
	        <div id="tab6" class="tabTarget">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper03">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_04.svg" alt="dummy">
	                        </li>
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_05.svg" alt="dummy">
	                        </li>
	                    </ul>
	                    <div class="swiper-pagination screenshot-swiper03-pagination"></div>
	                </div>
	               
	            </div>
	        </div>
	
	        <div id="tab7" class="tabTarget">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper04">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_06.svg" alt="dummy">
	                        </li>
	                    </ul>
	                    <div class="swiper-pagination screenshot-swiper04-pagination"></div>
	                </div>
	               
	            </div>
	        </div>
	
	        <div id="tab8" class="tabTarget">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper05">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_08.svg" alt="dummy">
	                        </li>
	                       
	                    </ul>
	                    <div class="swiper-pagination screenshot-swiper05-pagination"></div>
	                </div>
	               
	            </div>
	        </div>
	
	        <div id="tab9" class="tabTarget">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper06">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_09.svg" alt="dummy">
	                        </li>
	                    </ul>
	                    <div class="swiper-pagination screenshot-swiper06-pagination"></div>
	                </div>
	               
	            </div>
	        </div>
	
	
	        <div id="tab10" class="tabTarget">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper07">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_11.svg" alt="dummy">
	                        </li>
	                    </ul>
	                    <div class="swiper-pagination screenshot-swiper07-pagination"></div>
	                </div>
	               
	            </div>
	        </div>
	
	        <div id="tab11" class="tabTarget">
	            <div class="wrap ">
	                <div class="wrap-detail screenshot-swiper08">
	                    <ul class="swiper-wrapper">
	                        <li class="swiper-slide">
	                            <img src="${pageContext.request.contextPath}/kid/images/temp/dummy_hts_screenshot_13.svg" alt="dummy">
	                        </li>
	                    </ul>
	                    <div class="swiper-pagination screenshot-swiper08-pagination"></div>
	                </div>
	               
	            </div>
	        </div>
	
	    </section>
	
	</section>
</main>

<script type="text/javascript">

let language = '${language}';
let youtubePlayer;

window.addEventListener('load', function(){
    
    tab.init('.screenshot-tap-triger', '.screenshot-tap-list');
    setTimeout(function(){
        comprehensiveFeature(language);
    },500);
   	
    initYoutube();
    howToApp01();
    howToApp02();
    swiperScreenShot01();
    swiperScreenShot02();
    swiperScreenShot03();
    swiperScreenShot04();
    swiperScreenShot05();
    swiperScreenShot06();
    swiperScreenShot07();
    swiperScreenShot08();
    swiperScreenShotMenu();
});

comprehensiveFeature_01 = undefined;
function comprehensiveFeature(language){
    if(comprehensiveFeature_01 != undefined){
        comprehensiveFeature_01.destroy();
        comprehensiveFeature_01 = undefined;
    }
    reset = () =>{
        $('.left-pagenation').find('.swiper-pagination-bullet').removeClass('swiper-pagination-bullet-active');
        $('.comprehensiveFeature-pagination').find('.swiper-pagination-bullet').removeClass('swiper-pagination-bullet-active');
    }
    reset();
   
    var comprehensiveFeature_01 = new Swiper(".swiper-comprehensiveFeature", {
        slidsPerView:1,
        speed: 500,
        loop:false,
        loopAdditionalSlides : 1,
       
        a11y : false, 
        observer: true,	
        observeParents: true, 
        touchMoveStopPropagation:true,
        navigation: {
            nextEl: ".next",
            prevEl: ".prev",
        },
       
        pagination: {
            el: ".comprehensiveFeature-pagination",
            type: "bullets",
        },
    
        on: {
            slideChange : function() {
                reset();
                var num = this.realIndex;
                $('.left-pagenation').find('.swiper-pagination-bullet').removeClass('swiper-pagination-bullet-active');
                $('.left-pagenation').find('.swiper-pagination-bullet').eq(num).addClass('swiper-pagination-bullet-active');
                $('.comprehensiveFeature-pagination').find('.swiper-pagination-bullet').removeClass('swiper-pagination-bullet-active');
                $('.comprehensiveFeature-pagination').find('.swiper-pagination-bullet').eq(num).addClass('swiper-pagination-bullet-active');
            },
        },
       
    });
    
  var pagingSwiper = new Swiper(".swiper-comprehensiveFeature", {
        loop:false,
        loopAdditionalSlides : 1,
        a11y : false, 
        observer: true,	
        observeParents: true, 
        pagination: {   
            el: ".left-pagenation",   
            clickable: true,    
            type : 'bullets',   
            renderBullet: function (index, className) {
                let txt_en = [
                    'HERO have various contents and more easily trading',
                    'System suport by advance AI',
                    'Auto-Sync Favorite stock HTS and MTS',
                    'Display Real-time market information <br>(e.g. Running Trade, Quote, Portfolio)',
                    'Provide very fast and various chart type',
                    'Customized menu for flexible user experience.'
                ];
                let txt_id = [
                	'HERO dilengkapi dengan banyak fitur dan semakin mudah digunakan untuk ber transaksi.',
                    'Dukungan sistim AI yang canggih',
                    'Otomatis sinkron saham favorit antara <br>HTS & MTS.',
                    'Menampilkan informasi secara Real-Time <br>(e.g. Running Trade, Penawaran, Portofolio)',
                    'Menyediakan chart yang beragam dan cepat',
                    'Menu yang dapat diatur sesuai dengan keinginan.'
                ];
                
                if(language == '0'){
            		return '<div class="' + className + '"><span>' + (index+1) + '</span>' + txt_en[index] + '</div>';	
            	}else if (language == '1'){
            		return '<div class="' + className + '"><span>' + (index+1) + '</span>' + txt_id[index] + '</div>';
            	}
            },
        },
   
        
       
    });
    //comprehensiveFeature_01.controller.control = pagingSwiper;

}

function initYoutube(){	
	try {
		if(youtubePlayer){
			youtubePlayer = undefined;
		}
		youtubePlayer = new YT.Player('div_youtube', {
			videoId: 'bDTvRjeiKlA',
			width: '100%',
			height: '315px',
			playerVars:{
				'modestbranding' : 1,
				'autoplay' : 0,
				'controls' : 1,
				'rel' : 0,
				'loop' : 0
			}
		})	
	} catch (e) {
		console.log(e);
	}
}

function playYoutube(){
	if(youtubePlayer){
		var offset = $("#div_youtube").offset();
		$("html, body").animate({scrollTop: offset.top - 100}, 400);
		youtubePlayer.playVideo();
	}
}

function howToApp01(){
    var howToApp1 = new Swiper(".swiper-02", {
        observer: true,	
        allowTouchMove:true,
        observeParents: true,
        watchSlidesProgress: true,
        navigation: {
            nextEl: ".swiper-02-next",
            prevEl: ".swiper-02-prev",
        },
        pagination: {
            el: ".swiper-02-graybullet-page",
            type: "bullets",
        },

        breakpoints: {
            360: {
                spaceBetween: 50,
                slidesPerView: 'auto',
                loop: true,
            
            },
            720: {
                spaceBetween: 80,
                slidesPerView: 3,
                loop: true,
            },
        },
    });
}

function howToApp02(){
    var howToApp2 = new Swiper(".swiper-03", {
        observer: true,	
        allowTouchMove:true,
        observeParents: true,
        watchSlidesProgress: true,
        navigation: {
            nextEl: ".swiper-03-next",
            prevEl: ".swiper-03-prev",
        },
        pagination: {
            el: ".swiper-03-graybullet-page",
            type: "bullets",
        },

        breakpoints: {
            360: {
                spaceBetween: 50,
                slidesPerView: 'auto',
                loop: true,
            
            },
            720: {
                spaceBetween: 80,
                slidesPerView: 3,
                loop: true,
            },
        },
    });
}

function swiperScreenShot01(){
    var researchSwiper1 = new Swiper(".screenshot-swiper01", {
        observer: true,
        observeParents: true,

        pagination: {
            el: '.screenshot-swiper01-pagination',
            type: 'bullets',
            clickable: true
        },
        breakpoints: {
            360: {
                watchOverflow : true,
                slidesPerView: 1,
                spaceBetween: 50,
                
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
                
            },
        },
       
    });
}
function swiperScreenShot02(){
    var researchSwiper2 = new Swiper(".screenshot-swiper02", {
        observer: true,	
        observeParents: true,
        pagination: {
            el: '.screenshot-swiper02-pagination',
            type: 'bullets',
            clickable: true
        },
        breakpoints: {
            360: {
                slidesPerView: 1,
                spaceBetween: 50,
               
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
                
            },
        },
        
    });
}
function swiperScreenShot03(){
    var researchSwiper3 = new Swiper(".screenshot-swiper03", {
        observer: true,	
        observeParents: true,
        pagination: {
                    el: '.screenshot-swiper03-pagination',
                    type: 'bullets',
                    clickable: true
                },
        breakpoints: {
            360: {
                slidesPerView: 1,
                spaceBetween: 50,
               
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
               
            },
        },
        
    });
}
function swiperScreenShot04(){
    var researchSwiper4 = new Swiper(".screenshot-swiper04", {
        observer: true,	
        observeParents: true,
        pagination: {
            el: '.screenshot-swiper04-pagination',
            type: 'bullets',
            clickable: true
        },
        breakpoints: {
            360: {
                slidesPerView: 1,
                spaceBetween: 50,
                
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
                
            },
        },
        
    });
}
function swiperScreenShot05(){
    var researchSwiper5 = new Swiper(".screenshot-swiper05", {
        observer: true,	
        observeParents: true,
        pagination: {
            el: '.screenshot-swiper05-pagination',
            type: 'bullets',
            clickable: true
        },
        breakpoints: {
            360: {
                slidesPerView: 1,
                spaceBetween: 50,
               
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
               
            },
        },
        
    });
}
function swiperScreenShot06(){
    var researchSwiper6 = new Swiper(".screenshot-swiper06", {
        observer: true,	
        observeParents: true,
        pagination: {
            el: '.screenshot-swiper06-pagination',
            type: 'bullets',
            clickable: true
        },
        breakpoints: {
            360: {
                slidesPerView: 1,
                spaceBetween: 50,
               
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
                
            },
        },
        
    });
}
function swiperScreenShot07(){
    var researchSwiper7 = new Swiper(".screenshot-swiper07", {
        observer: true,	
        observeParents: true,
        pagination: {
            el: '.screenshot-swiper07-pagination',
            type: 'bullets',
            clickable: true
        },
        breakpoints: {
            360: {
                slidesPerView: 1,
                spaceBetween: 50,
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
               
            },
        },
        
    });
}
function swiperScreenShot08(){
    var researchSwiper8 = new Swiper(".screenshot-swiper08", {
        observer: true,	
        observeParents: true,
        pagination: {
            el: '.screenshot-swiper08-pagination',
            type: 'bullets',
            clickable: true
        },	
        breakpoints: {
            360: {
                slidesPerView: 1,
                spaceBetween: 50,
               
            },
            720: {
                slidesPerView: 2,
                spaceBetween: 10,
               
            },
        },
        
    });
}


function swiperScreenShotMenu(){
    var swiperScreenShotMenu = new Swiper(".screenshot-menu", {
        observer: true,	
        observeParents: true,
        loop:true,
        navigation: {
            nextEl: ".screenshot-next",
            prevEl: ".screenshot-prev",
        },
        breakpoints: {
            360: {
               
                direction:'horizontal',
                slidesPerView: 'auto',
                spaceBetween: 16,
                allowTouchMove:true,
            },
            720: {
               
                direction:'vertical',
                slidesPerView: 8,
                spaceBetween: 0,
               
            },
        },
        
    });
}

var cachedWidth = $(window).width();
window.addEventListener('resize', function(){
    
    var newWidth = $(window).width();
    if(newWidth !== cachedWidth){
        comprehensiveFeature(language);
        cachedWidth = $(window).width();
    }
});

$(document).ready(function() {
	let _btnOpenAccount = $("button[name='btn_open_account']");
	_btnOpenAccount.on('click', ()=> kidCommon.goOpenAccount());
	
	let _btnHtsDownload = $("button[name='btn_hts_download']");
	_btnHtsDownload.on('click', ()=> kidCommon.downloadHts());
	
	let _btnSeeIntroduction = $("button[name='btn_see_introduction']");
	_btnSeeIntroduction.on('click', ()=> playYoutube());
	
	let _select_lang = $('#lang-select');
	_select_lang[0].addEventListener('language', (e) => {
		let _target = $(e.target);
		let _target_value = _target.attr('data-value');
		if(_target_value){
			language = _target_value;
			comprehensiveFeature(_target_value);
		}
	});
});
</script>