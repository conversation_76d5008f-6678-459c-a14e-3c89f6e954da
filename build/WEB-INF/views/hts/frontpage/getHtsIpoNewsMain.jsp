<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<script src="${pageContext.request.contextPath}/kid/lib/moment-2.20.0.min.js"></script>
<body <c:if test="${mode == 'dark'}">class="dark"</c:if>>
	<!-- S:CONTENTS -->
	<div class="news-wrap"> 
	    <div class="list">
	         <ul id="ipoNews">
	         </ul>
	      </div>
	</div>
	<!-- E:CONTENTS -->
</body>
<script type="text/javascript">
	
	var protocol = window.location.protocol;
	var host = window.location.host;
	
	$(document).ready(function() {
		initIpoNews("#ipoNews");
		kidCommon.setDocumentTitle('IPO News | Frontpage | HTS');
	})
	
	function isMoreThanOneDayAhead(targetDateStr) {
		// Parse the target date using moment
		var targetDate = moment(targetDateStr, 'DD/MM/YYYY HH:mm:ss');
		// Set the desired UTC offset to UTC+7 (Bangkok, Thailand)
		var desiredUtcOffset = 7;
		// Get the current date in the local timezone
		var currentDateLocal = moment();
		// Get the UTC offset in hours for the local timezone
		var currentUtcOffset = currentDateLocal.utcOffset() / 60;
		// Calculate the difference in hours between local timezone and UTC+7
		var offsetDifference = desiredUtcOffset - currentUtcOffset;
		// Convert the current date to the UTC+7 timezone
		var currentDateUtcPlus7 = currentDateLocal.add(offsetDifference, 'hours');
		// Calculate the difference in days
		var differenceInDays = currentDateUtcPlus7.diff(targetDate, 'days');
		// Check if the difference is greater than 1
		return differenceInDays >= 1;
	}
	
	function goDetail(seqNo) {
		document.location.href = 'kwtp://7047?goUrl=' + protocol + '//' + host + '/iponews/detailIpoNewsMain?id=' + seqNo;
	}
	
	function initIpoNews(id) {
		$(id).empty();
		var url = '${pageContext.request.contextPath}/kid/upload/iponews/json/IPONewsJSON.json?${ver}';
		
		$.getJSON(url, function(res) {
			for (var i = 0; i < res.length; i++){
		    	var ipon = res[i];
		    	var title = ipon.TITL;
		    	var makeDate = ipon.MAKEDATE;
		    	var seqNo = ipon.SEQNO;
		    	var isNew = !isMoreThanOneDayAhead(ipon.MAKEDATE3);
		    	var li = '<li>'	
		    		li += '<a href="javascript:goDetail('+seqNo+');" data-gtm-grp="HTS Frontpage" data-gtm-id="IpoNewsBtn'+ (i+1) + '">'				
		    		li += '<span class="txt">'			
		    	if(isNew == true){
		    		li += '[NEW] '
		    	}
		    		li += title
		    		li += '</span>'				
		    		li += '<span class="date">'				
		    		li += makeDate					
		    		li += '</span>'				
		    		li += '</a>'			
		    		li += '</li>'		
				$(id).append(li);
		    }
		});
		
		/*
		var jsonObj = {
				type : 'GET',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res){
					for (var i = 0; i < res.length; i++){
				    	var ipon = res[i];
				    	var title = ipon.TITL;
				    	var makeDate = ipon.MAKEDATE;
				    	var seqNo = ipon.SEQNO;
				    	var isNew = !isMoreThanOneDayAhead(ipon.MAKEDATE3);
				    	var li = '<li>'	
				    		li += '<a href="javascript:goDetail('+seqNo+');" data-gtm-grp="HTS Frontpage" data-gtm-id="IpoNewsBtn'+ (i+1) + '">'				
				    		li += '<span class="txt">'			
				    	if(isNew == true){
				    		li += '[NEW] '
				    	}
				    		li += title
				    		li += '</span>'				
				    		li += '<span class="date">'				
				    		li += makeDate					
				    		li += '</span>'				
				    		li += '</a>'			
				    		li += '</li>'		
						$(id).append(li);
				    }
				},
				error: function(e1,e2,e3) {
					// Called only if json file cannnot be read
					var url = '${pageContext.request.contextPath}/hts/frontpage/getIpoNewsTop5';
					var jsonObj = {
						type : 'POST',
						url : url,
						contentType : 'application/json; charset=utf-8',
						success : function(res) {
							for (var i = 0; i < res.length; i++){
						    	var ipon = res[i];
						    	var title = ipon.TITL;
						    	var makeDate = ipon.MAKEDATE;
						    	var seqNo = ipon.SEQNO;
						    	var isNew = ipon.IS_NEW;
						    	var li = '<li>'	
						    		li += '<a href="javascript:goDetail('+seqNo+');" data-gtm-grp="HTS Frontpage" data-gtm-id="IpoNewsBtn'+ (i+1) + '">'				
						    		li += '<span class="txt">'			
						    	if(isNew == 1){
						    		li += '[NEW] '
						    	}
						    		li += title
						    		li += '</span>'				
						    		li += '<span class="date">'				
						    		li += makeDate					
						    		li += '</span>'				
						    		li += '</a>'			
						    		li += '</li>'		
								$(id).append(li);
						    }
						},	
						error: function(request, status, error) {
							kidCommon.showErrorMessage(request, status, error);
			    		}
					}
					kidCommon.ajax(jsonObj);
				}
		}
		kidCommon.ajax(jsonObj);
		*/
	}
</script>