<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<body>
    <!-- S:CONTENTS -->
    <div class="swiper-wrap swiper">
        <ul class="swiper-wrapper" id="swiper-wrapper">
        	<c:if test="${htsTopFirstImgUrl != ''}">
	        	<li class="swiper-slide">
	        		<a><img src="${pageContext.request.contextPath}/kid/upload/banner/${htsTopFirstImgUrl}" /></a>
	        	</li>
	        </c:if>	        
		</ul>       
        <div id="swiper-pagination" class="swiper-pagination"></div>
    </div>
    <!-- E:CONTENTS -->
	<script type="text/javascript">
		var bannerCnt = 0;
	
		window.onload = function(){
			initBanner("#swiper-wrapper");
			kidCommon.setDocumentTitle('TopBanner | Frontpage | HTS');
		}
		
	    function initBanner(id) {
	    	$(id).empty();
	    	var url = '${pageContext.request.contextPath}/kid/upload/banner/json/frontpage_banner01.json?${ver}';
			
	    	$.getJSON(url, function(res) {
				for (var i = 0; i < res.length; i++) {
					var banner = res[i];
					var bannerImgUrl = '${pageContext.request.contextPath}/kid/upload/banner/'+banner.imgFileUrl;
					var li = '<li class="swiper-slide">';
					var pcLinkUrl = banner.pcLinkUrl;
					var linkMethod = banner.linkMethod;
					if(pcLinkUrl){
						if(pcLinkUrl.indexOf('/') == 0){
							pcLinkUrl = 'kwtp://7047?goUrl=' + pcLinkUrl;
							linkMethod = "_self";
						}else if(pcLinkUrl.indexOf('kiwoom.co.id') > -1){
							if(pcLinkUrl.indexOf('welcome.kiwoom.co.id') < 0){
								pcLinkUrl = 'kwtp://7047?goUrl=' + pcLinkUrl;
								linkMethod = "_self";
							}
						}
						li += '<a href="'+pcLinkUrl+'" target="'+linkMethod+'" class="banner-01" data-gtm-grp="HTS Frontpage" data-gtm-id="BottomBannerBtn'+ (i+1) + '">';
					}
					li += '<img src="'+bannerImgUrl+'" alt="'+banner.alt+'"></img>';
					li += '</a></li>';
					$(id).append(li);
					
					bannerCnt++;
				}
				initSwiperMain();
			})
	    	
	    	/*
			var jsonObj = {
				type : 'GET',
				url : url,
				contentType : 'application/json; charset=utf-8',
				success : function(res) {
					for (var i = 0; i < res.length; i++) {
						var banner = res[i];
						var bannerImgUrl = '${pageContext.request.contextPath}/kid/upload/banner/'+banner.imgFileUrl;
						var li = '<li class="swiper-slide">';
						var pcLinkUrl = banner.pcLinkUrl;
						var linkMethod = banner.linkMethod;
						if(pcLinkUrl){
							if(pcLinkUrl.indexOf('/') == 0){
								pcLinkUrl = 'kwtp://7047?goUrl=' + pcLinkUrl;
								linkMethod = "_self";
							}else if(pcLinkUrl.indexOf('kiwoom.co.id') > -1){
								if(pcLinkUrl.indexOf('welcome.kiwoom.co.id') < 0){
									pcLinkUrl = 'kwtp://7047?goUrl=' + pcLinkUrl;
									linkMethod = "_self";
								}
							}
							li += '<a href="'+pcLinkUrl+'" target="'+linkMethod+'" class="banner-01" data-gtm-grp="HTS Frontpage" data-gtm-id="TopBannerBtn'+ (i+1) + '">';	
						}
						li += '<img src="'+bannerImgUrl+'" alt="'+banner.alt+'"></img>';
						li += '</a></li>';
						$(id).append(li);
					}
					initSwiperMain();
				},	
				error : function(e1, e2, e3) {
					// Called only if json file cannnot be read
					var url = '${pageContext.request.contextPath}/hts/frontpage/getTopBanners';
					var jsonObj = {
						type : 'POST',
						url : url,
						contentType : 'application/json; charset=utf-8',
						success : function(res) {
							for (var i = 0; i < res.length; i++) {
								var banner = res[i];
								var bannerImgUrl = '${pageContext.request.contextPath}/kid/upload/banner/'+banner.IMG_URL;
								var li = '<li class="swiper-slide">';
								var pcLinkUrl = banner.PC_LINK_URL;
								var linkMethod = banner.LINK_METHOD;
								if(pcLinkUrl){
									if(pcLinkUrl.indexOf('/') == 0){
										pcLinkUrl = 'kwtp://7047?goUrl=' + pcLinkUrl;
										linkMethod = "_self";
									}else if(pcLinkUrl.indexOf('kiwoom.co.id') > -1){
										if(pcLinkUrl.indexOf('welcome.kiwoom.co.id') < 0){
											pcLinkUrl = 'kwtp://7047?goUrl=' + pcLinkUrl;
											linkMethod = "_self";
										}
									}
									li += '<a href="'+pcLinkUrl+'" target="'+linkMethod+'" class="banner-01" data-gtm-grp="HTS Frontpage" data-gtm-id="TopBannerBtn'+ (i+1) + '">';	
								}
								li += '<img src="'+bannerImgUrl+'" alt="'+banner.IMG_DESC+'"></img>';
								li += '</a></li>';
								$(id).append(li);
							}
							initSwiperMain();
						},	
						error: function(request, status, error) {
							kidCommon.showErrorMessage(request, status, error);
			    		}
					}
					kidCommon.ajax(jsonObj);
				}
			}
			kidCommon.ajax(jsonObj);
			*/
		}
        function initSwiperMain(){
        	var isLoop = bannerCnt > 1 ? true : false;
        	var pagenationCss = "";
        	if(isLoop){
        		pagenationCss = ".swiper-pagination";
        	}
        	var swiper = new Swiper(".swiper-wrap", {
                slidesPerView: 1,
                autoplay:4000,
                autoplayDisableOnInteraction: false,
                speed:1600,
                loop:isLoop,
                pagination: pagenationCss,
                paginationClickable: isLoop,
                touchRatio: 0
            });
        	
        	$('.swiper-pagination-switch').bind('click',function(){
                 setTimeout(function(){
                     swiper.startAutoplay(1000);
                 },200);
            });
        }
    </script>
</body>