<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<section class="sub-header-type01">
    <div class="wrap">
        <h2>
            <img src="${pageContext.request.contextPath}/kid/images/icon/icon_logo.svg" alt="KIWOOM SEKURITAS INDONESIA">
        </h2>
        <div class="customer">
            Customer Service<br>
            (021) 2708 5695~5696
        </div>
    </div>
</section>
<form id="frm" name="frm" onSubmit="return false;">
	<section id="step1" class="sub-contents-type01">
     <div class="contents-bg-area">
         <img src="${pageContext.request.contextPath}/kid/images/common/bg_etc_01.svg" alt="">
     </div>
     <div class="contents-area">
         <div class="title">
             <h1>Find ID</h1>
         </div>
         <dl class="form-list-sub">
             <dt>Email</dt>
             <dd id="dd_email">
                 <span id="span_email" class="inp-txt sub-icon sub-icon-01">
                     <input type="text" id="email" name="email">
                 </span>
             </dd>
             <dt>Mobile Number</dt>
             <dd id="dd_phone_no">
                 <span id="span_phone_no" class="inp-txt sub-icon sub-icon-02">
                 	<input type="number" pattern="\d*" id="phone_no" name="phone_no" style="ime-mode:disabled" onkeypress="onlyNumChk(this)" placeholder="8xxxxxxxx">
                 </span>
                 <span class="err-inp-txt">Please enter your Mobile Number.</span>
             </dd>
             <dt>Mother's Maiden Name</dt>
             <dd id="dd_mother_nm">
                 <span id="span_mother_nm" class="inp-txt sub-icon sub-icon-03">
                     <input type="text" id="mother_nm" name="mother_nm">
                 </span>
                 <span class="err-inp-txt">Please enter your Mother's Maiden Name.</span>
             </dd>
         </dl>
         <div class="forgotPassword">
             <a id="btnForgotPassword" href="#none">Forgot password?</a>
         </div>
	      <div class="sub-contents-btn-area">
	          <button id="btnConfirm" data-gtm-grp="FindId" data-gtm-id="Confirmation" class="btn btn-blue">Confirmation</button>
	      </div>
     </div>
	</section>
</form>	 

<form id="frmMove" name="frmMove">
	<input type="hidden" id="resp_gubn" name="resp_gubn" />
	<input type="hidden" id="resp_mesg" name="resp_mesg" />
	<input type="hidden" id="login_id" name="login_id" />
	<input type="hidden" id="id_yn" name="id_yn" />
</form>	    
<script type="text/javascript">
	
	$(document).ready(function() {
		kidCommon.setDocumentTitle('FIND ID');
		init();
	});
	
	const init = () => {
		initButton();
	}
	
	const initValidate = () => {
		$("#frm").validate({
			onsubmit: true,
			errorElement: "span",
			errorClass: "err-inp-txt",
			errorPlacement: function(error, element){
				error.insertAfter("#span_" + element.attr("name"));
			},
			highlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).addClass('err');
			},
			unhighlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).removeClass('err');
			},
			rules : {
				email: {
					required : true,
					email : true
				},
				phone_no : {
					required : true,
					digits : true
				},
				mother_nm : {
					required : true
				}
			},
			messages : {
				email : {
					required : "Please enter your Email.",
					email : "Please check the email format."
				},
				phone_no : {
					required : "Please enter your Phone number."
				},
				mother_nm : {
					required : "Please enter your Mother's Maiden Name."
				}
			},
			submitHandler: function() {
				find();
			}
		});
	}
	
	const initButton = () => {
		$("#btnConfirm").on('click', function() {
			initValidate();
		});
		
		$("#btnForgotPassword").on('click', function() {
			kidCommon.goForgotPassword();
		});
	}
	
	const onlyNumChk = (obj) => {
		var tgId = "#"+obj.id;
		$(tgId).numeric();
	}
	
	const find = () => {
		const params = {
			langType : "0",	// 0:EN, 1:IN, 2:KR
			email : $('#email').val(),
			phoneNo : $('#phone_no').val(),
			motherNm : $('#mother_nm').val()
		}
			
		const jsonObj = {
			isShowLoading : false,
			method : 'POST',
			url : "/hts/findid/getHtsFindId",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(result) {
				if(result){
					$('#resp_gubn').val(result.resp_gubn);
					$('#resp_mesg').val(result.resp_mesg);
					$('#login_id').val(result.login_id);
					$('#id_yn').val(result.id_yn);
					
					const moveParams = $("#frmMove").serialize();	
					
					$.post("/hts/findid/getHtsFindIdResult", moveParams, function(data) {
						$('html').scrollTop(0);
						$('#step1').addClass('complte');
						$('#frm').after(data);
					});
				}
			},
			error: function(request, status, error) {
				kidCommon.showErrorMessage(request, status, error);
    		}
		}

		kidCommon.ajax(jsonObj);
	}
</script>