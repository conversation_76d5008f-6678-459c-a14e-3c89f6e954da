<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<section id="step2" class="complte-pop find-id block" >
    <div class="wrap">
        <div class="form-center-contents">
            <p class=" bold fs24 blue">
            	<c:choose>
           			<c:when test="${resp_gubn eq '0'}">
           				<c:choose>
		           			<c:when test="${id_yn eq 'Y'}">
		           				Your ID is <span class="pink"><c:out value="${login_id}"/></span>
		           			</c:when>
		           			<c:otherwise>
		           				<c:out value="${resp_mesg}"/>
		           			</c:otherwise>
		           		</c:choose>
           			</c:when>
           			<c:otherwise>
           				<c:out value="${resp_mesg}"/>
           			</c:otherwise>
           		</c:choose>
           	</p>
        </div>
    </div>
    <div class="form-center-btn">
        <button id="btnClose" class="btn btn-blue" data-gtm-grp="FindId" data-gtm-id="Close">Close</button>
    </div>
</section>

<script type="text/javascript">
	$(document).ready(function() {
		kidCommon.setDocumentTitle('Result | Find ID');
		init2();
	});
	
	var init2 = () => {
		initButton2();
	}
	
	var initButton2 = () => {
		var resp_gubn = '${resp_gubn}';
		var id_yn = '${id_yn}';
		$("#btnClose").on('click', function() {
			if(resp_gubn == '0' && id_yn == 'Y'){	// success
				self.close();
			}else {	// fail
				$('#step2').remove();
				$('#step1').removeClass('complte');	
			}
		});
	}
</script>