<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<section id="step2" class="complte-pop">
    <div class="wrap">
        <div class="form-center-contents">
            <p class="txt"><c:out value="${resp_mesg}"/></p>
        </div>
    </div>
    <div class="form-center-btn">
        <button id="btnClose" class="btn btn-blue" data-gtm-grp="TrialId" data-gtm-id="Close">Close</button>
    </div>
</section>
<script type="text/javascript">
	$(document).ready(function() {
		kidCommon.setDocumentTitle('Result | Trial ID | HTS');
		init2();
	});
	
	var init2 = () => {
		initButton2();
	}
	
	var initButton2 = () => {
		var resp_gubn = '${resp_gubn}';
		$("#btnClose").on('click', function() {
			if(resp_gubn == '0'){	// success
				self.close();
			}else {	// fail
				$('#step2').remove();
				$('#step1').removeClass('complte');	
			}
		});
	}
</script>