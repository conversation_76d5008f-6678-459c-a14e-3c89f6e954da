<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<header id="header-hts">
    <h1>Request Trial ID</h1>
    <div class="icon-cs">
        Customer Service :<br> (021) 2708 5695~5696
    </div>
</header>
<div id="wrap">
	<section id="step1" class="hts-contents">
		<form id="frm" name="frm" onSubmit="return false;">
		    <div class="wrap">
		        <dl class="form-list">
		            <dt>Name</dt>
		            <dd id="dd_name">
		                <span id="span_name" class="inp-txt">
		                    <input type="text" id="name" name="name">
		                </span>
		            </dd>
		            <dt>Email</dt>
		            <dd id="dd_email">
		                <span id="span_email" class="inp-txt">
		                    <input type="text" id="email" name="email">
		                </span>
		            </dd>
		            <dt>Cellular Phone Number</dt>
		            <dd id="dd_hp_no">
		                <span id="span_hp_no" class="inp-txt">
		                    <input type="text" id="hp_no" name="hp_no" style="ime-mode:disabled" onkeypress="onlyNumChk(this)">
		                </span>
		            </dd>
		        </dl>
		        <ul class="list-type-02">
		            <li>Please input your correct email address and cellular phone number.</li>
		            <li>We will send trial ID & PW to your cellular phone number by SMS.</li>
		            <li>Please wait for a few minutes.</li>
		        </ul>
		    </div>
		    <div class="wrap">
		        <div class="form-center-contents">
		            <p class="txt">I would like to receive event, promotion information via Email or SMS.</p>
		            <div class="inp-area">
		                <span class="inp-radio">
		                    <input type="radio" id="sms_agree_y" name="sms_agree_yn" value="Y" checked>
		                    <label for="sms_agree_y">Agree</label>
		                </span>
		                <span class="inp-radio">
		                    <input type="radio" id="sms_agree_n" name="sms_agree_yn" value="N">
		                    <label for="sms_agree_n">Disagree</label>
		                </span>
		            </div>
		        </div>
		        <ul class="list-type-02">
		            <li>Trial ID can be used only for two weeks.</li>
		            <li>Registration using same email address or cellular phone number can only be used twice.</li>
		        </ul>
		    </div>
		    <div class="form-center-btn">
		        <button id="btnCreate" class="btn btn-blue" data-gtm-grp="TrialId" data-gtm-id="Create">Create</button>
		        <button id="btnCancel" class="btn btn-blue-lighter" data-gtm-grp="TrialId" data-gtm-id="Cancel">Cancel</button>
		    </div>
		</form>
		<form id="frmMove" name="frmMove">
			<input type="hidden" id="resp_gubn" name="resp_gubn" />
			<input type="hidden" id="resp_mesg" name="resp_mesg" />
		</form>
	</section>
</div>
<script type="text/javascript">
	
	$(document).ready(function() {
		kidCommon.setDocumentTitle('Trial ID | HTS');
		init();
	});
	
	const init = () => {
		initButton();
	}
	
	const initValidate = () => {
		$("#frm").validate({
			onsubmit: true,
			errorElement: "span",
			errorClass: "err-inp-txt",
			errorPlacement: function(error, element){
				error.insertAfter("#span_" + element.attr("name"));
			},
			highlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).addClass('err');
			},
			unhighlight: function(element, errorClass){
				$("#dd_" + $(element).attr("name")).removeClass('err');
			},
			rules : {
				name : {
					required : true
				},
				email: {
					required : true,
					email : true
				},
				hp_no : {
					required : true
				}
			},
			messages : {
				name : {
					required : "Please enter your name."
				},
				email : {
					required : "Please enter your email.",
					email : "Please check the email format."
				},
				hp_no : {
					required : "Please enter your cellular phone number."
				}
			},
			submitHandler: function() {
				register();
			}
		});
	}
	
	const initButton = () => {
		$("#btnCreate").on('click', function() {
			initValidate();
		});
		
		$("#btnCancel").on('click', function() {
			self.close();
		});
	}
	
	const onlyNumChk = (obj) => {
		var tgId = "#"+obj.id;
		$(tgId).numeric();
	}
	
	const register = () => {
		const params = {
			langType : "0",	// 0:EN, 1:IN, 2:KR
			name : $('#name').val(),
			email : $('#email').val(),
			hpNo : $('#hp_no').val(),
			smsAgreeYn: $("input[name='sms_agree_yn']:checked").val()
		}
			
		const jsonObj = {
			isShowLoading : false,
			method : 'POST',
			url : "/hts/trial/regHtsTrial",
			contentType : 'application/json; charset=utf-8',
			cache : false,
			data : JSON.stringify(params),
			async : true,
			success : function(result) {
				if(result){
					$('#resp_gubn').val(result.resp_gubn);
					$('#resp_mesg').val(result.resp_mesg);
					
					const moveParams = $("#frmMove").serialize();	
					
					$.post("/hts/trial/getHtsTrialResult", moveParams, function(data) {
						$('html').scrollTop(0);
						$('#step1').addClass('complte');
						$('#step1').after(data);
					});
				}
			},
			error: function(request, status, error) {
				kidCommon.showErrorMessage(request, status, error);
    		}
		}

		kidCommon.ajax(jsonObj);
	}
</script>