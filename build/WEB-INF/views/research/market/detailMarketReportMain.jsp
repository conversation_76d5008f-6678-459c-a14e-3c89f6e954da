<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<script type="text/javascript">
	$(document).ready(function() {
		init();
	});

	function init() {
		initPdf();
		initButton();
		initInput();
	}
	function initInput() {

	}
	function initButton() {

	}
	function initPdf() {
		const fileUrl = `${pageContext.request.contextPath}/kid/upload/mr/${marketReport.SNMAKEDATE}/${marketReport.APNDNM}`;
		const fileName = `${marketReport.FILENM}`;
		var options = {
			pdfOpenParams : {
				navpanes : 1,
				toolbar : 0,
				statusbar : 0,
			},
			forcePDFJS : true,
			PDFJS_URL : "/pdf/pdfviewer"
		};

		var myPDF = PDFObject.embed(fileUrl, "#viewer-pdf", options);
		$("#btnDownload").attr("href", fileUrl);
		$("#btnDownload").attr("download", fileName);
	}
</script>
<main>
	<!-- S:CONTENTS AREA -->
	<div class="contents-wrap">
		<div class="title-report">
			<div class="left">
				<h1>
					${marketReport.TITL}
				</h1>
				<br>
				<span><c:out value="${marketReport.MAKEDATE}"/></span>
			</div>
			<div class="right">
				<a href="${pageContext.request.contextPath}/kid/upload/mr/${marketReport.SNMAKEDATE}/${marketReport.APNDNM}" download="${marketReport.FILENM}">Download</a>
				<span>PDF</span>
			</div>
		</div>
		<div class="viewer-pdf">
			<div id="viewer-pdf" class="pdf-viewer-height">
			</div>
		</div>
		<div class="list-navi">
			<c:set var="showPreviousButton" value="${empty marketReportPrevious ? 'display:none' : ''}" />
			<c:set var="showNextButton" value="${empty marketReportNext ? 'display:none' : ''}" />
			<a href="${pageContext.request.contextPath}/market/detailMarketReportMain?id=${marketReportPrevious.SEQNO}" class="btn-next" style="${showPreviousButton}">
				<span class="max-lines-detail"><c:out value="${marketReportPrevious.TITL}"/></span>Previous
			</a>
			<a href="${pageContext.request.contextPath}/market/detailMarketReportMain?id=${marketReportNext.SEQNO}" class="btn-prev" style="${showNextButton}">
				Next<span class="max-lines"><c:out value="${marketReportNext.TITL}"/></span>
			</a>
		</div>
		<div class="list-view-btn">
			<a href="${pageContext.request.contextPath}/market/getMarketReportMain" class="btn btn-blue-mid al-c">List view</a>
		</div>
	</div>
	<!-- E:CONTENTS AREA -->
</main>
