<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<script type="text/javascript">
	let tblSearch;
	let currentOffset = 8;
	let searching = false;
	let searchCriteriaMap = {
		"0" : "all",
		"1" : "titl",
		"2" : "cont"
	};
	$(document).ready(function() {
		init();
	});

	function init() {
		initButton();
		initInput();
	}
	function initInput() {
		$("#txtFromDate").datepicker(
				{
					dateFormat : 'dd/mm/yy',
					maxDate : 0,
					onClose : function(selectedDate) {
						if(selectedDate == ""){
							return;
						}
						var currentDate = new Date();
						var selectedDateTime = $.datepicker.parseDate(
								'dd/mm/yy', selectedDate).setHours(0, 0, 0, 0);
						var currentDateTime = currentDate.setHours(0, 0, 0, 0);

						if (selectedDateTime > currentDateTime) {
							$("#txtFromDate")
									.datepicker("setDate", currentDate);
						}
						$("#txtToDate").datepicker("option", "minDate",
								selectedDate);
						validateDateRange();
					},
					beforeShowDay : function(date) {
						var today = new Date();
						return [ date <= today ];
					},
					//Event is used to execute the positioning logic before showing the datepicker popup. Setting datepicker popup always show below the input text
					beforeShow: function(input, inst) {
						var calendar = inst.dpDiv;
					    setTimeout(function() {
					      calendar.css("z-index", 9999); // Set the desired z-index value
					    }, 1);
					}
				});

		$("#txtToDate").datepicker(
				{
					dateFormat : 'dd/mm/yy',
					maxDate : 0,
					onClose : function(selectedDate) {
						if(selectedDate == ""){
							return;
						}
						var currentDate = new Date();
						var selectedDateTime = $.datepicker.parseDate(
								'dd/mm/yy', selectedDate).setHours(0, 0, 0, 0);
						var currentDateTime = currentDate.setHours(0, 0, 0, 0);

						if (selectedDateTime > currentDateTime) {
							$("#txtToDate").datepicker("setDate", currentDate);
						}
						$("#txtFromDate").datepicker("option", "maxDate",
								selectedDate);
						validateDateRange();
					},
					beforeShowDay : function(date) {
						var today = new Date();
						return [ date <= today ];
					},
					//Event is used to execute the positioning logic before showing the datepicker popup. Setting datepicker popup always show below the input text
					beforeShow: function(input, inst) {
						var calendar = inst.dpDiv;
					    setTimeout(function() {
					      calendar.css("z-index", 9999); // Set the desired z-index value
					    }, 1);
					}
				});
		$('#txtFromDate,#txtToDate').mask('00/00/0000');
		$('#searchTerms').keydown(function(event) {
			if (event.keyCode === 13) {
				if ($('#formSearch').valid()) {
					onSearch(0, 8, false);
				}
			}
		});
		$('#searchTerms').on('blur', function() {
			if (searching === false) {
				onSearch(0, 8, false);
			}
		})
	}

	function validateDateRange() {
		const startDateInput = $("#txtFromDate");
		const endDateInput = $("#txtToDate");

		const startDateValue = startDateInput.val().trim();
		const endDateValue = endDateInput.val().trim();

		const startDate = startDateValue ? parseDate(startDateValue) : null;
		const endDate = endDateValue ? parseDate(endDateValue) : null;

		// Requirement 1: Start date should be before end date
		if (startDate && endDate && startDate > endDate) {
			endDateInput.val(startDateInput.val());
		}

		// Requirement 2: If end date is selected and start date is empty, do nothing
		/*
		if (!startDateValue && endDateValue) {
			startDateInput.val(endDateValue);
		}
		*/
		// Requirement 3: If start date is selected and end date is empty, do nothing
		/*
		if (startDateValue && !endDateValue) {
			endDateInput.val(startDateValue);
		}
		*/
		// Requirement 4: If start date is invalid and end date is valid, set start date = end date
		/*
		if ((!startDate || !startDate.isValid()) && endDate
				&& endDate.isValid()) {
			startDateInput.val(endDateInput.val());
		}
		*/
		// Requirement 5: If end date is invalid and start date is valid, set end date = start date
		/*
		if ((!endDate || !endDate.isValid()) && startDate
				&& startDate.isValid()) {
			endDateInput.val(startDateInput.val());
		}
		*/
		onSearch(0, 8, false);
	}
	function parseDate(dateString) {
		return moment(dateString, "DD/MM/YYYY", true);
	}
	function initButton() {
		$("#btnViewMore").on('click', function() {
			if ($('#formSearch').valid()) {
				onSearch(currentOffset, 4, true);
			}
		});
	}
	function onSearch(offset, limit, isViewMore) {
		if ($("#formSearch").valid()) {
			searching = true;
			var jsonObj = {
				isShowLoading : false,
				method : 'POST',
				url : "/iponews/getIpoNewsList",
				contentType : 'application/json; charset=utf-8',
				cache : false,
				data : JSON.stringify({
					fromDate : $("#txtFromDate").val(),
					toDate : $("#txtToDate").val(),
					searchCriteria : searchCriteriaMap[$("#searchCriteria")
							.attr("data-value")],
					searchTerms : $("#searchTerms").val(),
					limit : limit,
					offset : offset
				}),
				async : true,
				success : function(list) {
					if (isViewMore === false) {
						if($("#divNoData")){
							$("#divNoData").remove();	
						}
						$("#listIpoNews").empty();
					}
					if(list.data && list.data.length > 0){
						for (let i = 0; i < list.data.length; i++) {
							const ipoNews = list.data[i];
							let li = "<li><div class='top'>";
							li += '<a href="/iponews/detailIpoNewsMain?id=' + ipoNews.SEQNO + '">' + ipoNews.MAKEDATE + "</a>";
							if (ipoNews.IS_NEW == 1) {
								li += '<em>NEW</em>';
							}
							li += '</div>';
							li += '<div class="mid">';
							li += '<a href="/iponews/detailIpoNewsMain?id='
									+ ipoNews.SEQNO + '">';
							
							const titl = kidCommon.unescape(ipoNews.TITL);
							const arrTitl = titl.split(" ");
							const arrTitlLen = arrTitl.length;
							let repTitl = "";
							
							for(let index=0; index < arrTitlLen; index++){
								let item = arrTitl[index];
								if(item.length > 15){
									repTitl += item.substring(0,15) + "<br/>" + item.substring(15);
								}else {
									repTitl += item;
								}
								if(!(index == (arrTitlLen)-1)){
									repTitl += " ";
								}
							}
							
							li += '<p>' + repTitl + '</p>';
							li += '</a>';
							li += '</div>';
							li += '<div class="bottom">';
							li += '<a href="/iponews/detailIpoNewsMain?id=' + ipoNews.SEQNO + '"> No. <i>' + ipoNews.SEQNO + '</i></a>';
							li += '</div>';
							li += '</li>';
							$("#listIpoNews").append(li);
						}
					}else {
						let div = '<div id="divNoData" class="nodata-area">';
						div += '	<h3>No data to display</h3>';
						div += '	<div>';
						div += '		<img src="${pageContext.request.contextPath}/kid/images/common/bg_no_data.png" alt="No Data">';
						div += '	</div>';
						div += '</div>';
						$("#listIpoNews").before(div);
					}
					currentOffset = offset+ limit;
					searching = false;
					if(list.canFetchData == true){
						$("#btnViewMore").attr("style","");
					}else{
						$("#btnViewMore").attr("style","display:none;");
					}
				},
				error: function(request, status, error) {
					searching = false;
					kidCommon.showErrorMessage(request, status, error);
	    		}
			}
			kidCommon.ajax(jsonObj);
		}
	}
</script>
<main>
	<div class="grand-banner type01">
		<h2>IPO NEWS</h2>
	</div>
	<!-- S:CONTENTS AREA -->
	<div class="contents-wrap">
		<!-- S:SEARCH AREA -->
		<form id="formSearch" action="">
			<div class="search-bar">
				<div class="search-date">
					<div class="search-data-half">
						<span class="txt">From</span>
						<label class="datepicker">
							<input type="text" id="txtFromDate" name="fromDate" placeholder="dd/mm/yyyy" inputmode="none" autocomplete="off">
						</label>
					</div>
					<div class="search-data-half">
						<span class="txt">To</span>
						<label class="datepicker">
							<input type="text" id="txtToDate" name="toDate" placeholder="dd/mm/yyyy" inputmode="none" autocomplete="off">
						</label>
					</div>
				</div>
				<div class="search-info">
					<div class="search-select select-item" id="searchCriteria" data-value="0">
						<button class="target" type="button" name="searchCriteria">All</button>
						<ul>
							<li>
								<button type="button">All</button>
							</li>
							<li>
								<button type="button">Subject</button>
							</li>
							<li>
								<button type="button">Content</button>
							</li>
						</ul>
					</div>
					<div class="search-box">
						<span class="search-inp"> <label for="search">
								<input id="searchTerms" type="text" placeholder="Search" name="searchTerms">
							</label>
						</span>
					</div>
				</div>
			</div>
		</form>
		<!-- E:SEARCH AREA -->
		<!-- S:LIST -->
		<div class="report-list">
			<c:choose>
				<c:when test="${ipoNewsList.data != null && fn:length(ipoNewsList.data) > 0}">
					<ul id="listIpoNews">
						<c:forEach items="${ipoNewsList.data}" var="ipoNews">
							<li>
								<div class="top">
									<a href="/iponews/detailIpoNewsMain?id=${ipoNews.SEQNO}"><c:out value="${ipoNews.MAKEDATE}"/></a>
									<c:if test="${ipoNews.IS_NEW == 1}">
										<em>NEW</em>
									</c:if>
								</div>
								<div class="mid">
									<a href="/iponews/detailIpoNewsMain?id=${ipoNews.SEQNO}">
										<p>${ipoNews.TITL}</p>
									</a>
								</div>
								<div class="bottom">
									<a href="/iponews/detailIpoNewsMain?id=${ipoNews.SEQNO}">No. <i><c:out value="${ipoNews.SEQNO}"/></i></a>
								</div>
							</li>
						</c:forEach>
					</ul>
				</c:when>
				<c:otherwise>
					<div id="divNoData" class="nodata-area">
	                    <h3>No data to display</h3>
	                    <div>
	                        <img src="${pageContext.request.contextPath}/kid/images/common/bg_no_data.png" alt="No Data">
	                    </div>
	                </div>
				</c:otherwise>
			</c:choose>
		</div>
		<!-- E:LIST -->
		<!-- S:MORE -->
		<c:set var="canFetchData" value="${ipoNewsList.canFetchData == true ? '' : 'display: none' }" />
		<div class="btn-viewmore" style="${canFetchData}">
			<button type="button" id="btnViewMore">View more</button>
		</div>
		<!-- E:MORE -->
	</div>
	<!-- E:CONTENTS AREA -->
</main>
