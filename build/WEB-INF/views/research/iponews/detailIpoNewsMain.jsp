<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ page import="java.util.HashMap"%>
<%@ page import="org.apache.commons.lang.StringEscapeUtils"%>
<%@ page import="com.kiwoom.common.utils.StringUtil"%>
<%
	// To unescape the escaped data
	HashMap<String, Object> ipoNews = (HashMap<String, Object>)request.getAttribute("ipoNews");
	String cont = StringUtil.null2str((String)ipoNews.get("CONT"), "");
	ipoNews.put("CONT", StringEscapeUtils.unescapeHtml(cont));	
%>
<script type="text/javascript">
	$(document).ready(function() {
		init();
	});

	function init() {
		initButton();
		initInput();
	}
	function initInput() {

	}
	function initButton() {

	}
</script>
<main>
	<!-- S:CONTENTS AREA -->
	<div class="contents-wrap">
		<div class="round-view">
			<div class="title-news">
				<h1>
					${ipoNews.TITL}
				</h1>
				<div class="date">
                    <span><c:out value="${ipoNews.MAKEDATE}"/></span>
                    <em>No. <c:out value="${ipoNews.SEQNO}"/></em>
                </div>
			</div>
			<dl class="round-content"> 
				<dd>
					<!-- Open Date : 20240113 -->
					<c:choose>
						<c:when test="${ipoNews.SNMAKEDATE2 >= 20240113}">
							<p>${ipoNews.CONT}</p>		
						</c:when>
						<c:otherwise>
							<p><pre style="white-space: pre-wrap;">${ipoNews.CONT}</pre></p>
						</c:otherwise>
					</c:choose>
				</dd>
			</dl>
		</div>
		<div class="list-navi">
			<c:set var="showPreviousButton" value="${empty ipoNewsPrevious ? 'display:none' : ''}" />
			<c:set var="showNextButton" value="${empty ipoNewsNext ? 'display:none' : ''}" />
			<a href="${pageContext.request.contextPath}/iponews/detailIpoNewsMain?id=${ipoNewsPrevious.SEQNO}" class="btn-next" style="${showPreviousButton}">
				<span class="max-lines-detail"><c:out value="${ipoNewsPrevious.TITL}"/></span>Previous
			</a>
			<a href="${pageContext.request.contextPath}/iponews/detailIpoNewsMain?id=${ipoNewsNext.SEQNO}" class="btn-prev" style="${showNextButton}">
				Next<span class="max-lines"><c:out value="${ipoNewsNext.TITL}"/></span>
			</a>
		</div>
		<div class="list-view-btn">
			<a href="${pageContext.request.contextPath}/iponews/getIpoNewsMain" class="btn btn-blue-mid al-c">List view</a>
		</div>
	</div>
	<!-- E:CONTENTS AREA -->
</main>
