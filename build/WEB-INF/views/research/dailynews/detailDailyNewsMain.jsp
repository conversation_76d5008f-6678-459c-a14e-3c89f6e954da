<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ page import="java.util.HashMap"%>
<%@ page import="org.apache.commons.lang.StringEscapeUtils"%>
<%@ page import="com.kiwoom.common.utils.StringUtil"%>
<%
	// To unescape the escaped data
	HashMap<String, Object> dailyNews = (HashMap<String, Object>)request.getAttribute("dailyNews");
	String cont = StringUtil.null2str((String)dailyNews.get("CONT"), "");
	String cont_1 = StringUtil.null2str((String)dailyNews.get("CONT_1"), "");
	String cont_2 = StringUtil.null2str((String)dailyNews.get("CONT_2"), "");
	String cont_3 = StringUtil.null2str((String)dailyNews.get("CONT_3"), "");
	String cont_4 = StringUtil.null2str((String)dailyNews.get("CONT_4"), "");
	String cont_5 = StringUtil.null2str((String)dailyNews.get("CONT_5"), "");

	dailyNews.put("CONT", StringEscapeUtils.unescapeHtml(cont));
	dailyNews.put("CONT_1", StringEscapeUtils.unescapeHtml(cont_1));
	dailyNews.put("CONT_2", StringEscapeUtils.unescapeHtml(cont_2));
	dailyNews.put("CONT_3", StringEscapeUtils.unescapeHtml(cont_3));
	dailyNews.put("CONT_4", StringEscapeUtils.unescapeHtml(cont_4));
	dailyNews.put("CONT_5", StringEscapeUtils.unescapeHtml(cont_5));
%>
<script type="text/javascript">
	$(document).ready(function() {
		init();
	});

	function init() {
		initButton();
		initInput();
	}
	function initInput() {

	}
	function initButton() {

	}
</script>
<main>
	<!-- S:CONTENTS AREA -->
	<div class="contents-wrap">
		<div class="round-view">
			<div class="title-news ">
				<h1>
					${dailyNews.TITL}
				</h1>
				<div class="date">
					<span><c:out value="${dailyNews.MAKEDATE}"/></span> <em>No. <c:out value="${dailyNews.SEQNO}"/></em>
				</div>
			</div>
			<dl class="round-content">
				<c:choose>
					<c:when test="${empty dailyNews.CONT}">
						<c:if test="${not empty dailyNews.STOCK_CODE_1}">
							<dt>
								<div class="header-area">
									<p class="logo">
										<c:if test="${empty dailyNews.STOCK_IMG_1}">
											<img src="${pageContext.request.contextPath}/kid/images/temp/logo_default.svg" alt="LOGO">
										</c:if>
										<c:if test="${not empty dailyNews.STOCK_IMG_1}">
											<img src="${pageContext.request.contextPath}/kid/upload/stockimg/${dailyNews.STOCK_IMG_1}" alt="LOGO">
										</c:if>
									</p>
									<h2><c:out value="${dailyNews.STOCK_CODE_1}"/></h2>
									<p class="sub-tit"><c:out value="${dailyNews.STOCK_DESC_1}"/></p>
								</div>
							</dt>
						</c:if>
						<c:if test="${not empty dailyNews.CONT_1}">
							<dd>${dailyNews.CONT_1}</dd>
						</c:if>
						<c:if test="${not empty dailyNews.STOCK_CODE_2}">
							<dt>
								<div class="header-area">
									<p class="logo">
										<c:if test="${empty dailyNews.STOCK_IMG_2}">
											<img src="${pageContext.request.contextPath}/kid/images/temp/logo_default.svg" alt="LOGO">
										</c:if>
										<c:if test="${not empty dailyNews.STOCK_IMG_2}">
											<img src="${pageContext.request.contextPath}/kid/upload/stockimg/${dailyNews.STOCK_IMG_2}" alt="LOGO">
										</c:if>
									</p>
									<h2><c:out value="${dailyNews.STOCK_CODE_2}"/></h2>
									<p class="sub-tit"><c:out value="${dailyNews.STOCK_DESC_2}"/></p>
								</div>
							</dt>
						</c:if>
						<c:if test="${not empty dailyNews.CONT_2}">
							<dd>${dailyNews.CONT_2}</dd>
						</c:if>
						<c:if test="${not empty dailyNews.STOCK_CODE_3}">
							<dt>
								<div class="header-area">
									<p class="logo">
										<c:if test="${empty dailyNews.STOCK_IMG_3}">
											<img src="${pageContext.request.contextPath}/kid/images/temp/logo_default.svg" alt="LOGO">
										</c:if>
										<c:if test="${not empty dailyNews.STOCK_IMG_3}">
											<img src="${pageContext.request.contextPath}/kid/upload/stockimg/${dailyNews.STOCK_IMG_3}" alt="LOGO">
										</c:if>
									</p>
									<h2><c:out value="${dailyNews.STOCK_CODE_3}"/></h2>
									<p class="sub-tit"><c:out value="${dailyNews.STOCK_DESC_3}"/></p>
								</div>
							</dt>
						</c:if>
						<c:if test="${not empty dailyNews.CONT_3}">
							<dd>${dailyNews.CONT_3}</dd>
						</c:if>
						<c:if test="${not empty dailyNews.STOCK_CODE_4}">
							<dt>
								<div class="header-area">
									<p class="logo">
										<c:if test="${empty dailyNews.STOCK_IMG_4}">
											<img src="${pageContext.request.contextPath}/kid/images/temp/logo_default.svg" alt="LOGO">
										</c:if>
										<c:if test="${not empty dailyNews.STOCK_IMG_4}">
											<img src="${pageContext.request.contextPath}/kid/upload/stockimg/${dailyNews.STOCK_IMG_4}" alt="LOGO">
										</c:if>
									</p>
									<h2><c:out value="${dailyNews.STOCK_CODE_4}"/></h2>
									<p class="sub-tit"><c:out value="${dailyNews.STOCK_DESC_4}"/></p>
								</div>
							</dt>
						</c:if>
						<c:if test="${not empty dailyNews.CONT_4}">
							<dd>${dailyNews.CONT_4}</dd>
						</c:if>
						<c:if test="${not empty dailyNews.STOCK_CODE_5}">
							<dt>
								<div class="header-area">
									<p class="logo">
										<c:if test="${empty dailyNews.STOCK_IMG_5}">
											<img src="${pageContext.request.contextPath}/kid/images/temp/logo_default.svg" alt="LOGO">
										</c:if>
										<c:if test="${not empty dailyNews.STOCK_IMG_5}">
											<img src="${pageContext.request.contextPath}/kid/upload/stockimg/${dailyNews.STOCK_IMG_5}" alt="LOGO">
										</c:if>
									</p>
									<h2><c:out value="${dailyNews.STOCK_CODE_5}"/></h2>
									<p class="sub-tit">${dailyNews.STOCK_DESC_5}</p>
								</div>
							</dt>
						</c:if>
						<c:if test="${not empty dailyNews.CONT_5}">
							<dd>${dailyNews.CONT_5}</dd>
						</c:if>
					</c:when>
					<c:otherwise>
						<dd><pre style="white-space: pre-wrap;">${dailyNews.CONT}</pre></dd>
					</c:otherwise>
				</c:choose>
			</dl>
		</div>
		<div class="list-navi">
			<c:set var="showPreviousButton" value="${empty dailyNewsPrevious ? 'display:none' : ''}" />
			<c:set var="showNextButton" value="${empty dailyNewsNext ? 'display:none' : ''}" />
			<a href="${pageContext.request.contextPath}/dailynews/detailDailyNewsMain?id=${dailyNewsPrevious.SEQNO}" class="btn-next" style="${showPreviousButton}">
				<span class="max-lines-detail"><c:out value="${dailyNewsPrevious.TITL}"/></span>Previous
			</a>
			<a href="${pageContext.request.contextPath}/dailynews/detailDailyNewsMain?id=${dailyNewsNext.SEQNO}" class="btn-prev" style="${showNextButton}">
				Next<span class="max-lines"><c:out value="${dailyNewsNext.TITL}"/></span>
			</a>
		</div>
		<div class="list-view-btn">
			<a href="${pageContext.request.contextPath}/dailynews/getDailyNewsMain" class="btn btn-blue-mid al-c">List view</a>
		</div>
	</div>
	<!-- E:CONTENTS AREA -->
</main>
