<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ page import="java.util.HashMap"%>
<%@ page import="org.apache.commons.lang.StringEscapeUtils"%>
<%@ page import="com.kiwoom.common.utils.StringUtil"%>
<%
	// To unescape the escaped data
	HashMap<String, Object> internationalNews = (HashMap<String, Object>)request.getAttribute("internationalNews");
	String cont = StringUtil.null2str((String)internationalNews.get("CONT"), "");
	internationalNews.put("CONT", StringEscapeUtils.unescapeHtml(cont));
%>
<script type="text/javascript">

$(document).ready(function() {

})

</script>
<main>
	<!-- S:CONTENTS AREA -->
        <div class="contents-wrap">

            <div class="round-view">
                <div class="title-news ">
                    <h1>
                    	${internationalNews.TITL}
                    </h1>
                    <div class="date">
                        <span><c:out value="${internationalNews.MAKEDATE}"/></span>
                        <em>No. <c:out value="${internationalNews.SEQNO}"/></em>
                    </div>
                </div>
                <dl class="round-content">
                    <dd>
                        <p>
                            ${internationalNews.CONT}
                        </p>
                    </dd>
                </dl>
            </div>

            <div class="list-navi">
                <c:set var="showPreviousButton" value="${empty internationalNewsPrevious ? 'display:none' : ''}" />
				<c:set var="showNextButton" value="${empty internationalNewsNext ? 'display:none' : ''}" />
				<a href="${pageContext.request.contextPath}/internationalnews/detailInternationalNewsMain?id=${internationalNewsPrevious.SEQNO}" class="btn-next" style="${showPreviousButton}">
					<span class="max-lines-detail">${internationalNewsPrevious.TITL}</span>Previous
				</a>
				<a href="${pageContext.request.contextPath}/internationalnews/detailInternationalNewsMain?id=${internationalNewsNext.SEQNO}" class="btn-prev" style="${showNextButton}">
					Next<span class="max-lines">${internationalNewsNext.TITL}</span>
				</a>
            </div>
            <div class="list-view-btn">
            	<a href="${pageContext.request.contextPath}/internationalnews/getInternationalNewsMain" class="btn btn-blue-mid al-c">List view</a>
            </div>
        </div>
        <!-- E:CONTENTS AREA -->
</main>

<style>
    main ol, main ul {
        padding-left: 1.5rem !important;
    }
    main ol li {
        list-style: decimal;
    }

    main ul li {
        list-style: disc;
    }
</style>
