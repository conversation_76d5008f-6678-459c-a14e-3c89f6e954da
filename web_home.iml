<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="EclipseModuleManager">
    <conelement value="org.eclipse.jst.j2ee.internal.web.container" />
    <conelement value="org.eclipse.jst.j2ee.internal.module.container" />
    <conelement value="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v8.5" />
    <src_description expected_position="0">
      <src_folder value="file://$MODULE_DIR$/src/main/java" expected_position="0" />
      <src_folder value="file://$MODULE_DIR$/src/main/resources" expected_position="1" />
    </src_description>
  </component>
  <component name="NewModuleRootManager">
    <output url="file://$MODULE_DIR$/WebContent/WEB-INF/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" isTestSource="false" />
    </content>
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="inheritedJdk" />
    <orderEntry type="library" exported="" name="org.eclipse.jst.j2ee.internal.web.container" level="application" />
    <orderEntry type="library" exported="" name="org.eclipse.jst.j2ee.internal.module.container" level="application" />
    <orderEntry type="library" exported="" name="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/Apache Tomcat v8.5" level="application" />
    <orderEntry type="library" name="lib" level="project" />
  </component>
</module>