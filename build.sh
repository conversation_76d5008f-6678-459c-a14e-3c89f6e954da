#!/bin/bash

# Set environment variables
export WORKSPACE="/Users/<USER>/Desktop/DAOU/Side Project/indo-web/indo-web-dev"

# Check if ant is installed
if ! command -v ant &> /dev/null; then
    echo "Ant is not installed. Installing via Homebrew..."
    if command -v brew &> /dev/null; then
        brew install ant
    else
        echo "Homebrew is not installed. Please install Ant manually."
        echo "Visit: https://ant.apache.org/bindownload.cgi"
        exit 1
    fi
fi

# Run the build
echo "Building project with WORKSPACE=$WORKSPACE"
ant -f build-dev.xml compile

echo "Build completed!"
